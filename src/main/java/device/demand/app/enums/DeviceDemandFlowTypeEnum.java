package device.demand.app.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum DeviceDemandFlowTypeEnum {

    // 业务说 Q看成Q单了，换成X
    Q("X", "需求预测"),
    T("T", "需求转移");

    private final String code;
    private final String value;

    private static final Map<String, DeviceDemandFlowTypeEnum> CODE_MAP = new HashMap<>();

    static {
        for (DeviceDemandFlowTypeEnum status : values()) {
            CODE_MAP.put(status.getCode(), status);
        }
    }

    public static DeviceDemandFlowTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        return CODE_MAP.get(code);
    }

}
