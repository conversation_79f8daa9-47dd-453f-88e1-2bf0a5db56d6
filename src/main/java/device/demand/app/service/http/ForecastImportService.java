package device.demand.app.service.http;

import cs.easily.tp.annotation.TpApi;
import cs.easily.tp.annotation.TpBody;
import cs.easily.tp.annotation.TpClient;
import device.demand.app.dto.http.BatchImportDetailDataRequest;
import device.demand.app.dto.http.BatchImportResponse;
import device.demand.app.dto.http.CreatePlanItemRequest;
import device.demand.app.service.http.header.DevCookieService;
import device.demand.app.service.http.log.SimpleLogService;

@TpClient(baseUrl = "${url.device-demand-forecast}", prefix = "/device-demand-forecast/api",
        desc = "物理机预测版本与实时预测进行交互", apikey = true, apiKeyValue = "no",
        readTimeoutSeconds = 60 * 5, connectTimeoutSeconds = 60 * 5,
        logServiceClass = SimpleLogService.class, headerConfigClass = DevCookieService.class)
public interface ForecastImportService {


    @TpApi(path = "genForecastItem", desc = "通过业务维度生成空的预测明细，获取预测id")
    public CreatePlanItemRequest genForecastItem(@TpBody CreatePlanItemRequest request);

    @TpApi(path = "batchImportDetail", desc = "物理机需求预测生效，根据明细导入未执行量")
    public BatchImportResponse batchImportDetail(@TpBody BatchImportDetailDataRequest request);

}
