package cloud.demand.app.modules.common.service;

import cloud.demand.app.entity.plan.StaticGinstypeDO;
import cloud.demand.app.entity.plan.StaticStockPrincipalHosttypeDO;
import cloud.demand.app.entity.plan.StaticZoneDO;
import cloud.demand.app.entity.resource.BasCmdbCityDO;
import cloud.demand.app.entity.resource.BasStrategyDeviceVersionNettypeDO;
import cloud.demand.app.entity.resource.ServerPartsCompositionVO;
import cloud.demand.app.entity.rrp.ReportConfigGpuTypeDO;
import cloud.demand.app.entity.yunti.CloudDemandCsigDeviceExtendInfoDO;
import cloud.demand.app.entity.yunti.YuntiStategyZoneDO;
import cloud.demand.app.modules.common.model.dto.ErpRegionInfoDTO;
import cloud.demand.app.modules.common.model.dto.TxyRegionInfoDTO;
import cloud.demand.app.modules.common.model.req.QueryIndustryWarZoneReq;
import cloud.demand.app.modules.common.model.req.UpdateBizTimeConfigReq;
import cloud.demand.app.modules.common.model.req.UserPersonalConfigReq;
import cloud.demand.app.modules.common.model.rsp.UserPersonalConfigResp;
import cloud.demand.app.modules.common.service.entity.BasObsCloudCvmTypeDO;
import cloud.demand.app.modules.common.service.entity.ObsBizInfo;
import cloud.demand.app.modules.common.service.entity.ResPlanHolidayWeekDO;
import cloud.demand.app.modules.erp_transfer_return.model.ProductInfoDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.CustomerOnlyDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.IndustryDemandIndustryWarZoneDictDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.UinNameDTO;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandIndustryWarZoneDictDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.CrpBizTimeConfigDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.CrpCommonHolidayWeekDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PublicIndustryInstanceModelEnumDO;
import cloud.demand.app.modules.p2p.product_demand.entity.ServerPartsExtendedInfoDO;
import cloud.demand.app.modules.soe.entitiy.dict.SoeRegionNameCountryDO;
import cloud.demand.app.web.model.dict.QueryCampusAndBizType2ZoneDTO;
import cloud.demand.app.web.model.dict.QueryCampusAndBizType2ZonePageDTO;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.wooutils.cache.HiSpeedCache;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface DictService {


    /**
     * 获取腾讯云region的映射关系
     */
    Map<String, String> getRegionNameMap();

    Map<String, List<StaticZoneDO>> getRegionName2StaticZone();

    /**
     * 获取腾讯云regionName to city的映射关系
     *
     * case : 华东地区(上海) -> 上海
     */
    Map<String, String> getRegionName2City();

    /**
     * 获取腾讯云， regionName 到 国家的映射
     *
     * @return map
     */
    Map<String, String> getRegion2CustomhouseTitleMap();

    Map<String, String> getRegion2CountryMapping();

    Map<String, String> getRegionName2CountryMapping();

    /**
     * 根据国家名称，获取下面的地域名称（上海、广州这种）
     */
    List<String> getRegionNameByCountryName(String countryName);

    /**
     * 根据CustomhouseTitle 获取regionName
     *
     * @return 地域集合
     */
    List<String> getRegionNameByCustomhouseTitle(String customhouseTitle);

    /**
     * @return map
     */
    Map<String, String> getRegionMap();

    /**
     * 获取腾讯云的地域列表
     */
    List<String> queryRegionNameList();

    /**
     * 获取腾讯云zone的映射关系
     */
    Map<String, String> getZoneNameMap();


    /**
     * 获取腾讯云的可用区 - 列表
     * @return ret
     */
    Map<String, String> getZoneName2RegionName();

    Map<String, String> getZoneMap();

    Map<String, StaticZoneDO> getZone2DOMap();

    /**
     * 获取腾讯云的可用区列表
     */
    List<String> queryZoneNameList();

    Map<String,StaticZoneDO> queryZoneName2StaticZone();

    /**
     * 获取腾讯云的可用区列表 by regionName
     */
    List<String> queryZoneNameList(String regionName);

    /**
     * 查询static_zone 全表,缓存60秒
     * @return list
     */
    List<StaticZoneDO> queryStaticZone();

    /**
     * 获取腾讯云的地域列表 by regionName
     */
    List<String> queryRegionNameList(String areaName);

    /**
     * 获取Plan系统中的全量zone信息
     */
    Map<Long, StaticZoneDO> getAllPlanZoneInfos();

    /**
     * 获取Plan系统中的全量zone信息, group by zone_name
     */
    Map<String, StaticZoneDO> getAllPlanZoneInfosGroupByName();

    /**
     * 获取Plan系统中的全量zone信息, group by regin_name
     */
    Map<String, StaticZoneDO> getAllPlanZoneInfosGroupByRegionName();

    /**
     * 获取Plan系统中的全量zone信息, group by regin
     */
    Map<String, StaticZoneDO> getAllPlanZoneInfosGroupByRegion();

    /**
     * 获取Plan系统中的全量zone信息, group by zone
     * case: ap-guangzhou-1 -> StaticZoneDO
     */
    Map<String, StaticZoneDO> getAllPlanZoneInfosGroupByCode();

    /**
     * 通过zoneid获取对应zone对象的基础信息
     */
    StaticZoneDO getStaticZoneInfoById(Long zoneId);

    /**
     * 通过zone_name获取对应zone对象的基础信息
     */
    StaticZoneDO getStaticZoneInfoByName(String zoneName);

    /**
     * 通过campus查询zone信息
     */
    StaticZoneDO getStaticZoneInfoByCampus(String campus);

    /**
     * 查询全部机型可用区信息
     */
    List<StaticZoneDO> getAllZoneInfos();

    /**
     * 通过module查询zone信息
     */
    StaticZoneDO getStaticZoneInfoByModule(String moduleName);

    /**
     * 获取母机机型 -> 逻辑核心数的Map
     */
    Map<String, Integer> getDeviceLogicCpuCore();

    /**
     * 获得母机机型的核心数，支持指定是否前缀匹配
     *
     * @param matchPrefix 是否开启前缀匹配，前缀匹配采用最长前缀匹配
     * @return 机型不存在返回0
     */
    Integer getDeviceLogicCpuCore(String deviceType, boolean matchPrefix);

    /**
     * 查询指定母机的gpucard数
     */
    Integer getDeviceGpuCard(String deviceType);

    /**
     * 查询指定母机的存储数量，如果不存在该机型，用DEFAULT默认机型
     * 单位转化成pb
     */
    BigDecimal getDeviceCbsStore(String deviceType);

    /**
     * 查询指定母机的存储数量，如果不存在该机型，取0
     * 单位转化成pb
     */
    BigDecimal getDeviceCosStore(String deviceType, Date date, boolean isCN);

    /**
     * 查询指定母机的存储数量，如果不存在该机型，取0
     * 单位转化成pb
     */
    BigDecimal getDeviceCosStore(String planProduct, String deviceType);

    /**
     * 查询CDB产品指定物理机的最大可售数量，如果不存在该机型，取0
     * 单位转化成 tb
     */
    BigDecimal getDeviceCdbStore(String deviceType);

    /**
     * 获取erp城市相关信息的map，其中k是cityName
     */
    Map<String, BasCmdbCityDO> getBasCmdbCityInfoMap();

    /**
     * 查询CDB产品指定物理机的单机内存，如果不存在该机型，取0
     * 单位转化成 tb
     */
    BigDecimal getDeviceCdbMemCap(String deviceType);

    /**
     * 查询CRS产品指定物理机的物理容量，若不存在，取0
     * 单位 gb
     */
    BigDecimal getDeviceCrsMemCap(String deviceType);

    /**
     * 查询 StaticGinstypeDO
     * @return instancetype -> info
     */
    Map<String, StaticGinstypeDO> getAllInstanceTypes();

    /**
     * 获取Cmongo产品指定物理机的最大可用容量，单位gb
     */
    BigDecimal getCmongoDeviceMem(String deviceType);

    /**
     * 查询Plan系统中指定母机的逻辑核心数
     */
    Integer getPlanDeviceLogicCore(String deviceType);

    /**
     * 查询服务器处理器的类型
     *
     * @param deviceType 详见ComputeTypeEnum
     */
    String getComputeType(String deviceType);

    /**
     * 查询cvm的处理器类型
     */
    String getCvmComputeType(String instanceType);

    /**
     * 查询cvm机型的cpu核心数；不存在的返回0
     */
    Integer getInstanceCoreNum(String instanceModel);

    /**
     * 查询全部物理机和部件映射表数据
     */
    Map<String, ServerPartsCompositionVO> getAllServerPartsCompositions();

    /**
     * 通过物理机机型查询物理机和部件的映射关系
     */
    ServerPartsCompositionVO getServerPartsCompositionByDeviceType(String deviceType);

    /**
     * 查询全部网络类型信息
     */
    Map<String, BasStrategyDeviceVersionNettypeDO> getAllNetTypes();

    /**
     * 通过物理机机型查询对应的网络类型信息
     */
    BasStrategyDeviceVersionNettypeDO getNetTypeByDeviceType(String deviceType);

    /**
     * 查询全部Csig设备扩展信息
     */
    Map<String, CloudDemandCsigDeviceExtendInfoDO> getAllCsigDeviceExtendInfos();

    /**
     * 通过物理机机型查询对应的Csig设备扩展信息
     */
    CloudDemandCsigDeviceExtendInfoDO getCsigDeviceExtendInfoByDeviceType(String deviceType);

    /**
     * CSIG 设备类型与实例类型的映射表获取
     *
     * @return
     */
    Map<String, String> getCsigDeviceTypeToInstanceTypeMap();

    String getCsigInstanceTypeByDeviceType(String deviceType);

    Map<String, List<String>> getCsigInstanceTypeToDeviceTypeMap();

    List<String> getCsigDeviceTypeByInstanceType(String instanceType);

    List<String> getCsigDeviceTypeByInstanceType(List<String> instanceType);


    /**
     * 通过物理机机型查询对应的机型族信息(一对一)
     *
     * @param deviceType
     * @return
     */
    String getDeviceFamilyByDeviceType(String deviceType);

    /**
     * 通过机型族反查出任意物理机机型(多对一)
     *
     * @param deviceFamily
     * @return
     */
    String getDeviceTypeByDeviceFamily(String deviceFamily);

    /**
     * 获取全量GPU卡类配置信息
     */
    Map<String, ReportConfigGpuTypeDO> loadGpuCardConfig();


    /**
     * 查询 csig 的规划产品 id
     *
     * @return
     */
    List<Integer> getCsigDeptId();

    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 600)
    Map<String, ObsBizInfo> getBizInfoByPlanProductName();

    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 120)
    Map<String, ProductInfoDTO> getProductInfoByProductName();

    /** 实例族 ---> 实例类型集合 */
    Map<String, Set<String>> getInstanceFamily2TypeMap();

    Map<String, BasObsCloudCvmTypeDO> queryInstanceModelInfos();

    Map<String, BasObsCloudCvmTypeDO> queryInstanceCodeInfos();

    /**
     * 根据ERP Campus映射到腾讯云的可用区信息
     */
    Map<String, Long> getZoneIdByCampusName();

    /**
     * 根据ERP zoneName映射到YuntiStategyZoneDO
     */
    Map<String, YuntiStategyZoneDO> getConfigMapByZoneName();

    /**
     * 根据ERP 云梯zone
     * @return List<YuntiStategyZoneDO>
     */
    List<YuntiStategyZoneDO> getYunTiStategyZoneDOList();

    /**
     * 根据ERP zone映射到腾讯云的campus信息
     */
    Map<String, String> getCampusByZone();

    List<StaticZoneDO> getAllZoneInfo();

    /**
     * 根据ERP ModuleName映射到腾讯云的可用区信息
     */
    Map<String, Long> getZoneIdByModuleName();

    /**
     * 查询产品的库存成本单价
     */
    Map<String, BigDecimal> getProductUnitPrice();

    /**
     * 获取所有好料机型
     */
    Map<String, StaticStockPrincipalHosttypeDO> getAllGoodDeviceType();

    List<SoeRegionNameCountryDO> getRegion2Country();

    /**
     * 获取指定地域的国家信息
     * @param regionShort 简化的地域编码，例如：gz
     */
    SoeRegionNameCountryDO getRegion2Country(String regionShort);

    /**
     * 获取全部腾讯云地域信息
     */
    List<TxyRegionInfoDTO> getAllTxyRegionInfo();

    /**
     * 城市 -> 地区名称 ，用来判断是境内还是境外
     * eg: 北京 -> 中国内地
     * 加拿大 -> 加拿大
     */
    String getCountryChineseByCityName(String CityName);

    /**
     * 获取Erp地域相关的信息，key是CampusName
     */
    Map<String, ErpRegionInfoDTO> getErpRegionInfo();

    /**
     * 获取Erp的city 2 zone
     */
    Map<String, String> getErpCity2Zone();

    /**
     * 获取Erp的zone 2 region
     */
    Map<String, String> getErpZone2Region();

    /**
     * weekInfo
     *
     * @return list
     */
    List<ResPlanHolidayWeekDO> getAllHolidayWeekInfos();

    /**
     * 通过一个基准的节假周配置，获取前/后间隔为offset的节假周
     *
     * @param base 基准节假周
     * @param isAfter 是否落后节假周，否则为领先
     * @param offset 间隔
     * @return 目标节假周
     */
    ResPlanHolidayWeekDO getHolidayWeekInfoByBaseAndOffset(ResPlanHolidayWeekDO base, boolean isAfter, int offset);

    /**
     * 根据指定年+周确定唯一的节假周DO
     */
    ResPlanHolidayWeekDO getHolidayWeekInfoByYearWeek(int year, int week);

    /**
     * 根据指定年+月确定当月的节假周DO列表
     */
    List<ResPlanHolidayWeekDO> getHolidayWeekInfoByYearMonth(int year, int month);

    /**
     * 获取两个节假周之间的间隔
     *
     * @param beg 开始节假周
     * @param end 结束节假周
     * @return 间隔 = end - beg
     */
    Integer getHolidayWeekInfoOffset(ResPlanHolidayWeekDO beg, ResPlanHolidayWeekDO end);

    /**
     * 获取指定时间的节假周信息
     */
    ResPlanHolidayWeekDO getHolidayWeekInfoByDate(String dateStr);

    /**
     * 获取指定时间的节假周信息，批量
     *
     * @param dateStrs 日期字符串，格式为yyyy-MM-dd
     * @return 返回的map的key是对应的dateStr
     */
    Map<String, ResPlanHolidayWeekDO> getHolidayWeekInfoByDates(List<String> dateStrs);


    /**
     * 提供给外部系统使用的设备类型字典接口
     *
     * @return 设备类型列表
     */
    PageData<ServerPartsExtendedInfoDO> queryDeviceTypeInfoList(int pageNum, int pageSize, Boolean defaultFlag);


    /**
     * 通过传入的产品类型获取需求视图的产品分类，用category5、和全年需求执行报表的逻辑一致
     */
    List<String> queryPlanProductByDemandCategory(String productType);

    /**
     * 当某个地域下的可用区为【随机可用区】时调用
     * 获取某个地域下的默认可用区名称
     */
    String queryDefaultZoneNameByRegionName(String RegionName);

    /**
     * 从账号宽表中提取行业部门（含空值）
     *
     * @return
     */
    List<String> queryIndustryDeptFromAccountInfo();

    /**
     * 根据ERP Campus映射到腾讯云的可用区全量信息
     * 对应页面链接：https://tcres.woa.com/v3/resource/publicCloudSeat/config中【机房业务类型映射表】tab
     */
    QueryCampusAndBizType2ZoneDTO getCampusAndBizType2ZoneInfos();


    /**
     * https://tcres.woa.com/v3/resource/publicCloudSeat/index
     */
    QueryCampusAndBizType2ZonePageDTO getCampusAndBizType2ZoneInfosPage();

    /**
     * https://tcres.woa.com/v3/resource/publicCloudSeat/index
     * 参数 mod_band_type_name 改成 100G
     */
    QueryCampusAndBizType2ZonePageDTO getCampusAndBizType2ZoneInfosPage100();

    /**
     * 全量的老接口，映射不唯一，用作都低
     *
     * @return
     */
    Map<String, StaticZoneDO> getCampus2ZoneInfoMapV0();

    /**
     * 通过getCampusAndBizType2ZoneInfos接口返回一个campusName -> zoneInfo的映射接口
     * 通过【物理机Campus + Module业务类型】来唯一确认【腾讯云虚拟机可用区】信息
     * 对于相同Campus的多个业务类型，按照：【腾讯云-公有云-通用】-> 【腾讯云-公有云-黑石专用】-> 【其他】的优先级获取对应zone数据
     *
     * @return Map k:上海-花桥 v:上海五区的可用区信息
     */
    Map<String, StaticZoneDO> getCampus2ZoneInfoMap();

    /**
     * getCampus2ZoneInfoMap，逆向映射，因为 getCampus2ZoneInfoMap 会保证一对一映射，所以可以反查
     *
     * @return
     */
    Map<String, String> getZone2CampusInfoMap();

    /**
     * 调用getCampus2ZoneInfoMap接口根据CampusName找ZoneName
     *
     * @return Map k:上海-花桥 v:上海五区
     */
    String getZoneNameByCampusAndBizType(String campusName);

    /**
     * 调用getCampus2ZoneInfoMap接口根据ZoneName找CampusName
     *
     * @param zoneName
     * @return
     */
    String getCampusNameByZoneName(String zoneName);

//    /**
//     * 物理机类型到实例类型的 1:1 映射
//     * @return
//     */
//    Map<String, String> getDeviceType2InstanceTypeMap();

    UserPersonalConfigResp getUserPersonalConfig(UserPersonalConfigReq req);

    void saveUserPersonalConfig(UserPersonalConfigReq req);

    List<CrpBizTimeConfigDO> queryBizTimeConfigByGroup(String group, Boolean isFilterNullTime);

    void updateBizTimeConfig(UpdateBizTimeConfigReq req);

    Date getLocalDateByBizTimeWeek(CrpCommonHolidayWeekDO currentWeek, CrpBizTimeConfigDO crpBizTimeConfigDO);

    Date getLocalDateByTimeConfig(CrpCommonHolidayWeekDO currentWeek,
            Integer nextWeek,Integer dayOfWeek, LocalTime time);


    /**
     *
     * @param yearMonth yyyy-MM 格式
     * @param crpBizTimeConfigDO
     * @return
     */
    Date getDateByBizTimeMonth(String yearMonth, CrpBizTimeConfigDO crpBizTimeConfigDO);

    void refreshHolidayWeek(Integer year);

    CrpCommonHolidayWeekDO binarySearchHolidayWeek(List<CrpCommonHolidayWeekDO> list, String date);

    CrpCommonHolidayWeekDO queryNextWeek(LocalDate localDate);

    CrpCommonHolidayWeekDO queryCurrentWeek(LocalDate localDate);

    /**
     * @see #eventNotice(String, String, String, Map, String, String)
     */
    void eventNotice(String eventCode, String noticeTitle, String noticeContent);

    /**
     * @see #eventNotice(String, String, String, Map, String, String)
     */
    void eventNotice(String eventCode, String noticeTitle, String noticeContent,
            Map<String, Object> templateParams, String users);

    /**
     * crp_event_notice_config
     * 为避免方法报错阻塞正常流程，此方法内部捕获异常，通知对应研发 <br/><br/>
     * 实际通知的用户，会取方法入参用户和配置表中用户和角色的并集并去重 <br/><br/>
     * 入参或配置的通知内容，会使用模版参数来解析替换到通知内容中 <br/>
     * 例，通知内容为 <br/>
     * 您当前有客户【{customerName}】的{size}条PPL尚未提交 <br/>
     * 模版参数为
     * <blockquote><pre>
     *     Map&lt;String, Object&gt; templateParams = new HashMap&lt;&gt;();
     *     templateParams.put("size", 1);
     *     templateParams.put("customerName", "拼多多");
     * </pre></blockquote>
     * 则实际通知的内容为 <br/>
     * 您当前有客户【拼多多】的1条PPL尚未提交 <br/>
     *
     * @param eventCode 事件Code 必填
     * @param noticeTitle 若为空，则直接用配置表的
     * @param noticeContent 若为空，则直接用配置表的
     * @param templateParams 通知内容的模版参数
     * @param users 需要通知的用户，多个用户用 {@code ; } 隔开
     * @param industryDept 适用的行业部门， 为空时从 模版参数中获取key 为 industryDept的值作为行业部门
     */
    void eventNotice(String eventCode, String noticeTitle, String noticeContent,
            Map<String, Object> templateParams, String users, String industryDept);


    Boolean checkIsAdmin(String userName);


    List<IndustryDemandIndustryWarZoneDictDTO> queryIndustryWarZoneCustomerConfig(QueryIndustryWarZoneReq req);

    List<CustomerOnlyDTO> queryCustomerOnly(List<String> commonCustomerNameList, boolean queryUinOrFullName);

    IndustryDemandIndustryWarZoneDictDO queryWarZoneConfigByCustomerShortName(String industryDept,
            String customerName);

    List<IndustryDemandIndustryWarZoneDictDO> queryEnableIndustryWarZoneCustomerConfig();

    List<String> getBigCustomerShortName();

    /**
     * 获取 <行业,Top通用客户简称>
     * @return
     */
    Map<String, Set<String>> queryIndustryToTopCommonCustomerName();

    /**
     * 获取 <行业,客户简称>
     * @return
     */
    Map<String, Set<String>> queryIndustryToCustomerShortName();

    void insertOrUpdateIndustryWarZoneCustomerConfig(List<IndustryDemandIndustryWarZoneDictDO> insertOrUpdateList);

    void deletedIndustryWarZoneCustomerConfig(List<Long> ids);

    Map<String, IndustryDemandIndustryWarZoneDictDO> queryCustomerMap();

    /**
     * 获取所有的通用客户简称 映射 客户简称的Map
     *
     * @return
     */
    Map<String, List<IndustryDemandIndustryWarZoneDictDO>> queryCommonCustomerMap();


    /**
     * 获取所有的通用客户简称 映射 客户简称的Map
     *
     * @return
     */
    Map<String, List<String>> queryCommonCustomerName2ShortName();


    /**
     * 获取 某个行业下的 所有的通用客户简称 映射 客户简称的Map
     *
     * @return
     */
    Map<String, List<String>> queryCommonCustomerName2ShortNameByIndustryDept(String industryDept);

    /**
     * 通过客户简称返回通用客户简称 如没有返回本身
     * @param customerShortName
     * @return
     */
    String getCommonCustomerShortNameByCustomerShortName(String customerShortName);

    /**
     * 获取通用客户简称下 所有的客户简称， 没有则返回本身
     *
     * @param commonCustomerName
     * @return
     */
    List<String> queryAllCustomerShortName(String commonCustomerName);

    /**
     * 查询主力园区
     */
    List<String> queryMainZone();

    /**
     * 查询主力机型
     */
    List<String> queryMainInstanceType();

    List<String> queryRegionByCustomhouseTitle(String customhouseTitle);

    Map<String, PublicIndustryInstanceModelEnumDO> queryInstanceCvmWithGpu(List<String> instanceModelList);

    Map<String, String> queryInstanceTypeToGroup();

    Map<String, List<String>> queryGroupToInstanceType();

    Map<String,List<String>> queryPassWhiteListUin();

    List<UinNameDTO> queryCustomerByUin(List<String> uinList, String industryDept);
}
