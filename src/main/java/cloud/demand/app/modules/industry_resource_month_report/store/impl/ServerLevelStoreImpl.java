package cloud.demand.app.modules.industry_resource_month_report.store.impl;

import cloud.demand.app.modules.industry_cockpit.v3.enums.ProductEnum;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.NormalWithholdDetailTableReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.NormalWithholdDetailResp;
import cloud.demand.app.modules.industry_cockpit.v3.model.vo.NormalWithholdDetailItemVO;
import cloud.demand.app.modules.industry_cockpit.v3.service.WithholdService;
import cloud.demand.app.modules.industry_resource_month_report.dto.req.IndustryResourceReq;
import cloud.demand.app.modules.industry_resource_month_report.dto.req.IndustryResourceWithTopReq;
import cloud.demand.app.modules.industry_resource_month_report.store.ServerLevelStore;
import cloud.demand.app.modules.industry_resource_month_report.utils.ReportUtils;
import cloud.demand.app.modules.mrpv2.utils.PageQueryUtils;
import cloud.demand.app.modules.order.dto.req.ServiceLevelQueryReq;
import cloud.demand.app.modules.order.dto.resp.service_level.OfficialWebsiteServiceLevelOverviewResp;
import cloud.demand.app.modules.order.dto.resp.service_level.OrderServiceLeveResp;
import cloud.demand.app.modules.order.dto.resp.service_level.OrderServiceLeveResp.OrderServiceLevelDTO;
import cloud.demand.app.modules.order.service.OrderSatisfyRateService;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.soe.enums.DateTypeEnum;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop.http.domain.lab.PerformanceTrackItem;
import cloud.demand.app.modules.sop.http.domain.lab.PerformanceTrackReq;
import cloud.demand.app.modules.sop.http.domain.lab.PerformanceTrackReqBuilder;
import cloud.demand.app.modules.sop.http.domain.lab.PerformanceTrackResp;
import cloud.demand.app.modules.sop.http.service.CloudAppLabPerformanceHttpService;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Function;

@Service
public class ServerLevelStoreImpl implements ServerLevelStore {

    /**
     * 服务水平
     */
    @Resource
    private OrderSatisfyRateService orderSatisfyRateService;

    /**
     * 履约率 v2
     */
    @Resource
    private CloudAppLabPerformanceHttpService performanceHttpService;

    /**
     * 驾驶舱
     */
    @Resource
    private WithholdService withholdService;

    @Resource
    private ServerLevelStore self;

    private ServiceLevelQueryReq buildSLReq(IndustryResourceReq req) {
        String yearMonth = req.getYearMonth();
        ServiceLevelQueryReq queryReq = new ServiceLevelQueryReq();
        YearMonth parse = YearMonth.parse(yearMonth);
        // 默认 CVM&CBS
        queryReq.setProduct(Ppl13weekProductTypeEnum.CVM.getName());
        queryReq.setEndMonth(parse);
        queryReq.setBeginMonth(parse);
        if (req.getIndustryDept() != null){
            queryReq.setIndustryDeptList(ListUtils.newList(req.getIndustryDept()));
        }
        List<String> customhouseTitle = req.getCustomhouseTitle();
        if (ListUtils.isNotEmpty(customhouseTitle) && customhouseTitle.size() == 1) {
            queryReq.setCustomhouseTitle(customhouseTitle.get(0));
        }
        return queryReq;
    }

    /** 缓存 12 小时 */
    @Override
    @HiSpeedCache(expireSecond = 60 * 60 * 12, keyScript = "args[0]", useRedis = true, cacheRedisDataMillisecond = 60)
    public ServerLevelItem doGetData(IndustryResourceReq req){
        ServerLevelItem ret = new ServerLevelItem();
        // 1. 查询服务水平接口(IRMR_SL_SL) && 查询履约率(IRMR_SL_PR)
        SeverLevelAndPerformanceRateL1 slAndPR = getSlAndPR(req);
        ret.setYearMonth(req.getYearMonth());
        ret.copyServerLevel(slAndPR);
        ret.copyPerformanceRate(slAndPR);
        ret.setCustomerDetails(slAndPR.getCustomerDetails());

        // 2. 设置中长尾服务水平(IRMR_SL_OFFICE)
        ServiceLevelQueryReq officialReq = buildSLReq(req);
        // 官网服务水平默认看主力机型和主力园区
        officialReq.setMainZone(true);
        officialReq.setMainInstanceType(true);
        OfficialWebsiteServiceLevelOverviewResp officialWebsiteResp = orderSatisfyRateService.officialWebsiteServiceLevelOverview(
                officialReq);
        if (officialWebsiteResp != null) {
            ret.setOfficialWebsiteServerLevel(officialWebsiteResp.getServiceLevel());
        }

        // 3. 设置预扣(IRMR_SL_Withholding)
        ret.setWithholding(getWithholdingL1(req));
        return ret;
    }

   @Override
    public Object getData(IndustryResourceReq req) {
        return self.doGetData(req);
    }

    @Override
    public Object getTopData(IndustryResourceWithTopReq req) {
        ServerLevelItem ret = self.doGetData(req.toIndustryResourceReq());
        Integer topNL1 = req.getTopNL1();
        Integer topNL2 = req.getTopNL2();
        Integer topNOrder = req.getTopNOrder();
        // 分客户订单详情 top 处理
        List<SeverLevelAndPerformanceRateL2> customerDetails = ret.getCustomerDetails();
        if (ListUtils.isNotEmpty(customerDetails) && topNOrder != null && topNOrder > 0){
            // 1. 客户层级排序
            customerDetails.sort(customerDetails.get(0));
            SeverLevelAndPerformanceRateL2 other = new SeverLevelAndPerformanceRateL2();
            other.setUnCustomerShortName(ReportUtils.OTHER);
            // 2. 非 top 归到其他
            List<SeverLevelAndPerformanceRateL2> topN = ReportUtils.getTopNIgnoreEmpty(customerDetails, topNOrder, other, (o1, o2) -> {
                o1.setConsensusCore(SoeCommonUtils.addAll(o1.getConsensusCore(), o2.getConsensusCore()));
                o1.setMatchCore(SoeCommonUtils.addAll(o1.getMatchCore(), o2.getMatchCore()));
                o1.setPerformanceCore(SoeCommonUtils.addAll(o1.getPerformanceCore(), o2.getPerformanceCore()));
            });
            other.computeServerLevel();
            other.setPerformanceRate(null);
            ret.setCustomerDetails(topN);
        }
        // 预扣 top 处理
        List<WithholdingL1> withholding = ret.getWithholding();
        if (ListUtils.isNotEmpty(withholding)){
            // 1. L1（客户层级）排序
            withholding.sort(withholding.get(0));
            // 2. L1（客户层级）非 top 归到其他
            WithholdingL1 other = new WithholdingL1();
            other.setUnCustomerShortName(ReportUtils.OTHER);
            List<WithholdingL1> topN = ReportUtils.getTopNIgnoreEmpty(withholding, topNL1, other, (o1, o2) -> {
                o1.setPreYearMonthCore(ReportUtils.addAll(o1.getPreYearMonthCore(),o2.getPreYearMonthCore()));
                o1.setCurYearMonthCore(ReportUtils.addAll(o1.getCurYearMonthCore(),o2.getCurYearMonthCore()));
            });
            ret.setWithholding(topN);
            for (WithholdingL1 withholdingL1 : topN) {
                if (withholdingL1 != other && ListUtils.isNotEmpty(withholdingL1.getL2Data())){
                    List<WithholdingL2> l2Data = withholdingL1.getL2Data();
                    // 3. L2（实例类型层级）排序
                    l2Data.sort(l2Data.get(0));
                    WithholdingL2 l2Other = new WithholdingL2();
                    l2Other.setInstanceType(ReportUtils.OTHER);
                    // 4. L2（实例类型层级）非 top 归到其他
                    List<WithholdingL2> newL2 = ReportUtils.getTopNIgnoreEmpty(l2Data, topNL2, l2Other, (o1, o2) -> {
                        o1.setPreYearMonthCore(ReportUtils.addAll(o1.getPreYearMonthCore(), o2.getPreYearMonthCore()));
                        o1.setCurYearMonthCore(ReportUtils.addAll(o1.getCurYearMonthCore(), o2.getCurYearMonthCore()));
                    });
                    withholdingL1.setL2Data(newL2);
                }
            }
        }
        return ret;
    }

    /**
     * 获取服务水平和履约率
     *
     * @param req
     * @return
     */
    public SeverLevelAndPerformanceRateL1 getSlAndPR(IndustryResourceReq req) {
        return (SeverLevelAndPerformanceRateL1)getSlAndPR(req,false);
    }

    @Override
    public DataIndex getSlAndPR(IndustryResourceReq req,boolean needAllIndustryDept) {
        // 1. 获取服务水平
        DataIndex serverLevel = getServerLevel(req);
        // 如果需要全行业，则加一层 L0
        if (needAllIndustryDept){
            IndustryResourceReq industryResourceReq = req.rebuildWithIndustryDept(null);
            SeverLevelAndPerformanceRateL1 serverLevel0 = getServerLevel(industryResourceReq);
            SeverLevelAndPerformanceRateL1 temp = (SeverLevelAndPerformanceRateL1)serverLevel;
            serverLevel = new SeverLevelAndPerformanceRateL0();
            serverLevel.copyServerLevel(serverLevel0);
            ((SeverLevelAndPerformanceRateL0) serverLevel).setL1(temp);
        }
        // 2. 获取履约率
        DataIndex performanceRate = getPerformanceRate(req,needAllIndustryDept);
        // 3. 合并
        return mergeSlAndPR(serverLevel, performanceRate);
    }

    /**
     * 合并服务水平和履约率
     * @param serverLevel 服务水平
     * @param performanceRate 履约率
     * @param <T>
     * @return
     */
    private <T extends DataIndex> T mergeSlAndPR(T serverLevel, T performanceRate){
        if (serverLevel instanceof SeverLevelAndPerformanceRateL0){
            SeverLevelAndPerformanceRateL0 ret = new SeverLevelAndPerformanceRateL0();
            ret.copyServerLevel(serverLevel);
            ret.copyPerformanceRate(performanceRate);
            SeverLevelAndPerformanceRateL1 l1 = mergeSlAndPR(
                    ((SeverLevelAndPerformanceRateL0) serverLevel).getL1(),
                    ((SeverLevelAndPerformanceRateL0) performanceRate).getL1());
            ret.setL1(l1);
            return (T)ret;
        }
        SeverLevelAndPerformanceRateL1 ret = new SeverLevelAndPerformanceRateL1();
        ret.copyServerLevel(serverLevel);
        if (performanceRate != null) {
            ret.copyPerformanceRate(performanceRate);
        }
        Map<String,SeverLevelAndPerformanceRateL2> customerMap = new HashMap<>();
        List<SeverLevelAndPerformanceRateL2> serverCustomerDetails = ((SeverLevelAndPerformanceRateL1)serverLevel).getCustomerDetails();
        // 3.1 处理服务水平
        if (ListUtils.isNotEmpty(serverCustomerDetails)){
            for (SeverLevelAndPerformanceRateL2 detail : serverCustomerDetails) {
                SeverLevelAndPerformanceRateL2 l2 = customerMap.computeIfAbsent(
                        detail.getUnCustomerShortName(), k -> new SeverLevelAndPerformanceRateL2());
                l2.copyServerLevel(detail);
            }
        }
        // 3.2 处理履约率
        if (performanceRate != null){
            List<SeverLevelAndPerformanceRateL2> performanceRateCustomerDetails = ((SeverLevelAndPerformanceRateL1)performanceRate).getCustomerDetails();
            if (ListUtils.isNotEmpty(performanceRateCustomerDetails)){
                for (SeverLevelAndPerformanceRateL2 detail : performanceRateCustomerDetails) {
                    SeverLevelAndPerformanceRateL2 l2 = customerMap.computeIfAbsent(
                            detail.getUnCustomerShortName(), k -> new SeverLevelAndPerformanceRateL2());
                    l2.copyPerformanceRate(detail);
                }
            }
        }
        List<SeverLevelAndPerformanceRateL2> customerDetails = new ArrayList<>(customerMap.values());
        if (ListUtils.isNotEmpty(customerDetails)){
            customerDetails.sort(customerDetails.get(0));
        }
        ret.setCustomerDetails(customerDetails);
        return (T)ret;
    }

    /**
     * 获取服务水平
     *
     * @param req
     * @return
     */
    private SeverLevelAndPerformanceRateL1 getServerLevel(IndustryResourceReq req) {
        // 行业服务水平和履历率
        SeverLevelAndPerformanceRateL1 ret = new SeverLevelAndPerformanceRateL1();
        // 客户详细
        List<SeverLevelAndPerformanceRateL2> customerDetails = new ArrayList<>();
        ret.setCustomerDetails(customerDetails);
        OrderServiceLeveResp orderServiceLeveResp = orderSatisfyRateService.orderServiceLevelOverview(buildSLReq(req));
        List<OrderServiceLevelDTO> details = orderServiceLeveResp.getDetails();
        if (ListUtils.isNotEmpty(details)) {
            Map<String, List<OrderServiceLevelDTO>> listMap = ListUtils.groupBy(details,
                    OrderServiceLevelDTO::getCommonCustomerShortName);
            // 共识核心
            BigDecimal industryConsensusCore = BigDecimal.ZERO;
            // 满足核心
            BigDecimal industryMatchCore = BigDecimal.ZERO;
            for (Entry<String, List<OrderServiceLevelDTO>> entry : listMap.entrySet()) {
                List<OrderServiceLevelDTO> value = entry.getValue();
                BigDecimal consensusCore = BigDecimal.ZERO;
                BigDecimal matchCore = BigDecimal.ZERO;
                Set<String> uniqueSet = new HashSet<>(); // 订单号，实例类型，可用区唯一
                for (OrderServiceLevelDTO dto : value) {
                    // 共识取 sum
                    consensusCore = consensusCore.add(dto.getConsensusCore());
                    // 订单号，实例类型，可用区唯一
                    String key = String.join("@", dto.getOrderNumber(),dto.getInstanceType(),dto.getZoneName());
                    if (uniqueSet.add(key)) {
                        matchCore = matchCore.add(dto.getMatchCore());
                    }
                }
                industryConsensusCore = industryConsensusCore.add(consensusCore);
                industryMatchCore = industryMatchCore.add(matchCore);
                SeverLevelAndPerformanceRateL2 e = new SeverLevelAndPerformanceRateL2();
                e.setUnCustomerShortName(entry.getKey());
                e.setConsensusCore(consensusCore);
                e.setMatchCore(matchCore);
                if (consensusCore.compareTo(BigDecimal.ZERO) > 0) {
                    e.setServerLevel(matchCore.divide(consensusCore, 4, RoundingMode.HALF_UP));
                }
                customerDetails.add(e);
            }
            if (ListUtils.isNotEmpty(customerDetails)){
                customerDetails.sort(customerDetails.get(0));
            }
            ret.setConsensusCore(industryConsensusCore);
            ret.setMatchCore(industryMatchCore);
            if (industryConsensusCore.compareTo(BigDecimal.ZERO) > 0) {
                ret.setServerLevel(industryMatchCore.divide(industryConsensusCore, 4, RoundingMode.HALF_UP));
            }
        }
        return ret;
    }


    /**
     * 查询行业履约率，支持查询全行业
     *
     * @param req 原始请求
     * @param needAllIndustryDept 是否需要支持全行业
     * @return
     */
    private DataIndex getPerformanceRate(IndustryResourceReq req, boolean needAllIndustryDept) {

        // 1. 调用 cloud-demand-lab 接口查询履约率 v2 数据
        List<List<PerformanceTrackItem>> lists = PageQueryUtils.waitAll(30,
                () -> {
                    // 1. 查询行业履历率
                    return queryPerformanceTrack(req, (trackReq) -> trackReq.getDims().add("industryDept"));
                },
                () -> {
                    // 1. 查询行业履历率（不剔除未到结束时间订单）
                    if (!needAllIndustryDept) {
                        return null;
                    }
                    return queryPerformanceTrack(req, (trackReq) -> {
                        trackReq.getDims().add("industryDept");
                        trackReq.setFilterNotEndOrder(false);
                    });
                },
                () -> {
                    // 2. 当前行业下钻到客户维度
                    return queryPerformanceTrack(req, (trackReq) -> {
                        List<String> dims = trackReq.getDims();
                        dims.add("industryDept");
                        dims.add("commonCustomerName");
                    });
                },
                () -> {
                    // 3. 全行业履约率(needAllIndustryDept = true)
                    if (!needAllIndustryDept) {
                        return null;
                    }
                    return queryPerformanceTrack(req, (trackReq) -> {
                        // 忽略行业过滤
                        trackReq.setIndustryDept(null);
                    });
                });

        List<PerformanceTrackItem> industryData = lists.get(0);
        List<PerformanceTrackItem> industryDataWithEndOrder = lists.get(1);
        List<PerformanceTrackItem> customerData = lists.get(2);

        DataIndex ret;

        if (needAllIndustryDept) {
            List<PerformanceTrackItem> allIndustryData = lists.get(3);
            SeverLevelAndPerformanceRateL0 ret1 = buildL0(allIndustryData, industryData, customerData);
            // 设置 履约率（不剔除未到结束时间订单）,只有在查询全行业的时候才需要
            if (ret1.getL1() != null && ListUtils.isNotEmpty(industryDataWithEndOrder)){
                ret1.getL1().setPerformanceRateV2(industryDataWithEndOrder.get(0).getBuyRate());
            }
            ret = ret1;
        } else {
            ret = buildL1(industryData, customerData);
        }


        return ret;
    }

    /**
     * 构建 L1
     *
     * @param industryData 当前行业履约率
     * @param customerData 下钻客户履约率
     * @return
     */
    private SeverLevelAndPerformanceRateL1 buildL1(List<PerformanceTrackItem> industryData,
            List<PerformanceTrackItem> customerData) {
        if (ListUtils.isEmpty(industryData)) {
            return null;
        }
        SeverLevelAndPerformanceRateL1 ret = new SeverLevelAndPerformanceRateL1();
        PerformanceTrackItem industryItem = industryData.get(0);
        ret.setPerformanceCore(industryItem.getBuyTotalCore()); // 履约核心数
        ret.setPerformanceRate(industryItem.getBuyRate()); // 履约率
        List<SeverLevelAndPerformanceRateL2> customerDetails = new ArrayList<>();
        ret.setCustomerDetails(customerDetails);
        for (PerformanceTrackItem customerDatum : customerData) {
            SeverLevelAndPerformanceRateL2 e = new SeverLevelAndPerformanceRateL2();
            e.setUnCustomerShortName(customerDatum.getCommonCustomerName()); // 通用客户简称
            e.setPerformanceCore(customerDatum.getBuyTotalCore()); // 履约核心数
            e.setPerformanceRate(customerDatum.getBuyRate()); // 履约率
            customerDetails.add(e);
        }
        return ret;
    }

    /**
     * 构建 L0（全行业）
     *
     * @param allIndustryData 全行业履约率
     * @param industryData 当前行业履约率
     * @param customerData 下钻客户履约率
     * @return
     */
    private SeverLevelAndPerformanceRateL0 buildL0(List<PerformanceTrackItem> allIndustryData,
            List<PerformanceTrackItem> industryData,
            List<PerformanceTrackItem> customerData) {
        SeverLevelAndPerformanceRateL0 ret = new SeverLevelAndPerformanceRateL0();
        if (ListUtils.isNotEmpty(allIndustryData)){
            PerformanceTrackItem industryItem = allIndustryData.get(0);
            ret.setPerformanceCore(industryItem.getBuyTotalCore()); // 履约核心数
            ret.setPerformanceRate(industryItem.getBuyRate()); // 履约率
        }
        ret.setL1(buildL1(industryData, customerData));
        return ret;
    }

    /**
     * 查询履约率
     *
     * @param req 原始请求
     * @param consumer 自定义请求处理
     * @return
     */
    private List<PerformanceTrackItem> queryPerformanceTrack(IndustryResourceReq req,
            Consumer<PerformanceTrackReq> consumer) {
        PerformanceTrackReq trackReq = new PerformanceTrackReqBuilder()
                .industryDept(ListUtils.newList(req.getIndustryDept()))
                .startYearMonth(req.getYearMonth())
                .endYearMonth(req.getYearMonth())
                .customhouseTitle(req.getCustomhouseTitle())
                .isDefault(true)
                .build();
        // 默认年月，可支持 yearMonth，industryDept，commonCustomerName
        trackReq.setDims(ListUtils.newList("yearMonth"));
        if (consumer != null) {
            consumer.accept(trackReq);
        }
        PerformanceTrackResp performanceTrackResp = performanceHttpService.queryMonthPerformanceTrackItemList(trackReq);
        return performanceTrackResp.getData();
    }

    /**
     * 获取预扣信息L1 + L2 层数据
     *
     * @param req 请求
     * @return
     */
    private List<WithholdingL1> getWithholdingL1(IndustryResourceReq req) {
        String yearMonth = req.getYearMonth();
        YearMonth parse = YearMonth.parse(yearMonth);
        YearMonth preYearMonth = parse.plusMonths(-1);
        List<String> range = SoeCommonUtils.getRange(DateTypeEnum.YearMonth.getName(), preYearMonth.atEndOfMonth(),
                parse.atEndOfMonth(),
                LocalDate.now().plusDays(-1));
        // 上月预扣
        String preStatTime = range.get(0);
        // 当月预扣(月第一天不一定有数据)
        String curStatTime = range.size() > 1 ? range.get(1) : null;
        List<List<NormalWithholdDetailItemVO>> lists = PageQueryUtils.waitAll(30,
                () -> getWithholdDetailTable(req, (tableReq) -> tableReq.setStatTime(LocalDate.parse(preStatTime))),
                () -> curStatTime == null ? null : getWithholdDetailTable(req,
                        (tableReq) -> tableReq.setStatTime(LocalDate.parse(curStatTime))));
        List<NormalWithholdDetailItemVO> preData = lists.get(0);
        List<NormalWithholdDetailItemVO> curData = ObjectUtils.defaultIfNull(lists.get(1), new ArrayList<>());
        Function<NormalWithholdDetailItemVO, String> getter = (item) -> String.join("@", item.getCustomerShortName(),
                item.getInstanceType());
        Map<String, WithholdingL1> l1Map = new HashMap<>();
        ListUtils.merge(preData, curData, getter, getter, (preList, curList) -> {
            WithholdingL1 l1 = null;
            WithholdingL2 l2 = null;
            if (ListUtils.isNotEmpty(preList)) {
                NormalWithholdDetailItemVO temp = preList.get(0);
                String customerShortName = temp.getCustomerShortName();
                l1 = l1Map.get(customerShortName);
                if (l1 == null) {
                    l1 = new WithholdingL1();
                    l1.setUnCustomerShortName(customerShortName);
                    l1.setL2Data(new ArrayList<>());
                    l1Map.put(customerShortName, l1);
                }
                l2 = new WithholdingL2();
                l2.setInstanceType(temp.getInstanceType());
                l2.setPreYearMonthCore(temp.getTotalWithholdNum());
            }
            if (ListUtils.isNotEmpty(curList)) {
                NormalWithholdDetailItemVO temp = curList.get(0);
                String customerShortName = temp.getCustomerShortName();
                if (l1 == null) {
                    l1 = l1Map.get(customerShortName);
                    if (l1 == null) {
                        l1 = new WithholdingL1();
                        l1.setUnCustomerShortName(customerShortName);
                        l1.setL2Data(new ArrayList<>());
                        l1Map.put(customerShortName, l1);
                    }
                }
                if (l2 == null) {
                    l2 = new WithholdingL2();
                    l2.setInstanceType(temp.getInstanceType());
                }
                l2.setCurYearMonthCore(temp.getTotalWithholdNum());
            }
            if (l1 != null) {
                l1.setPreYearMonthCore(l1.getPreYearMonthCore() + l2.getPreYearMonthCore());
                l1.setCurYearMonthCore(l1.getCurYearMonthCore() + l2.getCurYearMonthCore());
                l1.getL2Data().add(l2);
            }
            return l2;
        });
        List<WithholdingL1> ret = new ArrayList<>(l1Map.values());
        // 按当月预扣降序
        ret.sort((o1, o2) -> o2.getCurYearMonthCore().compareTo(o1.getCurYearMonthCore()));
        for (WithholdingL1 withholdingL1 : ret) {
            withholdingL1.getL2Data().sort((o1, o2) -> o2.getCurYearMonthCore().compareTo(o1.getCurYearMonthCore()));
        }
        return ret;
    }


    /**
     * 获取预扣
     *
     * @param req
     * @return
     */
    private List<NormalWithholdDetailItemVO> getWithholdDetailTable(IndustryResourceReq req,
                                                                    Consumer<NormalWithholdDetailTableReq> consumer) {
        NormalWithholdDetailTableReq tableReq = new NormalWithholdDetailTableReq();
        tableReq.setIndustryDept(ListUtils.newList(req.getIndustryDept()));
        tableReq.setProduct(ProductEnum.CVM_CPU.getName());
        tableReq.setUnit("核数");
        tableReq.setQueryRange("计费用量");
        tableReq.setDims(ListUtils.newList(
                "industryDept",
                "warZone",
                "customerShortName",
                "instanceType"
        ));
        // 不设置 topN，这里自己处理
        tableReq.setTopN(null);
        tableReq.setCustomhouseTitle(req.getCustomhouseTitle());
        tableReq.setAppRole(ListUtils.newList(
                "EKS",
                "EMR",
                "正常售卖",
                "其他"
        ));
        consumer.accept(tableReq);
        NormalWithholdDetailResp withholdDetailTable = withholdService.getNormalWithholdDetailTable(tableReq);
        return withholdDetailTable.getDetailItemList();
    }
}
