package cloud.demand.app.modules.industry_resource_month_report.store.impl;

import cloud.demand.app.modules.industry_cockpit.v3.enums.ProductEnum;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.NormalWithholdDetailTableReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.NormalWithholdDetailResp;
import cloud.demand.app.modules.industry_cockpit.v3.model.vo.NormalWithholdDetailItemVO;
import cloud.demand.app.modules.industry_cockpit.v3.service.IndustryCockpitV3DictService;
import cloud.demand.app.modules.industry_cockpit.v3.service.IndustryCockpitV3DictService.LongTermVersion;
import cloud.demand.app.modules.industry_cockpit.v3.service.WithholdService;
import cloud.demand.app.modules.industry_resource_month_report.dto.req.IndustryResourceReq;
import cloud.demand.app.modules.industry_resource_month_report.dto.req.IndustryResourceWithTopReq;
import cloud.demand.app.modules.industry_resource_month_report.service.IndustryResourceCommonService;
import cloud.demand.app.modules.industry_resource_month_report.store.GpuProjectStore;
import cloud.demand.app.modules.industry_resource_month_report.utils.ReportUtils;
import cloud.demand.app.modules.mrpv2.enums.DemandTypeEnum;
import cloud.demand.app.modules.mrpv2.utils.PageQueryUtils;
import cloud.demand.app.modules.mrpv2.utils.RangeUtils;
import cloud.demand.app.modules.mrpv3.dto.req.MrpV3ReportReq;
import cloud.demand.app.modules.mrpv3.dto.req.MrpV3ReportReqBuilder;
import cloud.demand.app.modules.mrpv3.dto.resp.MrpV3ReportResp;
import cloud.demand.app.modules.mrpv3.enums.MrpV3DimFieldEnum;
import cloud.demand.app.modules.mrpv3.enums.MrpV3IndexFieldEnum;
import cloud.demand.app.modules.mrpv3.enums.MrpV3ReqCategoryEnum;
import cloud.demand.app.modules.mrpv3.model.item.MrpV3DataItem;
import cloud.demand.app.modules.mrpv3.service.MrpV3ReportService;
import cloud.demand.app.modules.p2p.longterm.controller.dto.LongtermGroupItemDTO;
import cloud.demand.app.modules.p2p.longterm.controller.dto.LongtermGroupItemWithDeptDTO;
import cloud.demand.app.modules.p2p.longterm.controller.dto.LongtermGroupReasonDTO;
import cloud.demand.app.modules.p2p.longterm.controller.dto.LongtermGroupReasonWithDeptDTO;
import cloud.demand.app.modules.p2p.longterm.controller.dto.LongtermTimeDTO;
import cloud.demand.app.modules.p2p.longterm.controller.req.LongtermDemandStatsReq;
import cloud.demand.app.modules.p2p.longterm.controller.resp.LongtermDemandStatsResp;
import cloud.demand.app.modules.p2p.longterm.controller.resp.LongtermDemandStatsResp.Item;
import cloud.demand.app.modules.p2p.longterm.service.LongtermStatsService;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.soe.enums.DateTypeEnum;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class GpuProjectStoreImpl implements GpuProjectStore {

    @Resource
    private MrpV3ReportService mrpV3ReportService;

    @Resource
    private IndustryResourceCommonService industryResourceCommonService;

    @Resource
    private WithholdService withholdService;

    /** 中长期预测 */
    @Resource
    private LongtermStatsService longtermStatsService;

    /** 中长期预测版本号-驾驶舱 */
    @Resource
    private IndustryCockpitV3DictService dictService;


    @Resource
    private GpuProjectStore self;


    @Override
    public Object getData(IndustryResourceReq req) {
        return self.doGetData(req);
    }


    /** 缓存 12 小时 */
    @Override
    @HiSpeedCache(expireSecond = 60 * 60 * 12, keyScript = "args[0]", useRedis = true, cacheRedisDataMillisecond = 60)
    public GpuProjectItem doGetData(IndustryResourceReq req) {
        GpuProjectItem ret = new GpuProjectItem();
        String yearMonth = req.getYearMonth();
        String preYearMonth = YearMonth.parse(yearMonth).plusMonths(-1).format(DateTimeFormatter.ofPattern("yyyy-MM"));

        List<String> pplYearMonthRange = industryResourceCommonService.getPplYearMonthRange(yearMonth);
        String startYm = pplYearMonthRange.get(1); // 取 0 表示从当月开始，取 1 表示从次月开始
        String endYm = pplYearMonthRange.get(pplYearMonthRange.size() - 1);
        List<String> ymRange = RangeUtils.rangeDate(startYm, endYm);

        List<List<MrpV3DataItem>> lists = PageQueryUtils.waitAll(30,
                // 1. GPU计费（客户&GPU卡型）
                () -> getGpuData(req, (v3ReportReq) -> {
                    v3ReportReq.setStartYearMonth(preYearMonth);
                    v3ReportReq.setEndYearMonth(yearMonth);
                    v3ReportReq.setIndex(
                            ListUtils.newList(MrpV3IndexFieldEnum.daily_scale_stock_bill.getColumn()));
                }),
                // 2. GPU新增预测（客户&GPU卡型）
                () -> getGpuData(req, (v3ReportReq) -> {
                    v3ReportReq.setStartYearMonth(startYm);
                    v3ReportReq.setEndYearMonth(endYm);
                    v3ReportReq.setIndex(
                            ListUtils.newList(MrpV3IndexFieldEnum.ppl_forecast_newest.getColumn()));
                    v3ReportReq.setDemandType(
                            ListUtils.newList(DemandTypeEnum.NEW.getName(), DemandTypeEnum.ELASTIC.getName()));
                })
        );

        // 2. 设置 GPU 计费
        List<MrpV3DataItem> gpuBillData = lists.get(0);
        Map<String, GpuBillScaleL1> billCustomerMap = new HashMap<>();
        Map<String, GpuBillScaleL2> billCustomerGpuCardMap = new HashMap<>();
        for (MrpV3DataItem gpuBillDatum : gpuBillData) {
            String key = gpuBillDatum.getUnCustomerShortName();
            GpuBillScaleL1 l1 = billCustomerMap.computeIfAbsent(key, k -> {
                GpuBillScaleL1 temp = new GpuBillScaleL1();
                temp.setUnCustomerShortName(key);
                temp.setGpuCardDetails(new ArrayList<>());
                return temp;
            });

            Integer billNum = ReportUtils.toInt(gpuBillDatum.getDailyScaleStockBillCoreNum());
            boolean isCurYearMonth = Objects.equals(gpuBillDatum.getYearMonth(), yearMonth);
            if (isCurYearMonth) {
                // 当月
                l1.setCurGpuNumber(l1.getCurGpuNumber() + billNum);
            } else {
                // 上个月
                l1.setPreGpuNumber(l1.getPreGpuNumber() + billNum);
            }
            String key2 = gpuBillDatum.getUnCustomerShortName() + "@" + gpuBillDatum.getGpuCardType();
            GpuBillScaleL2 l2 = billCustomerGpuCardMap.get(key2);
            if (l2 == null) {
                l2 = new GpuBillScaleL2();
                l2.setGpuCardType(gpuBillDatum.getGpuCardType());
                billCustomerGpuCardMap.put(key2, l2);
                l1.getGpuCardDetails().add(l2);
            }
            if (isCurYearMonth) {
                // 当月
                l2.setCurGpuNumber(l2.getCurGpuNumber() + billNum);
            } else {
                // 上个月
                l2.setPreGpuNumber(l2.getPreGpuNumber() + billNum);
            }
        }

        // 2.1 gpu 计费
        List<GpuBillScaleL1> gpuBillScale = new ArrayList<>(billCustomerMap.values());
        if (ListUtils.isNotEmpty(gpuBillScale)){
            gpuBillScale.sort(gpuBillScale.get(0));
            for (GpuBillScaleL1 gpuBillScaleL1 : gpuBillScale) {
                List<GpuBillScaleL2> gpuCardDetails = gpuBillScaleL1.getGpuCardDetails();
                if (ListUtils.isNotEmpty(gpuCardDetails)){
                    gpuCardDetails.sort(gpuCardDetails.get(0));
                }
            }
        }
        ret.setGpuBillScale(gpuBillScale);

        // 3. 设置 GPU 新增预测
        List<MrpV3DataItem> gpuAddForecastData = lists.get(1);
        Map<String, GpuNewestForecastL1> gpuAddForecastMap = new HashMap<>(); // key：通用客户简称，value：GpuNewestForecastL1
        Map<String, GpuNewestForecastL2> gpuAddForecastGpuCardMap = new HashMap<>(); // key：通用客户简称+GPU卡型，value：GpuNewestForecastL2

        for (MrpV3DataItem gpuAddForecastDatum : gpuAddForecastData) {
            String unCustomerShortName = gpuAddForecastDatum.getUnCustomerShortName();
            GpuNewestForecastL1 l1 = gpuAddForecastMap.computeIfAbsent(unCustomerShortName,
                    k -> {
                        GpuNewestForecastL1 temp = new GpuNewestForecastL1();
                        temp.setUnCustomerShortName(unCustomerShortName);
                        temp.setGpuCardDetails(new ArrayList<>());
                        temp.setGpuNumberMap(new TreeMap<>());
                        return temp;
                    });
            l1.addYearMonthValue(gpuAddForecastDatum.getYearMonth(), ReportUtils.toInt(gpuAddForecastDatum.getPplForecastNewestCoreNum()));
            String key2 = gpuAddForecastDatum.getUnCustomerShortName() + "@" + gpuAddForecastDatum.getGpuCardType();
            GpuNewestForecastL2 l2 = gpuAddForecastGpuCardMap.get(key2);
            if (l2 == null) {
                l2 = new GpuNewestForecastL2();
                l2.setGpuCardType(gpuAddForecastDatum.getGpuCardType());
                gpuAddForecastGpuCardMap.put(key2, l2);
                l1.getGpuCardDetails().add(l2);
            }
            l2.addYearMonthValue(gpuAddForecastDatum.getYearMonth(), ReportUtils.toInt(gpuAddForecastDatum.getPplForecastNewestCoreNum()));
        }

        // 3.1 排序
        List<GpuNewestForecastL1> gpuNewestForecast = new ArrayList<>(gpuAddForecastMap.values());
        if (ListUtils.isNotEmpty(gpuNewestForecast)){
            gpuNewestForecast.sort(gpuNewestForecast.get(0));
            for (GpuNewestForecastL1 gpuNewestForecastL1 : gpuNewestForecast) {
                List<GpuNewestForecastL2> gpuCardDetails = gpuNewestForecastL1.getGpuCardDetails();
                TreeMap<String, Integer> l1Map = gpuNewestForecastL1.getGpuNumberMap();
                if (l1Map == null){
                    l1Map = new TreeMap<>();
                    gpuNewestForecastL1.setGpuNumberMap(l1Map);
                }
                for (String ym : ymRange) {
                    if (!l1Map.containsKey(ym)){
                        l1Map.put(ym, 0);
                    }
                }
                if (ListUtils.isNotEmpty(gpuCardDetails)){
                    gpuCardDetails.sort(gpuCardDetails.get(0));
                    // 填充年月
                    for (GpuNewestForecastL2 gpuCardDetail : gpuCardDetails) {
                        TreeMap<String, Integer> l2Map = gpuCardDetail.getGpuNumberMap();
                        if (l2Map == null){
                            l2Map = new TreeMap<>();
                            gpuCardDetail.setGpuNumberMap(l2Map);
                        }
                        for (String ym : ymRange) {
                            if (!l2Map.containsKey(ym)){
                                l2Map.put(ym, 0);
                            }
                        }
                    }
                }
            }
        }
        ret.setGpuNewestForecast(gpuNewestForecast);

        // 4. 设置 GPU 预扣数据
        ret.setWithholding(getWithholding(req));

        // 5. 设置 GPU 中长期数据
        ret.setLongTerm(getGpuLongTerm(req));

        return ret;
    }

    /**
     * 获取中长期数据
     * @param req 请求
     * @return 中长期数据
     */
    private GpuLongTerm getGpuLongTerm(IndustryResourceReq req) {
        // map 数据（key：gpu 卡型 + 年）
        GpuLongTerm ret = getLongTermMap(req);
        // 填充 gpu 计费变化量
        ret.setItems(fillGpuScaleData(req, ret.getItems()));

        // 排序，按 gpu 卡数排序
        if (ListUtils.isNotEmpty(ret.getItems())){
            for (GpuLongTermL1 value : ret.getItems()) {
                value.sort();
            }
        }
        return ret;
    }

    /**
     * 填充 gpu 计费变化量
     * @param req 请求
     * @param l1Data 中长期预测数据
     */
    private List<GpuLongTermL1> fillGpuScaleData(IndustryResourceReq req,List<GpuLongTermL1> l1Data){

        Map<String, GpuLongTermL1> mapRet = l1Data == null ? new HashMap<>() : ListUtils.toMap(l1Data,
                item -> String.join("@", item.getGpuCardType(), String.valueOf(item.getYear())),item->item);

        String yearMonth = req.getYearMonth();
        // gpu 规模数据
        List<MrpV3DataItem> gpuScaleData = getGpuData(req, v3Req -> {
            String startYearMonth = yearMonth.substring(0, 4) + "-01"; // 首月
            v3Req.setStartYearMonth(startYearMonth);
            v3Req.setEndYearMonth(yearMonth); // 当前年月
            v3Req.setIndex(
                    ListUtils.newList(MrpV3IndexFieldEnum.daily_scale_change_bill.getColumn())); // 日规模计费变化
            v3Req.setDims(ListUtils.newArrayList(MrpV3DimFieldEnum.statTime.name(), MrpV3DimFieldEnum.yearQuarter.name(),
                    MrpV3DimFieldEnum.gpuCardType.name()));
        });
        // gpu 计费规模 map，维度：gpu 卡型， 年
        Map<String, List<MrpV3DataItem>> scaleMap = ListUtils.groupBy(gpuScaleData,
                item -> String.join("@",item.getGpuCardType(), item.getYearQuarter().substring(0, 4)));

        scaleMap.forEach((k,ls)->{
            GpuLongTermL1 gpuLongTermL1 = mapRet.get(k);
            if (gpuLongTermL1 == null){
                // 没有预测的规模，初始化预测为 0，其他 copy 规模数据
                gpuLongTermL1 = new GpuLongTermL1();
                gpuLongTermL1.setProduct(Ppl13weekProductTypeEnum.GPU.getName());
                gpuLongTermL1.setIndustryDept(req.getIndustryDept());
                MrpV3DataItem mrpV3DataItem = ls.get(0);
                gpuLongTermL1.setYear(Integer.valueOf(mrpV3DataItem.getYearQuarter().substring(0,4)));
                gpuLongTermL1.setGpuCardType(mrpV3DataItem.getGpuCardType());
                gpuLongTermL1.setTotalForecastGpuNum(BigDecimal.ZERO);
                mapRet.put(k,gpuLongTermL1);
            }
            // 填充规模数据
            BigDecimal totalScale = BigDecimal.ZERO;
            List<GpuLongTermL2> scaleData = new ArrayList<>();
            Function<Object, BigDecimal> fieldGetter = MrpV3IndexFieldEnum.daily_scale_change_bill.getFieldGetter();
            for (MrpV3DataItem l : ls) {
                BigDecimal gpuNum = ObjectUtils.defaultIfNull(fieldGetter.apply(l),BigDecimal.ZERO);
                totalScale = SoeCommonUtils.addAll(totalScale, gpuNum);
                GpuLongTermL2 gpuLongTermL2 = new GpuLongTermL2();
                gpuLongTermL2.setGpuNum(gpuNum);
                String yearQuarter = l.getYearQuarter();
                gpuLongTermL2.setLabel(yearQuarter);
                gpuLongTermL2.setIsAllYear(false);
                scaleData.add(gpuLongTermL2);
            }
            gpuLongTermL1.setBillScale(scaleData);
            gpuLongTermL1.setTotalBillScaleGpuNum(totalScale);
        });
        return new ArrayList<>(mapRet.values());
    }

    /**
     * 获取中长期预测数据
     * @param req
     * @return
     */
    private GpuLongTerm getLongTermMap(IndustryResourceReq req){
        String yearMonth = req.getYearMonth();
        // 首个已关闭的版本
        LongTermVersion v = dictService.getLongTermVersion(yearMonth);
        GpuLongTerm ret = new GpuLongTerm();

        if (v == null){
            return ret;
        }

        List<GpuLongTermL1> longTerm = new ArrayList<>();
        ret.setItems(longTerm);

        String versionCode = v.getVersionCode();
        LongtermDemandStatsReq longTermReq = new LongtermDemandStatsReq();
        longTermReq.setCurrentVersionCode(versionCode); // 当前版本号
        // 中长期数据拉取，全量数据，需要手动过滤
        LongtermDemandStatsResp demandStatsDetail = longtermStatsService.getDemandStatsDetail(longTermReq);
        // 填充预测数据
        if (demandStatsDetail != null){
            Item longTermLItem = demandStatsDetail.getCurrent();
            if (longTermLItem != null){
                List<LongtermTimeDTO> times = longTermLItem.getTimes();
                ret.setTimes(times);
                // 时间 map
                Set<String> timeMap = times.stream().map(item -> String.join("@",
                        String.valueOf(item.getYear()),
                        String.valueOf(item.getQuarter()),
                        String.valueOf(item.getMonth()))).collect(Collectors.toSet());
                List<LongtermGroupReasonWithDeptDTO> reasons = longTermLItem.getReasons();

                // 过滤行业，只保留 GPU 数据，境内外这里不涉及，不过滤
                reasons = reasons.stream().filter(reason -> Objects.equals(reason.getIndustryDept(), req.getIndustryDept())
                        && Objects.equals(reason.getProduct(), Ppl13weekProductTypeEnum.GPU.getName())).collect(Collectors.toList());

                Map<Integer, List<String>> reasonMap = reasons.stream().collect(Collectors.groupingBy(
                        LongtermGroupReasonDTO::getReasonYear, Collectors.mapping(LongtermGroupReasonDTO::getReason, Collectors.toList())
                ));

                ret.setReason(new TreeMap<>(reasonMap));

                List<LongtermGroupItemWithDeptDTO> items = longTermLItem.getItems();
                String industryDept = req.getIndustryDept();
                // 过滤行业，只保留 GPU 数据，境内外这里不涉及，不过滤
                items = items.stream().filter(item -> Objects.equals(item.getIndustryDept(), industryDept) &&
                        Objects.equals(item.getProduct(), Ppl13weekProductTypeEnum.GPU.getName())
                && timeMap.contains(String.join("@",
                        String.valueOf(item.getDemandYear()),
                        String.valueOf(item.getDemandQuarter()),
                        String.valueOf(item.getDemandMonth())))).collect(Collectors.toList());
                // 聚合，维度：产品，行业，GPU 卡型，年
                Map<String, List<LongtermGroupItemWithDeptDTO>> map = ListUtils.groupBy(items,
                        item -> String.join("@", item.getProduct(),
                                item.getIndustryDept(),
                                item.getGpuType(),
                                String.valueOf(item.getDemandYear())));
                for (List<LongtermGroupItemWithDeptDTO> value : map.values()) {
                    LongtermGroupItemWithDeptDTO item = value.get(0);
                    // 初始化L1 预测数据，分两个 group by，第一次是 gpu 卡型 + 年，第二次是季度
                    GpuLongTermL1 gpuLongTermL1 = new GpuLongTermL1();
                    gpuLongTermL1.setProduct(item.getProduct());
                    gpuLongTermL1.setIndustryDept(item.getIndustryDept());
                    gpuLongTermL1.setGpuCardType(item.getGpuType());
                    gpuLongTermL1.setYear(item.getDemandYear());
                    List<GpuLongTermL2> forecast = new ArrayList<>();
                    gpuLongTermL1.setForecast(forecast);
                    longTerm.add(gpuLongTermL1);
                    // 聚合
                    BigDecimal totalGpuNum = BigDecimal.ZERO;
                    Map<Integer, List<LongtermGroupItemWithDeptDTO>> quarterMap = ListUtils.groupBy(value,
                            LongtermGroupItemWithDeptDTO::getDemandQuarter);
                    for (Entry<Integer, List<LongtermGroupItemWithDeptDTO>> entry : quarterMap.entrySet()) {
                        Integer k = entry.getKey();
                        List<LongtermGroupItemWithDeptDTO> ls = entry.getValue();
                        GpuLongTermL2 gpuLongTermL2 = new GpuLongTermL2();
                        gpuLongTermL2.setIsAllYear(k == 0);
                        // 0 表示全年，1-4 表示 1-4 季度
                        if (gpuLongTermL2.getIsAllYear()){
                            gpuLongTermL2.setLabel(String.valueOf(gpuLongTermL1.getYear()));
                        }else {
                            gpuLongTermL2.setLabel(gpuLongTermL1.getYear() + "-Q" + k);
                        }
                        BigDecimal gpuNum = ls.stream().map(LongtermGroupItemDTO::getGpuNum)
                                .reduce(BigDecimal.ZERO, SoeCommonUtils::addAll);
                        gpuLongTermL2.setGpuNum(gpuNum);
                        totalGpuNum = SoeCommonUtils.addAll(totalGpuNum, gpuNum);
                        forecast.add(gpuLongTermL2);
                    }
                    gpuLongTermL1.setTotalForecastGpuNum(totalGpuNum);
                }
            }
        }
        return ret;
    }


    private List<GpuWithholdingL1> getWithholding(IndustryResourceReq req) {
        String yearMonth = req.getYearMonth();
        YearMonth parse = YearMonth.parse(yearMonth);
        YearMonth preYearMonth = parse.plusMonths(-1);
        List<String> range = SoeCommonUtils.getRange(DateTypeEnum.YearMonth.getName(), preYearMonth.atEndOfMonth(),
                parse.atEndOfMonth(),
                LocalDate.now().plusDays(-1));
        // 上月预扣
        String preStatTime = range.get(0);
        // 当月预扣(月第一天不一定有数据)
        String curStatTime = range.size() > 1 ? range.get(1) : null;
        List<List<NormalWithholdDetailItemVO>> lists = PageQueryUtils.waitAll(30,
                () -> getWithholdDetailTable(req, (tableReq) -> tableReq.setStatTime(LocalDate.parse(preStatTime))),
                () -> curStatTime == null ? null : getWithholdDetailTable(req,
                        (tableReq) -> tableReq.setStatTime(LocalDate.parse(curStatTime))));
        List<NormalWithholdDetailItemVO> preData = lists.get(0);
        List<NormalWithholdDetailItemVO> curData = ObjectUtils.defaultIfNull(lists.get(1), new ArrayList<>());
        Function<NormalWithholdDetailItemVO, String> getter = (item) -> String.join("@", item.getCustomerShortName(),
                item.getGpuCardType());
        Map<String, GpuWithholdingL1> l1Map = new HashMap<>();
        ListUtils.merge(preData, curData, getter, getter, (preList, curList) -> {
            GpuWithholdingL1 l1 = null;
            GpuWithholdingL2 l2 = null;
            if (ListUtils.isNotEmpty(preList)) {
                NormalWithholdDetailItemVO temp = preList.get(0);
                String customerShortName = temp.getCustomerShortName();
                l1 = l1Map.get(customerShortName);
                if (l1 == null) {
                    l1 = new GpuWithholdingL1();
                    l1.setUnCustomerShortName(customerShortName);
                    l1.setGpuCardDetails(new ArrayList<>());
                    l1Map.put(customerShortName, l1);
                }
                l2 = new GpuWithholdingL2();
                l2.setGpuCardType(temp.getGpuCardType());
                l2.setPreGpuNumber(temp.getTotalWithholdNum());
            }
            if (ListUtils.isNotEmpty(curList)) {
                NormalWithholdDetailItemVO temp = curList.get(0);
                String customerShortName = temp.getCustomerShortName();
                if (l1 == null) {
                    l1 = l1Map.get(customerShortName);
                    if (l1 == null) {
                        l1 = new GpuWithholdingL1();
                        l1.setUnCustomerShortName(customerShortName);
                        l1.setGpuCardDetails(new ArrayList<>());
                        l1Map.put(customerShortName, l1);
                    }
                }
                if (l2 == null) {
                    l2 = new GpuWithholdingL2();
                    l2.setGpuCardType(temp.getGpuCardType());
                }
                l2.setCurGpuNumber(temp.getTotalWithholdNum());
            }
            if (l1 != null) {
                l1.setPreGpuNumber(ReportUtils.addAll(l1.getPreGpuNumber(), l2.getPreGpuNumber()));
                l1.setCurGpuNumber(ReportUtils.addAll(l1.getCurGpuNumber(), l2.getCurGpuNumber()));
                l1.getGpuCardDetails().add(l2);
            }
            return l2;
        });
        List<GpuWithholdingL1> ret = new ArrayList<>(l1Map.values());
        // 按当月预扣降序
        if (ListUtils.isNotEmpty(ret)) {
            ret.sort(ret.get(0));
        }
        for (GpuWithholdingL1 withholdingL1 : ret) {
            List<GpuWithholdingL2> gpuCardDetails = withholdingL1.getGpuCardDetails();
            if (ListUtils.isNotEmpty(gpuCardDetails)) {
                gpuCardDetails.sort(gpuCardDetails.get(0));
            }
        }
        return ret;
    }

    /**
     * 获取预扣
     *
     * @param req
     * @return
     */
    private List<NormalWithholdDetailItemVO> getWithholdDetailTable(IndustryResourceReq req,
                                                                    Consumer<NormalWithholdDetailTableReq> consumer) {
        NormalWithholdDetailTableReq tableReq = new NormalWithholdDetailTableReq();
        tableReq.setIndustryDept(ListUtils.newList(req.getIndustryDept()));
        tableReq.setProduct(ProductEnum.GPU.getName());
        tableReq.setUnit("卡数");
        tableReq.setQueryRange("计费用量");
        tableReq.setDims(ListUtils.newList(
                "industryDept",
                "warZone",
                "customerShortName",
                "gpuCardType"
        ));
        // 不设置 topN，这里自己处理
        tableReq.setTopN(null);
        tableReq.setCustomhouseTitle(req.getCustomhouseTitle());
        tableReq.setAppRole(ListUtils.newList(
                "EKS",
                "EMR",
                "正常售卖",
                "其他"
        ));
        consumer.accept(tableReq);
        NormalWithholdDetailResp withholdDetailTable = withholdService.getNormalWithholdDetailTable(tableReq);
        return withholdDetailTable.getDetailItemList();
    }


    /**
     * 获取GPU数据
     */
    public List<MrpV3DataItem> getGpuData(IndustryResourceReq req, Consumer<MrpV3ReportReq> consumer) {
        MrpV3ReportReqBuilder builder = new MrpV3ReportReqBuilder();
        MrpV3ReportReq v3ReportReq = builder.startYearMonth(req.getYearMonth())
                .endYearMonth(req.getYearMonth())
                .demandType(null)
                .customhouseTitle(req.getCustomhouseTitle())
                .category(MrpV3ReqCategoryEnum.GPU)
                .isDefault(true)
                .builder();
        v3ReportReq.setIndustryDept(ListUtils.newList(req.getIndustryDept()));
        // 默认有行业，通用客户简称，gpu卡型 维度
        List<String> dims = v3ReportReq.getDims();
        dims.add(MrpV3DimFieldEnum.industryDept.name());
        dims.add(MrpV3DimFieldEnum.unCustomerShortName.name());
        dims.add(MrpV3DimFieldEnum.gpuCardType.name());
        consumer.accept(v3ReportReq);
        MrpV3ReportResp mrpV3ReportResp = mrpV3ReportService.queryReport(v3ReportReq);
        return mrpV3ReportResp.getData();
    }

    @Override
    public Object getTopData(IndustryResourceWithTopReq req) {
        GpuProjectItem ret = self.doGetData(req.toIndustryResourceReq());

        Integer topNL1 = req.getTopNL1();
        Integer topNL2 = req.getTopNL2();

        // 1. 预扣 top 处理
        List<GpuWithholdingL1> withholding = ret.getWithholding();
        GpuWithholdingL1 other = new GpuWithholdingL1();
        other.setUnCustomerShortName(ReportUtils.OTHER);
        List<GpuWithholdingL1> topN = ReportUtils.getTopNIgnoreEmpty(withholding, topNL1, other, (o1, o2) -> {
            o1.setCurGpuNumber(ReportUtils.addAll(o1.getCurGpuNumber(), o2.getCurGpuNumber()));
            o1.setPreGpuNumber(ReportUtils.addAll(o1.getPreGpuNumber(), o2.getPreGpuNumber()));
        });

        for (GpuWithholdingL1 withholdingL1 : topN) {
            if (withholdingL1 != other){
                List<GpuWithholdingL2> gpuCardDetails = withholdingL1.getGpuCardDetails();
                if (ListUtils.isNotEmpty(gpuCardDetails)){
                    GpuWithholdingL2 l2Other = new GpuWithholdingL2();
                    l2Other.setGpuCardType(ReportUtils.OTHER);
                    List<GpuWithholdingL2> l2TopN = ReportUtils.getTopNIgnoreEmpty(gpuCardDetails, topNL2, l2Other, (o1, o2) -> {
                        o1.setCurGpuNumber(ReportUtils.addAll(o1.getCurGpuNumber(), o2.getCurGpuNumber()));
                        o1.setPreGpuNumber(ReportUtils.addAll(o1.getPreGpuNumber(), o2.getPreGpuNumber()));
                    });
                    withholdingL1.setGpuCardDetails(l2TopN);
                }
            }
        }

        ret.setWithholding(topN);

        // 2. GPU最新版预测新增 top 处理
        List<GpuNewestForecastL1> gpuNewestForecast = ret.getGpuNewestForecast();
        if (ListUtils.isNotEmpty(gpuNewestForecast)){
            gpuNewestForecast.sort(gpuNewestForecast.get(0));
            GpuNewestForecastL1 gpuNewestForecastOther = new GpuNewestForecastL1();
            gpuNewestForecastOther.setUnCustomerShortName(ReportUtils.OTHER);
            List<GpuNewestForecastL1> topN1 = ReportUtils.getTopNIgnoreEmpty(gpuNewestForecast, topNL1, gpuNewestForecastOther,
                    (o1, o2) -> o2.getGpuNumberMap().forEach(o1::addYearMonthValue));

            for (GpuNewestForecastL1 gpuNewestForecastL1 : topN1) {
                if (gpuNewestForecastL1 != gpuNewestForecastOther) {
                    List<GpuNewestForecastL2> gpuCardDetails = gpuNewestForecastL1.getGpuCardDetails();
                    if (ListUtils.isNotEmpty(gpuCardDetails)) {
                        gpuCardDetails.sort(gpuCardDetails.get(0));
                        GpuNewestForecastL2 l2Other = new GpuNewestForecastL2();
                        l2Other.setGpuCardType(ReportUtils.OTHER);
                        List<GpuNewestForecastL2> l2TopN = ReportUtils.getTopNIgnoreEmpty(gpuCardDetails, topNL2, l2Other,
                                (o1, o2) -> o2.getGpuNumberMap().forEach(o1::addYearMonthValue));
                        gpuNewestForecastL1.setGpuCardDetails(l2TopN);
                    }
                }
            }

            ret.setGpuNewestForecast(topN1);
        }

        // 3. gpu计费规模 top 处理
        List<GpuBillScaleL1> gpuBillScale = ret.getGpuBillScale();
        GpuBillScaleL1 gpuBillScaleOther = new GpuBillScaleL1();
        gpuBillScaleOther.setUnCustomerShortName(ReportUtils.OTHER);
        List<GpuBillScaleL1> topN2 = ReportUtils.getTopNIgnoreEmpty(gpuBillScale, topNL1, gpuBillScaleOther,(o1,o2) -> {
            o1.setCurGpuNumber(ReportUtils.addAll(o1.getCurGpuNumber(), o2.getCurGpuNumber()));
            o1.setPreGpuNumber(ReportUtils.addAll(o1.getPreGpuNumber(), o2.getPreGpuNumber()));
        });

        for (GpuBillScaleL1 gpuBillScaleL1 : topN2) {
            if (gpuBillScaleL1 != gpuBillScaleOther) {
                List<GpuBillScaleL2> gpuCardDetails = gpuBillScaleL1.getGpuCardDetails();
                if (ListUtils.isNotEmpty(gpuCardDetails)) {
                    GpuBillScaleL2 l2Other = new GpuBillScaleL2();
                    l2Other.setGpuCardType(ReportUtils.OTHER);
                    List<GpuBillScaleL2> l2TopN = ReportUtils.getTopNIgnoreEmpty(gpuCardDetails, topNL2, l2Other,
                            (o1, o2) -> {
                                o1.setCurGpuNumber(ReportUtils.addAll(o1.getCurGpuNumber(), o2.getCurGpuNumber()));
                                o1.setPreGpuNumber(ReportUtils.addAll(o1.getPreGpuNumber(), o2.getPreGpuNumber()));
                            });
                    gpuBillScaleL1.setGpuCardDetails(l2TopN);
                }
            }
        }

        ret.setGpuBillScale(topN2);

        // 4. gpu中长期 top 处理
        ret.setLongTerm(dealTopLongTerm(ret.getLongTerm(),req));

        return ret;
    }

    /**
     * 处理中长期 top（top gpu 卡型，按当年 gpu 预测总量降序）
     * @param longTerm 中长期数据
     * @param req 请求
     * @return
     */
    private GpuLongTerm dealTopLongTerm(GpuLongTerm longTerm, IndustryResourceWithTopReq req) {
        if (longTerm == null || ListUtils.isEmpty(longTerm.getItems())){
            return longTerm;
        }
        List<GpuLongTermL1> longTermL1 = longTerm.getItems();
        Integer topNL1 = req.getTopNL1();
        // 1.1 如果数据小于等于 topN，直接返回
        if (longTermL1.stream().map(GpuLongTermL1::getGpuCardType).collect(Collectors.toSet()).size() <= topNL1){
            return longTerm;
        }
        Map<String,Integer> topMap = new HashMap<>();
        int index = 0;
        // 1.2 获取 top N 的 gpu卡型（按 gpu 预测总量降序，预测量相等则用规模量排序）
        t:
        for (List<GpuLongTermL1> value : new TreeMap<>(ListUtils.groupBy(longTermL1, GpuLongTermL1::getYear)).values()) {
            List<GpuLongTermL1> data = value.stream()
                    .sorted((o1, o2) -> {
                        int i = SoeCommonUtils.compare2(o2.getTotalForecastGpuNum(), o1.getTotalForecastGpuNum());
                        if (i == 0) {
                            return SoeCommonUtils.compare2(o2.getTotalBillScaleGpuNum(), o1.getTotalBillScaleGpuNum());
                        } else {
                            return i;
                        }
                    }).collect(Collectors.toList());
            for (GpuLongTermL1 datum : data) {
                topMap.put(datum.getGpuCardType(), index++);
                if (index >= topNL1){
                    break t;
                }
            }
        }

        // 2. 聚合非 topN 的 gpu 卡型为其他
        List<GpuLongTermL1> ret = new ArrayList<>();
        Map<Integer, List<GpuLongTermL1>> map = ListUtils.groupBy(longTermL1, GpuLongTermL1::getYear);

        // 2.1 l2 merge 方法
        BiFunction<List<GpuLongTermL2>, List<GpuLongTermL2>, GpuLongTermL2>  mergeFunction = (ls1, ls2) -> {
            GpuLongTermL2 temp = new GpuLongTermL2();
            GpuLongTermL2 first;
            BigDecimal total = BigDecimal.ZERO;
            if (ListUtils.isNotEmpty(ls1)) {
                first = ls1.get(0);
                total = ls1.stream().map(GpuLongTermL2::getGpuNum)
                        .reduce(total, SoeCommonUtils::addAll);
            }else {
                first = ls2.get(0);
            }
            if (ListUtils.isNotEmpty(ls2)) {
                total = ls2.stream().map(GpuLongTermL2::getGpuNum)
                        .reduce(total, SoeCommonUtils::addAll);
            }
            temp.setLabel(first.getLabel());
            temp.setGpuNum(total);
            temp.setIsAllYear(first.getIsAllYear());
            return temp;
        };

        // 2.2 处理 topN 数据
        map.forEach((k,v)->{
            // 按照 topN gpu卡型排序
            v.sort(Comparator.comparing(o -> topMap.getOrDefault(o.getGpuCardType(), topNL1)));
            GpuLongTermL1 other = new GpuLongTermL1();
            other.setGpuCardType(ReportUtils.OTHER);
            other.setYear(k);
            other.setTotalBillScaleGpuNum(BigDecimal.ZERO);
            other.setTotalForecastGpuNum(BigDecimal.ZERO);
            other.setForecast(new ArrayList<>());
            other.setBillScale(new ArrayList<>());
            other.setProduct(Ppl13weekProductTypeEnum.GPU.getName());
            other.setIndustryDept(req.getIndustryDept());
            List<GpuLongTermL1> topN = ReportUtils.getTopN(v, topNL1, other, (o1, o2) -> {
                o1.setTotalForecastGpuNum(SoeCommonUtils.addAll(o1.getTotalForecastGpuNum(),o2.getTotalForecastGpuNum()));
                o1.setTotalBillScaleGpuNum(SoeCommonUtils.addAll(o1.getTotalBillScaleGpuNum(),o2.getTotalBillScaleGpuNum()));
                o1.setForecast(ListUtils.merge(o1.getForecast(), o2.getForecast(),
                        GpuLongTermL2::getLabel,
                        GpuLongTermL2::getLabel,
                        mergeFunction));
                o1.setBillScale(ListUtils.merge(o1.getBillScale(), o2.getBillScale(),
                        GpuLongTermL2::getLabel,
                        GpuLongTermL2::getLabel,
                        mergeFunction));
            });
            // 重新排序
            for (GpuLongTermL1 gpuLongTermL1 : topN) {
                gpuLongTermL1.sort();
            }
            // 填充合计
            GpuLongTermL1 total = new GpuLongTermL1();
            total.setGpuCardType("合计");
            total.setYear(k);
            total.setTotalBillScaleGpuNum(BigDecimal.ZERO);
            total.setTotalForecastGpuNum(BigDecimal.ZERO);
            total.setForecast(new ArrayList<>());
            total.setBillScale(new ArrayList<>());
            total.setProduct(Ppl13weekProductTypeEnum.GPU.getName());
            total.setIndustryDept(req.getIndustryDept());
            ReportUtils.getTopN(v, 0, total, (o1, o2) -> {
                o1.setTotalForecastGpuNum(SoeCommonUtils.addAll(o1.getTotalForecastGpuNum(),o2.getTotalForecastGpuNum()));
                o1.setTotalBillScaleGpuNum(SoeCommonUtils.addAll(o1.getTotalBillScaleGpuNum(),o2.getTotalBillScaleGpuNum()));
                o1.setForecast(ListUtils.merge(o1.getForecast(), o2.getForecast(),
                        GpuLongTermL2::getLabel,
                        GpuLongTermL2::getLabel,
                        mergeFunction));
                o1.setBillScale(ListUtils.merge(o1.getBillScale(), o2.getBillScale(),
                        GpuLongTermL2::getLabel,
                        GpuLongTermL2::getLabel,
                        mergeFunction));
            });
            ret.addAll(topN);
            ret.add(total);
        });
        longTerm.setItems(ret);

        return longTerm;
    }
}
