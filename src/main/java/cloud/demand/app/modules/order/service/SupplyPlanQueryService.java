package cloud.demand.app.modules.order.service;

import cloud.demand.app.modules.order.dto.req.CreatePreDeductPlanReq.PreDeductItemForCreateChoose;
import cloud.demand.app.modules.order.dto.resp.ConsensusDemandDetailDTO;
import cloud.demand.app.modules.order.dto.SupplyPlanMatchWayDTO;
import cloud.demand.app.modules.order.dto.resp.OrderDemandDetailForWaitSupplyResp;
import cloud.demand.app.modules.order.dto.resp.OrderItemDTO;
import cloud.demand.app.modules.order.dto.resp.OrderItemWithSupplyDetail;
import cloud.demand.app.modules.order.dto.resp.OrderSupplyPlanDetailWithPlanDTO;
import cloud.demand.app.modules.order.dto.resp.OrderSupplyPlanWithDetailDTO;
import cloud.demand.app.modules.order.dto.resp.SupplyDetailWithOrderItemDTO;
import cloud.demand.app.modules.order.dto.resp.SupplyPlanVersionVO;
import cloud.demand.app.modules.order.dto.resp.supply.DeliverRecordDTO;
import cloud.demand.app.modules.order.dto.resp.supply.DeliverRecordDTO.DeliverItem;
import cloud.demand.app.modules.order.dto.resp.supply.DeliverTrackAndRiskForecastDTO;
import cloud.demand.app.modules.order.dto.resp.supply.RiskForecastDTO;
import cloud.demand.app.modules.order.dto.resp.supply.process.ActualSupplDateGetter;
import cloud.demand.app.modules.order.dto.resp.supply.process.MoveResp.MoveInfo;
import cloud.demand.app.modules.order.dto.resp.supply.process.OrderSupplyProcessVO;
import cloud.demand.app.modules.order.dto.resp.supply.process.QuotaSupplyDetailResp.QuotaSupplyDetailVO;
import cloud.demand.app.modules.order.entity.OrderInfoDO;
import cloud.demand.app.modules.order.entity.OrderSupplyPlanDO;
import cloud.demand.app.modules.order.entity.OrderSupplyPlanDetailDO;
import cloud.demand.app.modules.order.entity.OrderSupplyPlanVersionDO;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

public interface SupplyPlanQueryService {

    /**
     *  获取待处理的方案版本
     */
    OrderSupplyPlanVersionDO getWaitingHandleVersion(String orderNumber);

    OrderSupplyPlanVersionDO getNewestVersion(String orderNumber, String flowNo);

    OrderSupplyPlanVersionDO getAvailableVersion(String orderNumber);

    List<OrderSupplyPlanDO> getAllSupplyPlan(long planVersionId, String orderNumber);

    List<OrderSupplyPlanDetailWithPlanDTO> getListDetailWithPlan(List<Long> detailIds);

    List<OrderSupplyPlanDetailWithPlanDTO> getListDetailWithPlanByVersionId(Long versionId, String orderNumber);

    List<OrderSupplyPlanWithDetailDTO> getAllSupplyPlanWithDetails(long planVersionId, String orderNumber);

    /**
     * 通过单号查询订单绑定了哪些供应方案明细
     * @param bizIds
     * @return 业务单号2供应方案明细
     */
    Map<String, List<OrderSupplyPlanDetailDO>> getSupplyPlanDetailBySupplyBizId(List<String> bizIds);

    /**
     * 通过订单明细号查询采购供应方案
     *
     * @param matchTypeCode
     * @param orderNumberList
     * @return
     */
    List<OrderSupplyPlanDetailDO> querySupplyPlanDetailByOrderNumber(String matchTypeCode,List<String> orderNumberList);

    /**
     * 通过单号查询订单绑定了哪些供应方案明细
     * @param bizIds
     * @return 业务单号2供应方案明细
     */
    Map<String, String> getOrderNumberBySupplyBizId(List<String> bizIds);

    /**
     *  获取 Q单 交付明细
     * @param bizId erp 的业务单号，Q单
     */
    List<QuotaSupplyDetailVO> queryQuotaDetail(String bizId);

    /**
     *  获取 M单 交付明细
     * @param bizId erp 的业务单号，M单
     */
    List<MoveInfo> querySmoveDetail(String bizId);

    /**
     *  从 erp 接口获取资源准备进度
     * @param bizOrders erp 的业务单号，Q单、M单
     */
    Map<String, OrderSupplyProcessVO> callErpForSupplyProcess(List<String> bizOrders, Integer supplyCore);

    /**
     * 获取需求明细待分、已分配供应方案信息
     *
     * @param orderNumber 订单号，非空
     * @param supplyPlanFlowNo 供应方案制定子流程流水号， 非空
     * @return
     */
    OrderDemandDetailForWaitSupplyResp demandDetailForWaitSupply(String orderNumber, String supplyPlanFlowNo);


    /**
     * 获取需求明细待分、已分配供应方案信息 for 数据库
     * @param orderInfoDO
     * @return
     */
    List<OrderItemDTO> demandDetailForWaitSupplyForDataBase(OrderInfoDO orderInfoDO);

    /**
     * 获取共识列表数据
     *
     * @param orderNumber
     * @param supplyPlanFlowNo
     * @return
     */
    List<OrderItemWithSupplyDetail> queryConsensusList(String orderNumber, String supplyPlanFlowNo);

    /**
     *  查询供应方案版本列表
     * @param orderNumber 订单号
     */
    List<SupplyPlanVersionVO> planVersionList(String orderNumber);

    /**
     *  根据订单记录查询方案制定的可处理人
     * @param orderNumber 订单号
     */
    List<String> queryPlanProcessorByOrderRecord(String orderNumber);

    OrderSupplyPlanVersionDO queryVersionById(Long versionId);

    /**
     *  查询生效的方案明细信息
     * @param orderNumber 订单号
     */
    List<OrderSupplyPlanDetailWithPlanDTO> getAvailableDetailWithPlan(String orderNumber);

    /**
     *  查询带订单明细信息的生效的方案明细信息
     * @param orderNumber 订单号
     */
    List<SupplyDetailWithOrderItemDTO> getAvailableSupplyDetailWithOrderItem(String orderNumber);

    /**
     *  用户申请预扣时的可勾选进行预扣的数据，根据供应方案（相当于共识需求）生成、预扣数据生成
     * @param orderNumber 订单号
     */
    List<PreDeductItemForCreateChoose> preDeductItemForCreateChoose(String orderNumber,boolean isWeekMonthElastic);

    /**
     *  交付记录追踪，从Q单、M单获取相关交付信息，仅支持采购满足、搬迁满足
     * @param planId {@link OrderSupplyPlanDO#getId()}
     */
    List<DeliverRecordDTO> deliverTrack(Long planId);

    /**
     *  交付记录跟踪信息、风险预估信息，支持大盘满足、采购满足、搬迁满足
     * @param planId {@link OrderSupplyPlanDO#getId()}
     */
    DeliverTrackAndRiskForecastDTO deliverTrackAndRiskForecast(Long planId);

    /**
     *  用于供应方案制定时，预览交付记录跟踪、风险预估信息. <br/>
     *  交付记录跟踪信息、风险预估信息，支持大盘满足、采购满足、搬迁满足
     * @param planData 供应方案制定时的方案数据
     */
    DeliverTrackAndRiskForecastDTO deliverTrackAndRiskForecast(SupplyPlanMatchWayDTO planData);

    /**
     * 查询生效的共识需求明细信息
     * @param orderNumber 订单号
     */
    List<ConsensusDemandDetailDTO> getAvailableConsensusDemandDetail(String orderNumber);

}
