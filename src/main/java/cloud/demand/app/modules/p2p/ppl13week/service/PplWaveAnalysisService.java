package cloud.demand.app.modules.p2p.ppl13week.service;

import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.QueryVersionInfoRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.QueryWaveReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.detail.QueryIndustryDeptCustomerRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.detail.QueryWaveDetailRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.overview.QueryWaveOverviewDetailRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.overview.QueryWaveOverviewGroupRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.overview.QueryWaveOverviewRsp;
import cloud.demand.app.web.model.common.DownloadBean;
import java.util.List;
import java.util.Map;

/**
 * PPL波动分析相关
 */
public interface PplWaveAnalysisService {

    /**
     * 波动分析概览主树状查询接口
     */
    QueryWaveOverviewRsp compareAnalysis(QueryWaveReq req);

    /**
     * 波动分析概览（BY各个维度查询接口）
     */
    QueryWaveOverviewGroupRsp waveGroupOverview(QueryWaveReq req);

    /**
     * 波动分析概览By维度明细
     */
    QueryWaveOverviewDetailRsp waveGroupDetail(QueryWaveReq req);

    /**
     * 波动分析明细波动分析
     */
    QueryWaveDetailRsp waveDetail(QueryWaveReq req);

    /**
     * 波动分析明细波动分析，导出明细数据
     */
    DownloadBean exportWaveDetail(QueryWaveReq req);

    /**
     * 查询最新版本信息
     */
    QueryVersionInfoRsp queryNewestVersionInfo();

    /**
     * 查询行业-分类（供波动分析明细筛选框使用）
     *
     * @return
     */
    List<Map<String, Object>> queryAllIndustryDeptForWaveDetail();

    /**
     * 查询行业部门-客户的树
     */
    QueryIndustryDeptCustomerRsp queryIndustryDeptCustomerTree(QueryWaveReq req);

    /**
     * 查询产品大类-产品 (供波动分析明细筛选框使用)
     */
    Map<String, List<String>> queryWaveProductCategory();

}