package cloud.demand.app.modules.p2p.ppl13week.entity;

import cloud.demand.app.common.BaseDO;
import cloud.demand.app.modules.p2p.ppl13week.constant.ConsensusConstant;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplConsensusStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplCosAZEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplCosStorageEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDataBaseFrameworkEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDatabaseEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDatabaseStorageEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDeloyTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.listener.tool.FieldComparator.ChineseName;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.wooutils.collect.ListUtils;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.StringJoiner;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.nutz.lang.Strings;

@Data
@ToString
@Table("ppl_version_group_record_item")
public class PplVersionGroupRecordItemDO extends BaseDO {

    /**
     * 版本group id<br/>Column: [version_group_id]
     */
    @Column(value = "version_group_id")
    private Long versionGroupId;

    /**
     * ppl_version_group_record的版本<br/>Column: [record_version]
     */
    @Column(value = "record_version")
    private Integer recordVersion;

    /**
     * 版本group record id<br/>Column: [version_group_record_id]
     */
    @Column(value = "version_group_record_id")
    private Long versionGroupRecordId;

    /**
     * ppl单号<br/>Column: [ppl_order]
     */
    @Column(value = "ppl_order")
    private String pplOrder;

    /**
     * ppl需求id<br/>Column: [ppl_id]
     */
    @Column(value = "ppl_id")
    private String pplId;

    /**
     * 母单id（拆单时设置）
     */
    @Column(value = "parent_ppl_id")
    private String parentPplId;

    /**
     * ppl需求审批通过时的状态<br/>Column: [status]
     */
    @Column(value = "status")
    private String status;

    /**
     * 产品<br/>Column: [product]
     */
    @Column(value = "product")
    private String product;

    /**
     * 需求类型<br/>Column: [demand_type]
     */
    @Column(value = "demand_type")
    private String demandType;

    /**
     * 需求场景<br/>Column: [demand_scene]
     */
    @Column(value = "demand_scene")
    private String demandScene;

    /**
     * 项目名称<br/>Column: [project_name]
     */
    @Column(value = "project_name")
    private String projectName;

    /**
     * 计费模式<br/>Column: [bill_type]
     */
    @Column(value = "bill_type")
    private String billType;

    /**
     * 是否重保需求，当前仅GPU产品需求这个字段，重保需求盈率固定为100%。备货策略：重保需求按100%备货，非重保需求按量级*赢率进行备货
     */
    @Column(value = "important_demand")
    private Boolean importantDemand;

    /**
     * 赢率，百分比<br/>Column: [win_rate]
     */
    @Column(value = "win_rate")
    private BigDecimal winRate;

    /**
     * 开始购买日期<br/>Column: [begin_buy_date]
     */
    @Column(value = "begin_buy_date")
    private LocalDate beginBuyDate;

    /**
     * 结束购买日期<br/>Column: [end_buy_date]
     */
    @Column(value = "end_buy_date")
    private LocalDate endBuyDate;

    /**
     * 弹性开始日期<br/>Column: [begin_elastic_date]
     */
    @Column(value = "begin_elastic_date")
    private LocalTime beginElasticDate;

    /**
     * 弹性结束日期<br/>Column: [end_elastic_date]
     */
    @Column(value = "end_elastic_date")
    private LocalTime endElasticDate;

    /**
     * 特殊备注说明<br/>Column: [note]
     */
    @Column(value = "note")
    private String note;

    /**
     * 地域，看看要不要存id<br/>Column: [region_name]
     */
    @Column(value = "region_name")
    private String regionName;

    /**
     * 可用区，看看要不要存id<br/>Column: [zone_name]
     */
    @Column(value = "zone_name")
    private String zoneName;


    /**
     * 是否强制指定可用区<br/>Column: [is_strong_designate_zone]
     * 行业用户输入PPL： 新增需求 自己指定， 退回只能为true（历史数据可能存在false），
     * 模型预测： 新增/退回  均为false
     * 云梯公有池：  新增 根据可用区是否模糊决定 true or false， 退回只能为true
     */
    @Column(value = "is_strong_designate_zone", insertValueScript = "0")
    private Boolean isStrongDesignateZone;

    /**
     * 实例类型<br/>Column: [instance_type]
     */
    @Column(value = "instance_type")
    private String instanceType;

    /**
     * 实例规格<br/>Column: [instance_model]
     */
    @Column(value = "instance_model")
    private String instanceModel;

    /**
     * 实例数量<br/>Column: [instance_num]
     */
    @Column(value = "instance_num")
    private Integer instanceNum;

    /**
     * 实例总核心数<br/>Column: [total_core]
     */
    @Column(value = "total_core")
    private Integer totalCore;


    /**
     * 预约的实例数量<br/>Column: [instance_num]
     */
    @Column(value = "apply_instance_num")
    private Integer applyInstanceNum;

    /**
     * 预约的实例总核心数<br/>Column: [total_core]
     */
    @Column(value = "apply_total_core")
    private Integer applyTotalCore;


    /**
     * 关联的云霄单号<br/>Column: [yunxiao_order_id]
     */
    @Column(value = "yunxiao_order_id", maxStringLength = 1000)
    private String yunxiaoOrderId;


    /**
     * 可接受的其它实例类型，分号隔开<br/>Column: [alternative_instance_type]
     */
    @Column(value = "alternative_instance_type", insertValueScript = "''")
    private String alternativeInstanceType;

    /**
     * 备选可用区，分号隔开<br/>Column: [alternative_zone_name]
     */
    @Column(value = "alternative_zone_name")
    @ChineseName("备选可用区")
    private String alternativeZoneName;

    /**
     * 亲和度类型<br/>Column: [affinity_type]
     */
    @Column(value = "affinity_type")
    private String affinityType;

    /**
     * 亲和度值<br/>Column: [affinity_value]
     */
    @Column(value = "affinity_value")
    private BigDecimal affinityValue;

    /**
     * 磁盘类型<br/>Column: [system_disk_type]
     */
    @Column(value = "system_disk_type")
    private String systemDiskType;

    /**
     * 磁盘容量，单位G<br/>Column: [system_disk_storage]
     */
    @Column(value = "system_disk_storage")
    private Integer systemDiskStorage;

    /**
     * 磁盘块数<br/>Column: [system_disk_num]
     */
    @Column(value = "system_disk_num")
    private Integer systemDiskNum;

    /**
     * 磁盘类型<br/>Column: [data_disk_type]
     */
    @Column(value = "data_disk_type")
    private String dataDiskType;

    /**
     * 磁盘容量，单位G<br/>Column: [data_disk_storage]
     */
    @Column(value = "data_disk_storage")
    private Integer dataDiskStorage;

    /**
     * 磁盘块数<br/>Column: [data_disk_num]
     */
    @Column(value = "data_disk_num")
    private Integer dataDiskNum;

    /**
     * creator<br/>Column: [data_disk_num]
     */
    @Column(value = "creator")
    private String creator;

    /**
     * 总磁盘数数<br/>Column: [total_core]
     */
    @Column(value = "total_disk", insertValueScript = "0")
    private Integer totalDisk;

    /**
     * 录入类型<br/>Column: [importType]
     */
    @Column(value = "import_type", insertValueScript = "''")
    private String importType;

    /**
     * gpu产品形态 cvm/裸金属 <br/>Column: [gpuProductType]
     */
    @Column(value = "gpu_product_type")
    private String gpuProductType;

    /**
     * gpu型号 <br/>Column: [gpuType]
     */
    @Column(value = "gpu_type")
    private String gpuType;

    /**
     * gpu卡数 <br/>Column: [gpuNum]
     */
    @Column(value = "gpu_num")
    private BigDecimal gpuNum;

    /**
     * gpu总卡数 <br/>Column: [totalGpuNum]
     */
    @Column(value = "total_gpu_num")
    private BigDecimal totalGpuNum;

    /**
     * 是否接受其他卡型<br/>Column: [isAcceptAdjust]
     */
    @Column(value = "is_accept_adjust")
    private Boolean isAcceptAdjust;

    /**
     * 接受GPU卡型 用;分割<br/>Column: [acceptGpu]
     */
    @Column(value = "accept_gpu")
    private String acceptGpu;

    /**
     * 业务场景<br/>Column: [bizScene]
     */
    @Column(value = "biz_scene")
    private String bizScene;

    /**
     * 业务详情<br/>Column: [bizDetail]
     */
    @Column(value = "biz_detail")
    private String bizDetail;

    /**
     * 使用时长<br/>Column: [serviceTime]
     */
    @Column(value = "service_time")
    private String serviceTime;

    @Column(value = "forecast_model_detail_id")
    private Long forecastModelDetailId;

    @Column(value = "is_comd", insertValueScript = "0")
    private Boolean isComd;

    @Column(value = "source_ppl_id")
    private String sourcePplId;

    /**
     * 包销时长（月）- 现废弃使用
     */
    @Column(value = "sale_duration")
    private Integer saleDuration;

    /**
     * 是否CPQ（GPU相关）- 现废弃使用
     */
    @Column(value = "cpq")
    private Boolean cpq;

    /**
     * 包销时长(年)（允许一位小数点，excel导入时最小填写单位为0.5）
     */
    @Column(value = "sale_duration_year")
    private BigDecimal saleDurationYear;

    /**
     * 申请折扣(折)（填写范围为0.1~10）
     */
    @Column(value = "apply_discount")
    private BigDecimal applyDiscount;

    /**
     * 商务进展（枚举值-CpqTypeEnum）
     */
    @Column(value = "business_cpq")
    private String businessCpq;

    @Column("consensus_status")
    private String consensusStatus;

    /**
     * 共识的满足方式,多个满足方式用英文分号隔开
     * 分隔符：{@link cloud.demand.app.modules.p2p.ppl13week.constant.ConsensusConstant#CONSENSUS_DELIMITER}
     *
     * @see cloud.demand.app.modules.p2p.ppl13week.enums.stock_supply.PplStockSupplyMatchTypeEnum
     */
    @Column("consensus_match_type")
    private String consensusMatchType;

    @Column(value = "instance_num_apply_after", insertValueScript = "0")
    private Integer instanceNumApplyAfter;

    @Column(value = "total_core_apply_after", insertValueScript = "0")
    private Integer totalCoreApplyAfter;

    @Column(value = "total_gpu_num_apply_after", insertValueScript = "0")
    private BigDecimal totalGpuNumApplyAfter;

    /**
     * 占用其他客户的需求量(cpu核心数或者GPU卡数)
     */
    @Column("occupy_others_core_num")
    private Integer occupyOthersCoreNum;

    /**
     * 占用了哪些pplID的需求量，多个pplID用英文逗号({@code ,})分割
     */
    @Column("occupy_others_ppl_ids")
    private String occupyOthersPplIds;

    /**
     * 被占用的需求核心数
     */
    @Column("occupied_core_num")
    private Integer occupiedCoreNum;

    /**
     * 被哪些pplId占用了需求核心数，多个pplID用英文逗号({@code ,})分割
     */
    @Column("occupied_by_ppl_ids")
    private String occupiedByPplIds;

    /**
     * 是否已锁单
     */
    @Column(value = "is_lock", insertValueScript = "0")
    private Boolean isLock;

    /**
     *  行业共识的需求满足日期,多个满足方式用英文分号隔开,单个日期格式 yyyy-MM-dd
     *  分隔符：{@link cloud.demand.app.modules.p2p.ppl13week.constant.ConsensusConstant#CONSENSUS_DELIMITER}
     */
    @Column(value = "consensus_demand_date")
    private String consensusDemandDate;

    /**
     *  行业共识的实例类型（例S5）,多个满足方式用英文分号隔开
     *  分隔符：{@link cloud.demand.app.modules.p2p.ppl13week.constant.ConsensusConstant#CONSENSUS_DELIMITER}
     */
    @Column(value = "consensus_instance_type")
    private String consensusInstanceType;

    /**
     *  行业共识的可用区,多个满足方式用英文分号隔开
     *  分隔符：{@link cloud.demand.app.modules.p2p.ppl13week.constant.ConsensusConstant#CONSENSUS_DELIMITER}
     */
    @Column(value = "consensus_zone_name")
    private String consensusZoneName;

    /**
     *  CBS单实例IO(MB/s)
     */
    @Column(value = "cbs_io")
    private Integer cbsIo;

    /**
     *  项目类型
     */
    @Column(value = "is_spike")
    private Integer isSpike;

    /**
     * CBS项目类型
     */
    @Column(value = "cbs_is_spike")
    private Integer cbsIsSpike;

    /**
     * 置放群组，多个置放群组用英文分号隔开<br/>Column: [placement_group]
     */
    @Column(value = "placement_group")
    private String placementGroup;

    /**
     * 数据库名称
     * @see PplDatabaseEnum
     */
    @Column(value = "database_name")
    @ChineseName("数据库名称")
    private String databaseName;

    /**
     *  是否多AZ，数据库产品
     */
    @Column(value = "more_than_one_az")
    private Boolean moreThanOneAZ;

    /**
     * 数据库存储类型，数据库产品
     * @see PplDatabaseStorageEnum
     */
    @Column(value = "database_storage_type")
    @ChineseName("数据库存储类型")
    private String databaseStorageType;

    /**
     * 实例部署类型，数据库产品
     * @see PplDeloyTypeEnum
     */
    @Column(value = "deploy_type")
    @ChineseName("实例部署类型")
    private String deployType;

    /**
     * 实例架构类型，数据库产品
     * @see PplDataBaseFrameworkEnum
     */
    @Column(value = "framework_type")
    @ChineseName("实例架构类型")
    private String frameworkType;

    /**
     *  分片数量，数据库产品
     */
    @Column(value = "slice_num")
    @ChineseName("分片数量")
    private Integer sliceNum;

    /**
     *  副本数量，数据库产品
     */
    @Column(value = "replica_num")
    @ChineseName("副本数量")
    private Integer replicaNum;

    /**
     *  只读数量，数据库产品
     */
    @Column(value = "read_only_num")
    @ChineseName("只读数量")
    private Integer readOnlyNum;

    /**
     *  数据库实例规格（2C16G 这种），数据库产品
     */
    @Column(value = "database_specs")
    @ChineseName("数据库实例规格")
    private String databaseSpecs;

    /**
     *  数据库存储量，单位GB，数据库产品
     */
    @Column(value = "database_storage")
    private BigDecimal databaseStorage;

    /**
     *  总数据库存储量，单位GB，数据库产品
     */
    @Column(value = "total_database_storage")
    @ChineseName("总数据库存储量")
    private BigDecimal totalDatabaseStorage;

    /**
     *  COS存储类型，COS产品
     * @see PplCosStorageEnum
     */
    @Column(value = "cos_storage_type")
    @ChineseName("COS存储类型")
    private String cosStorageType;

    /**
     *  单AZ、多AZ，COS产品
     * @see PplCosAZEnum
     */
    @Column(value = "cos_az")
    private String cosAZ;

    /**
     *  COS存储量，单位PB，COS产品
     */
    @Column(value = "cos_storage")
    private BigDecimal cosStorage;

    /**
     *  带宽，单位 Gbit/s，COS产品
     */
    @Column(value = "bandwidth")
    private Integer bandwidth;

    /**
     * qps，COS产品
     */
    @Column(value = "qps")
    private Integer qps;

    /**
     *  总COS存储量，单位PB，COS产品
     */
    @Column(value = "total_cos_storage")
    @ChineseName("总COS存储量")
    private BigDecimal totalCosStorage;

    /**
     * 单台核心数<br/>
     */
    @Column(value = "instance_model_core_num")
    private Integer instanceModelCoreNum;

    /**
     * 单台内存数，单位GB<br/>
     */
    @Column(value = "instance_model_ram_num")
    private Integer instanceModelRamNum;

    /**
     * 总内存数，单位GB<br/>Column: [total_memory]
     */
    @Column(value = "total_memory")
    @ChineseName("总内存数")
    private Integer totalMemory;

    /**
     *  从数据库产品实例规格获取单台核心数和内存
     * @param databaseSpecs 数据库产品实例规格 （2C8G 这样）
     * @return left：核心数，right：内存
     */
    public static Tuple2<Integer, Integer> parseDatabaseSpecs(String databaseSpecs) {
        if (Strings.isBlank(databaseSpecs)) {
            return Tuple.of(null, null);
        }
        Pattern pattern = Pattern.compile("^\\d+C\\d+G$");
        Matcher matcher = pattern.matcher(databaseSpecs);
        // 判断是否匹配
        if (matcher.matches()) {
            // 提取数字
            String[] numbers = databaseSpecs.split("[CG]");
            return Tuple.of(Integer.parseInt(numbers[0]), Integer.parseInt(numbers[1]));
        }
        return Tuple.of(null, null);

    }

    /**
     * 获取置放群组列表
     */
    public List<String> placementGroupListGet() {
        if (Strings.isBlank(this.placementGroup)) {
            return new ArrayList<>();
        }
        String[] array = this.placementGroup.split(";");
        return ListUtils.newArrayList(array);
    }

    /**
     * 设置置放群组，置放群组用英文分号拼接
     *
     * @param placementGroupList 置放群组
     */
    public void placementGroupSet(List<String> placementGroupList) {
        if (ListUtils.isEmpty(placementGroupList)) {
            this.setPlacementGroup(null);
            return;
        }
        // 置放群组用英文分号拼接
        StringJoiner joiner = new StringJoiner(";");
        placementGroupList.forEach(joiner::add);
        this.setPlacementGroup(joiner.toString());
    }

    public void addOccupyOthersPplId(String occupyPplId) {
        if (Strings.isBlank(occupyPplId)) {
            return;
        }
        if (Strings.isBlank(this.occupyOthersPplIds)) {
            this.occupyOthersPplIds = occupyPplId;
        } else {
            this.occupyOthersPplIds = this.occupyOthersPplIds + "," + occupyPplId;
        }
    }

    public void addOccupyOthersNum(Integer occupyNum) {
        if (occupyNum == null) {
            return;
        }
        int num = this.occupyOthersCoreNum == null ? 0 : this.occupyOthersCoreNum;
        this.occupyOthersCoreNum = num + occupyNum;
    }

    public void addOccupiedNum(Integer occupied) {
        if (occupied == null) {
            return;
        }
        int num = this.occupiedCoreNum == null ? 0 : this.occupiedCoreNum;
        this.occupiedCoreNum = num + occupied;
    }

    public void addOccupiedByPplId(String occupiedPplId) {
        if (Strings.isBlank(occupiedPplId)) {
            return;
        }
        if (Strings.isBlank(this.occupiedByPplIds)) {
            this.occupiedByPplIds = occupiedPplId;
        } else {
            this.occupiedByPplIds = this.occupiedByPplIds + "," + occupiedPplId;
        }
    }

    /**
     *   共识信息
     */
    public void calcAnSetConsensusInfo(List<PplSupplyConsensusDO> consensusList) {
        if (ListUtils.isEmpty(consensusList)) {
            // 没有供应方案时，设置默认的共识信息
            this.consensusStatus = PplConsensusStatusEnum.NOT_CONSENSUS.getCode();;
            this.consensusInstanceType = this.instanceType;
            this.consensusZoneName = this.zoneName;
            if (this.beginBuyDate != null) {
                this.consensusDemandDate = LocalDateTimeUtil
                        .format(this.beginBuyDate, DateTimeFormatter.ISO_LOCAL_DATE);
            }
            return;
        }
        String consensusStatus = null;
        Set<String> consensusMatchTypeSet = new HashSet<>();
        Set<String> consensusInstanceTypeSet = new HashSet<>();
        Set<String> consensusZoneNameSet = new HashSet<>();
        Set<String> consensusDemandDateSet = new HashSet<>();
        PplConsensusStatusEnum accept = null;
        PplConsensusStatusEnum refuse = null;
        for (PplSupplyConsensusDO item : consensusList) {
            if (item == null) {
                continue;
            }
            if (StringUtils.isNotBlank(item.getConsensusStatus())) {
                consensusStatus = item.getConsensusStatus();
                PplConsensusStatusEnum status = PplConsensusStatusEnum.getByCode(item.getConsensusStatus());
                if (status != null) {
                    if (status.equals(PplConsensusStatusEnum.ACCEPT_CONSENSUS)) {
                        accept = status;
                    } else if (status.equals(PplConsensusStatusEnum.REFUSE_CONSENSUS)) {
                        refuse = status;
                    }
                }
            }
            if (StringUtils.isNotBlank(item.getMatchType())) {
                consensusMatchTypeSet.add(item.getMatchType());
            }
            if (StringUtils.isNotBlank(item.getConsensusInstanceType())) {
                consensusInstanceTypeSet.add(item.getConsensusInstanceType());
            }
            if (StringUtils.isNotBlank(item.getConsensusZoneName())) {
                consensusZoneNameSet.add(item.getConsensusZoneName());
            }
            if (item.getConsensusDemandDate() != null) {
                consensusDemandDateSet.add(
                        LocalDateTimeUtil.format(item.getConsensusDemandDate(), DateTimeFormatter.ISO_LOCAL_DATE));
            }
        }
        this.consensusStatus = consensusStatus;
        if (accept != null) {
            this.consensusStatus = accept.getCode();
        }
        if (refuse != null) {
            this.consensusStatus = refuse.getCode();
        }
        if (StringUtils.isBlank(this.consensusStatus)) {
            this.consensusStatus = PplConsensusStatusEnum.NOT_CONSENSUS.getCode();
        }

        StringJoiner consensusMatchType = new StringJoiner(ConsensusConstant.CONSENSUS_DELIMITER);
        consensusMatchTypeSet.forEach(consensusMatchType::add);
        this.consensusMatchType = consensusMatchType.toString();

        StringJoiner consensusInstanceType = new StringJoiner(ConsensusConstant.CONSENSUS_DELIMITER);
        consensusInstanceTypeSet.forEach(consensusInstanceType::add);
        this.consensusInstanceType = consensusInstanceType.toString();

        StringJoiner consensusZoneName = new StringJoiner(ConsensusConstant.CONSENSUS_DELIMITER);
        consensusZoneNameSet.forEach(consensusZoneName::add);
        this.consensusZoneName = consensusZoneName.toString();

        StringJoiner consensusDemandDate = new StringJoiner(ConsensusConstant.CONSENSUS_DELIMITER);
        consensusDemandDateSet.forEach(consensusDemandDate::add);
        this.consensusDemandDate = consensusDemandDate.toString();
    }
}