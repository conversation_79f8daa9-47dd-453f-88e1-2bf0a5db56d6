package cloud.demand.app.modules.p2p.ppl13week.entity.std_table;

import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplCosAZEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplCosStorageEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDataBaseFrameworkEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDatabaseEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDatabaseStorageEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDeloyTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.listener.tool.FieldComparator.ChineseName;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.CategoryFiller;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.CommonCustomerShortNameFiller;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.CommonInstanceTypeFiller;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.CustomerInfoFiller;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.CustomerTabTypeForPplFiller;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.StrategyGroupNameFiller;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.ZoneInfoFiller;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("dwd_crp_ppl_item_cf")
public class DwdCrpPplItemCfDO implements CustomerInfoFiller, ZoneInfoFiller, CustomerTabTypeForPplFiller,
        StrategyGroupNameFiller, CategoryFiller, SumTotalCoreOrGpu, CommonCustomerShortNameFiller,
        CommonInstanceTypeFiller {

    /**
     * pplId,唯一<br/>Column: [ppl_id]
     */
    @Column(value = "ppl_id")
    private String pplId;

    /**
     * ppl单号<br/>Column: [ppl_order]
     */
    @Column(value = "ppl_order")
    private String pplOrder;

    /**
     * ppl生成时间<br/>Column: [create_time]
     */
    @Column(value = "create_time")
    private Date createTime;

    /**
     * ppl更新时间<br/>Column: [update_time]
     */
    @Column(value = "update_time")
    private Date updateTime;

    /**
     * ppl状态，VALID已生效，APPLIED已预约<br/>Column: [status]
     */
    @Column(value = "status")
    private String status;

    /**
     * ppl状态名称<br/>Column: [status_name]
     */
    @Column(value = "status_name")
    private String statusName;

    /**
     * ppl需求台数<br/>Column: [instance_num]
     */
    @Column(value = "instance_num")
    private Integer instanceNum;

    /**
     * ppl需求总核心数<br/>Column: [total_core]
     */
    @Column(value = "total_core")
    private Integer totalCore;

    /**
     * ppl来源<br/>Column: [source]
     */
    @Column(value = "source")
    private String source;

    /**
     * ppl来源名称<br/>Column: [source_name]
     */
    @Column(value = "source_name")
    private String sourceName;

    /**
     * ppl需求-年<br/>Column: [year]
     */
    @Column(value = "year")
    private Integer year;

    /**
     * ppl需求-月<br/>Column: [month]
     */
    @Column(value = "month")
    private Integer month;

    /**
     * 客户uin<br/>Column: [customer_uin]
     */
    @Column(value = "customer_uin")
    private String customerUin;

    /**
     * 行业<br/>Column: [industry]
     */
    @Column(value = "industry")
    private String industry;

    /**
     * 行业部门<br/>Column: [industry_dept]
     */
    @Column(value = "industry_dept")
    private String industryDept;

    /**
     * 客户类型<br/>Column: [customer_type]
     */
    @Column(value = "customer_type")
    private String customerType;

    /**
     * 客户类型名称<br/>Column: [customer_type_name]
     */
    @Column(value = "customer_type_name")
    private String customerTypeName;

    /**
     * 客户名称<br/>Column: [customer_name]
     */
    @Column(value = "customer_name")
    private String customerName;

    /**
     * 客户简称<br/>Column: [customer_short_name]
     */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /**
     * 战区<br/>Column: [war_zone]
     */
    @Column(value = "war_zone")
    private String warZone;

    /**
     * 客户来源<br/>Column: [customer_source]
     */
    @Column(value = "customer_source")
    private String customerSource;

    /**
     * 需求提交人，一般叫架构师<br/>Column: [submit_user]
     */
    @Column(value = "submit_user")
    private String submitUser;

    /**
     * 需求所属产品，例如CVM&CBS<br/>Column: [product]
     */
    @Column(value = "product")
    private String product;

    /**
     * 需求类型，NEW新增，ELASTIC弹性，RETURN退回<br/>Column: [demand_type]
     */
    @Column(value = "demand_type")
    private String demandType;

    /**
     * 需求场景名称<br/>Column: [demand_type_name]
     */
    @Column(value = "demand_type_name")
    private String demandTypeName;

    /**
     * 需求场景<br/>Column: [demand_scene]
     */
    @Column(value = "demand_scene")
    private String demandScene;

    /**
     * 项目名称<br/>Column: [project_name]
     */
    @Column(value = "project_name")
    private String projectName;

    /**
     * 计费模式<br/>Column: [bill_type]
     */
    @Column(value = "bill_type")
    private String billType;

    /**
     * 赢率<br/>Column: [win_rate]
     */
    @Column(value = "win_rate")
    private BigDecimal winRate;

    /**
     * 开始购买日期<br/>Column: [begin_buy_date]
     */
    @Column(value = "begin_buy_date")
    private LocalDate beginBuyDate;

    /**
     * 结束购买日期<br/>Column: [end_buy_date]
     */
    @Column(value = "end_buy_date")
    private LocalDate endBuyDate;

    /**
     * 弹性开始时间 HH:mm<br/>Column: [begin_elastic_time]
     */
    @Column(value = "begin_elastic_time")
    private String beginElasticTime;

    /**
     * 弹性结束时间 HH:mm<br/>Column: [end_elastic_time]
     */
    @Column(value = "end_elastic_time")
    private String endElasticTime;

    /**
     * 需求备注<br/>Column: [note]
     */
    @Column(value = "note")
    private String note;

    /**
     * 境内外<br/>Column: [customhouse_title]
     */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /**
     * 地域<br/>Column: [region_name]
     */
    @Column(value = "region_name")
    private String regionName;

    /**
     * 可用区<br/>Column: [zone_name]
     */
    @Column(value = "zone_name")
    private String zoneName;

    /**
     * 实例大类<br/>Column: [instance_type]
     */
    @Column(value = "instance_type")
    private String instanceType;

    /**
     * 实例规格<br/>Column: [instance_model]
     */
    @Column(value = "instance_model")
    private String instanceModel;

    /**
     * ppl需求总磁盘数<br/>Column: [total_disk]
     */
    @Column(value = "total_disk")
    private Integer totalDisk;

    /**
     * 可接受的其它实例类型，分号隔开<br/>Column: [alternative_instance_type]
     */
    @Column(value = "alternative_instance_type")
    private String alternativeInstanceType;

    /**
     * 亲和度类型<br/>Column: [affinity_type]
     */
    @Column(value = "affinity_type")
    private String affinityType;

    /**
     * 亲和度值<br/>Column: [affinity_value]
     */
    @Column(value = "affinity_value")
    private BigDecimal affinityValue;

    /**
     * 系统盘类型<br/>Column: [system_disk_type]
     */
    @Column(value = "system_disk_type")
    private String systemDiskType;

    /**
     * 单块系统盘大小，单位G<br/>Column: [system_disk_storage]
     */
    @Column(value = "system_disk_storage")
    private Integer systemDiskStorage;

    /**
     * 系统盘块数<br/>Column: [system_disk_num]
     */
    @Column(value = "system_disk_num")
    private Integer systemDiskNum;

    /**
     * 数据盘类型<br/>Column: [data_disk_type]
     */
    @Column(value = "data_disk_type")
    private String dataDiskType;

    /**
     * 单块数据盘大小，单位G<br/>Column: [data_disk_storage]
     */
    @Column(value = "data_disk_storage")
    private Integer dataDiskStorage;

    /**
     * 数据盘块数<br/>Column: [data_disk_num]
     */
    @Column(value = "data_disk_num")
    private Integer dataDiskNum;

    /**
     * 卡型/gpu类型<br/>Column: [gpu_type]
     */
    @Column(value = "gpu_type")
    private String gpuType;

    /**
     * 单台GPU卡数<br/>Column: [gpu_num]
     */
    @Column(value = "gpu_num")
    private BigDecimal gpuNum;

    /**
     * GPU总卡数<br/>Column: [total_gpu_num]
     */
    @Column(value = "total_gpu_num")
    private BigDecimal totalGpuNum;

    /**
     * 是否接受调整卡型，1接受，0不接受<br/>Column: [is_accept_adjust_gpu]
     */
    @Column(value = "is_accept_adjust_gpu")
    private Integer isAcceptAdjustGpu;

    /**
     * 接受卡型，用;分割<br/>Column: [accept_gpu]
     */
    @Column(value = "accept_gpu")
    private String acceptGpu;

    /**
     * GPU业务场景<br/>Column: [biz_scene]
     */
    @Column(value = "biz_scene")
    private String bizScene;

    /**
     * GPU业务详情<br/>Column: [biz_detail]
     */
    @Column(value = "biz_detail")
    private String bizDetail;

    /**
     * 使用时长，x（月）/长期稳定使用<br/>Column: [service_time]
     */
    @Column(value = "service_time")
    private String serviceTime;

    /**
     * gpu产品形态 裸金属/CVM<br/>Column: [gpu_product_type]
     */
    @Column(value = "gpu_product_type")
    private String gpuProductType;

    /**
     * 云霄预约单号<br/>Column: [yunxiao_order_id]
     */
    @Column(value = "yunxiao_order_id")
    private String yunxiaoOrderId;

    /**
     * 云霄预约单明细id<br/>Column: [yunxiao_detail_id]
     */
    @Column(value = "yunxiao_detail_id")
    private Long yunxiaoDetailId;

    /**
     * 云霄预约单状态<br/>Column: [yunxiao_order_status]
     */
    @Column(value = "yunxiao_order_status")
    private String yunxiaoOrderStatus;

    /**
     * 云霄预约单状态名称<br/>Column: [yunxiao_order_status_name]
     */
    @Column(value = "yunxiao_order_status_name")
    private String yunxiaoOrderStatusName;

    /**
     * 云霄appRole<br/>Column: [yunxiao_app_role]
     */
    @Column(value = "yunxiao_app_role")
    private String yunxiaoAppRole;

    /**
     * 云霄预约单类型<br/>Column: [yunxiao_order_category]
     */
    @Column(value = "yunxiao_order_category")
    private String yunxiaoOrderCategory;

    /**
     * 客户集团id
     */
    @Column(value = "cid")
    private String cId;

    /**
     * 客户集团
     */
    @Column(value = "cname")
    private String cName;

    /**
     * 可用区编码
     */
    @Column(value = "zone")
    private String zone;

    /**
     * 区域名称
     */
    @Column(value = "area_name")
    private String areaName;

    /**
     * 战略集团名称
     */
    @Column(value = "strategy_group_name")
    private String strategyGroupName;

    /**
     * 磐石战区
     */
    @Column(value = "pan_shi_war_zone")
    private String panShiWarZone;

    /**
     * 客户类型
     */
    @Column(value = "customer_tab_type")
    private String customerTabType;

    /**
     * 是否被干预，0未被干预，1被干预
     */
    @Column(value = "is_comd")
    private Integer isComd;

    /**
     * 源ppl_id
     */
    @Column(value = "source_ppl_id")
    private String sourcePplId;

    /**
     * 业务分类
     */
    @Column(value = "category")
    private String category;

    /**
     * uin类型，0内部 1外部
     */
    @Column(value = "uin_type")
    private Integer uinType;

    /**
     * 客户类型(从社会角度上看属于哪一类)，0个人 1企业
     */
    @Column(value = "customer_society_type")
    private Integer customerSocietyType;

    /**
     * 包销时长(年)（允许一位小数点，excel导入时最小填写单位为0.5）
     */
    @Column(value = "sale_duration_year")
    private BigDecimal saleDurationYear;

    /**
     * 申请折扣(折)（填写范围为0.1~10）
     */
    @Column(value = "apply_discount")
    private BigDecimal applyDiscount;

    /**
     * 商务进展（枚举值-CpqTypeEnum）
     */
    @Column(value = "business_cpq")
    private String businessCpq;

    @Column("instance_num_apply_before")
    private Integer instanceNumApplyBefore;

    @Column("instance_num_apply_after")
    private Integer instanceNumApplyAfter;

    @Column("total_core_apply_before")
    private Integer totalCoreApplyBefore;

    @Column("total_core_apply_after")
    private Integer totalCoreApplyAfter;

    @Column("total_gpu_num_apply_before")
    private BigDecimal totalGpuNumApplyBefore;

    @Column("total_gpu_num_apply_after")
    private BigDecimal totalGpuNumApplyAfter;

    /**
     * 通用客户简称<br/>Column: [common_customer_short_name]
     */
    @Column(value = "common_customer_short_name")
    private String commonCustomerShortName;

    /**
     * 通用实例类型<br/>Column: [common_instance_type]
     */
    @Column(value = "common_instance_type")
    private String commonInstanceType;

    @Column(value = "is_expired")
    private Boolean isExpired;

    /**
     * 数据库名称
     * @see PplDatabaseEnum
     */
    @Column(value = "database_name")
    @ChineseName("数据库名称")
    private String databaseName;

    /**
     *  是否多AZ，数据库产品
     */
    @Column(value = "more_than_one_az")
    private Boolean moreThanOneAZ;

    /**
     * 数据库存储类型，数据库产品
     * @see PplDatabaseStorageEnum
     */
    @Column(value = "database_storage_type")
    @ChineseName("数据库存储类型")
    private String databaseStorageType;

    /**
     * 实例部署类型，数据库产品
     * @see PplDeloyTypeEnum
     */
    @Column(value = "deploy_type")
    @ChineseName("实例部署类型")
    private String deployType;

    /**
     * 实例架构类型，数据库产品
     * @see PplDataBaseFrameworkEnum
     */
    @Column(value = "framework_type")
    @ChineseName("实例架构类型")
    private String frameworkType;

    /**
     *  分片数量，数据库产品
     */
    @Column(value = "slice_num")
    @ChineseName("分片数量")
    private Integer sliceNum;

    /**
     *  副本数量，数据库产品
     */
    @Column(value = "replica_num")
    @ChineseName("副本数量")
    private Integer replicaNum;

    /**
     *  只读数量，数据库产品
     */
    @Column(value = "read_only_num")
    @ChineseName("只读数量")
    private Integer readOnlyNum;

    /**
     *  数据库实例规格（2C16G 这种），数据库产品
     */
    @Column(value = "database_specs")
    @ChineseName("数据库实例规格")
    private String databaseSpecs;

    /**
     *  数据库存储量，单位GB，数据库产品
     */
    @Column(value = "database_storage")
    private BigDecimal databaseStorage;

    /**
     *  总数据库存储量，单位GB，数据库产品
     */
    @Column(value = "total_database_storage")
    @ChineseName("总数据库存储量")
    private BigDecimal totalDatabaseStorage;

    /**
     *  COS存储类型，COS产品
     * @see PplCosStorageEnum
     */
    @Column(value = "cos_storage_type")
    @ChineseName("COS存储类型")
    private String cosStorageType;

    /**
     *  单AZ、多AZ，COS产品
     * @see PplCosAZEnum
     */
    @Column(value = "cos_az")
    private String cosAZ;

    /**
     *  COS存储量，单位PB，COS产品
     */
    @Column(value = "cos_storage")
    private BigDecimal cosStorage;

    /**
     *  带宽，单位 Gbit/s，COS产品
     */
    @Column(value = "bandwidth")
    private Integer bandwidth;

    /**
     * qps，COS产品
     */
    @Column(value = "qps")
    private Integer qps;

    /**
     *  总COS存储量，单位PB，COS产品
     */
    @Column(value = "total_cos_storage")
    @ChineseName("总COS存储量")
    private BigDecimal totalCosStorage;

    /**
     * 单台核心数<br/>
     */
    @Column(value = "instance_model_core_num")
    private Integer instanceModelCoreNum;

    /**
     * 单台内存数，单位GB<br/>
     */
    @Column(value = "instance_model_ram_num")
    private Integer instanceModelRamNum;

    /**
     * 总内存数，单位GB<br/>Column: [total_memory]
     */
    @Column(value = "total_memory")
    @ChineseName("总内存数")
    private Integer totalMemory;


    @Override
    public String provideCustomerUin() {
        return this.customerUin;
    }

    @Override
    public void fillStrategyGroupName(String strategyGroupName) {
        this.strategyGroupName = strategyGroupName;
    }

    @Override
    public String provideSource() {
        return this.source;
    }

    @Override
    public Integer provideYear() {
        return this.year;
    }

    @Override
    public Integer provideMonth() {
        return this.month;
    }

    @Override
    public void fillCustomerTabType(String customerTabType) {
        this.customerTabType = customerTabType;
    }

    @Override
    public void fillCId(String cId) {
        this.cId = cId;
    }

    @Override
    public void fillCName(String cName) {
        this.cName = cName;
    }

    @Override
    public void fillPanShiWarZone(String panShiWarZone) {
        this.panShiWarZone = panShiWarZone;
    }

    @Override
    public void fillUinType(Integer uinType) {
        this.uinType = uinType;
    }

    @Override
    public void fillCustomerSocietyType(Integer customerSocietyType) {
        this.customerSocietyType = customerSocietyType;
    }

    @Override
    public String provideZoneName() {
        return this.zoneName;
    }

    @Override
    public String provideRegionName() {
        return this.regionName;
    }

    @Override
    public void fillZone(String zone) {
        this.zone = zone;
    }

    @Override
    public void fillAreaName(String areaName) {
        this.areaName = areaName;
    }

    @Override
    public void fillCustomhouseTitle(String customhouseTitle) {
        this.customhouseTitle = customhouseTitle;
    }

    @Override
    public String provideCustomerShortName() {
        return this.customerShortName;
    }

    @Override
    public String provideIndustryDept() {
        return this.industryDept;
    }

    @Override
    public void fillCommonCustomerShortName(String commonCustomerShortName) {
        this.commonCustomerShortName = commonCustomerShortName;
    }

    @Override
    public void fillCategory(String category) {
        this.category = category;
    }

    @Override
    public BigDecimal totalCoreGet() {
        return this.totalCore == null ? null : new BigDecimal(totalCore.toString());
    }

    @Override
    public BigDecimal totalGpuNumGet() {
        return this.totalGpuNum;
    }

    @Override
    public BigDecimal totalSystemDiskCapacityGet() {
        return BigDecimal.valueOf(instanceNum).multiply(BigDecimal.valueOf(systemDiskStorage));
    }

    @Override
    public BigDecimal totalDataDiskCapacityGet() {
        return BigDecimal.valueOf(instanceNum).multiply(BigDecimal.valueOf(dataDiskNum))
                .multiply(BigDecimal.valueOf(dataDiskStorage));
    }

    @Override
    public PplDemandTypeEnum demandTypeEnumGet() {
        return PplDemandTypeEnum.getByCode(this.demandType);
    }

    @Override
    public Ppl13weekProductTypeEnum productEnumGet() {
        return Ppl13weekProductTypeEnum.getByName(this.product);
    }

    @Override
    public String provideInstanceType() {
        return this.instanceType;
    }

    @Override
    public void fillCommonInstanceType(String commonInstanceType) {
        this.commonInstanceType = commonInstanceType;
    }
}