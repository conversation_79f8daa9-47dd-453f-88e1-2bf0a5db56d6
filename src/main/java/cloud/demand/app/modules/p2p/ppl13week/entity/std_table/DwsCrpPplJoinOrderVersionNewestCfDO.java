package cloud.demand.app.modules.p2p.ppl13week.entity.std_table;

import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.pugwoo.dbhelper.annotation.Table;
import org.springframework.beans.BeanUtils;

/**
 * 最新版拼接宽表
 */
@Data
@ToString
@Table("dws_crp_ppl_join_order_version_newest_cf")
public class DwsCrpPplJoinOrderVersionNewestCfDO implements SumTotalCoreOrGpu{

    /** 版本代号<br/>Column: [version_code] */
    @Column(value = "version_code")
    private String versionCode;

    /** 版本名称<br/>Column: [version_name] */
    @Column(value = "version_name")
    private String versionName;

    /** 版本所属年<br/>Column: [version_year] */
    @Column(value = "version_year")
    private Integer versionYear;

    /** 版本所属月<br/>Column: [version_month] */
    @Column(value = "version_month")
    private Integer versionMonth;

    /** 版本开始-年<br/>Column: [version_begin_year] */
    @Column(value = "version_begin_year")
    private Integer versionBeginYear;

    /** 版本开始-月<br/>Column: [version_begin_month] */
    @Column(value = "version_begin_month")
    private Integer versionBeginMonth;

    /** 版本结束-年<br/>Column: [version_end_year] */
    @Column(value = "version_end_year")
    private Integer versionEndYear;

    /** 版本结束-月<br/>Column: [version_end_month] */
    @Column(value = "version_end_month")
    private Integer versionEndMonth;

    /** 版本类型<br/>Column: [version_type] */
    @Column(value = "version_type")
    private String versionType;

    /** 版本类型名称<br/>Column: [version_type_name] */
    @Column(value = "version_type_name")
    private String versionTypeName;

    /** 版本状态<br/>Column: [version_status] */
    @Column(value = "version_status")
    private String versionStatus;

    /** 版本状态名称<br/>Column: [version_status_name] */
    @Column(value = "version_status_name")
    private String versionStatusName;

    /** 版本备注<br/>Column: [version_note] */
    @Column(value = "version_note")
    private String versionNote;

    /** 版本创建人<br/>Column: [version_creator] */
    @Column(value = "version_creator")
    private String versionCreator;

    /** 版本开始审批时间<br/>Column: [version_start_audit_time] */
    @Column(value = "version_start_audit_time")
    private LocalDateTime versionStartAuditTime;

    /** 版本结束审批时间<br/>Column: [version_end_audit_time] */
    @Column(value = "version_end_audit_time")
    private LocalDateTime versionEndAuditTime;

    /** 版本分组-行业部门<br/>Column: [version_group_industry_dept] */
    @Column(value = "version_group_industry_dept")
    private String versionGroupIndustryDept;

    /** 版本分组-产品<br/>Column: [version_group_product] */
    @Column(value = "version_group_product")
    private String versionGroupProduct;

    /** 版本分组-当前状态<br/>Column: [version_group_status] */
    @Column(value = "version_group_status")
    private String versionGroupStatus;

    /** 版本分组-当前状态名称<br/>Column: [version_group_status_name] */
    @Column(value = "version_group_status_name")
    private String versionGroupStatusName;

    /** pplId,
唯一<br/>Column: [ppl_id] */
    @Column(value = "ppl_id")
    private String pplId;

    /** ppl单号<br/>Column: [ppl_order] */
    @Column(value = "ppl_order")
    private String pplOrder;

    /** ppl更新时间<br/>Column: [update_time] */
    @Column(value = "update_time")
    private LocalDateTime updateTime;

    /** ppl状态，VALID已生效，APPLIED已预约<br/>Column: [status] */
    @Column(value = "status")
    private String status;

    /** ppl状态名称<br/>Column: [status_name] */
    @Column(value = "status_name")
    private String statusName;

    /** ppl需求台数<br/>Column: [instance_num] */
    @Column(value = "instance_num")
    private Integer instanceNum;

    /** ppl需求总核心数<br/>Column: [total_core] */
    @Column(value = "total_core")
    private Integer totalCore;

    /** ppl来源<br/>Column: [source] */
    @Column(value = "source")
    private String source;

    /** ppl来源名称<br/>Column: [source_name] */
    @Column(value = "source_name")
    private String sourceName;

    /** ppl需求-年<br/>Column: [year] */
    @Column(value = "year")
    private Integer year;

    /** ppl需求-月<br/>Column: [month] */
    @Column(value = "month")
    private Integer month;

    /** 客户uin<br/>Column: [customer_uin] */
    @Column(value = "customer_uin")
    private String customerUin;

    /** 行业<br/>Column: [industry] */
    @Column(value = "industry")
    private String industry;

    /** 行业部门<br/>Column: [industry_dept] */
    @Column(value = "industry_dept")
    private String industryDept;

    /** 客户类型<br/>Column: [customer_type] */
    @Column(value = "customer_type")
    private String customerType;

    /** 客户类型名称<br/>Column: [customer_type_name] */
    @Column(value = "customer_type_name")
    private String customerTypeName;

    /** 客户名称<br/>Column: [customer_name] */
    @Column(value = "customer_name")
    private String customerName;

    /** 客户简称<br/>Column: [customer_short_name] */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /** 战区<br/>Column: [war_zone] */
    @Column(value = "war_zone")
    private String warZone;

    /** 客户来源<br/>Column: [customer_source] */
    @Column(value = "customer_source")
    private String customerSource;

    /** 需求提交人，一般叫架构师<br/>Column: [submit_user] */
    @Column(value = "submit_user")
    private String submitUser;

    /** 需求所属产品，例如CVM&CBS<br/>Column: [product] */
    @Column(value = "product")
    private String product;

    /** 需求类型，NEW新增，ELASTIC弹性，RETURN退回<br/>Column: [demand_type] */
    @Column(value = "demand_type")
    private String demandType;

    /** 需求场景名称<br/>Column: [demand_type_name] */
    @Column(value = "demand_type_name")
    private String demandTypeName;

    /** 需求场景<br/>Column: [demand_scene] */
    @Column(value = "demand_scene")
    private String demandScene;

    /** 项目名称<br/>Column: [project_name] */
    @Column(value = "project_name")
    private String projectName;

    /** 计费模式<br/>Column: [bill_type] */
    @Column(value = "bill_type")
    private String billType;

    /** 赢率<br/>Column: [win_rate] */
    @Column(value = "win_rate")
    private BigDecimal winRate;

    /** 开始购买日期<br/>Column: [begin_buy_date] */
    @Column(value = "begin_buy_date")
    private LocalDate beginBuyDate;

    /** 结束购买日期<br/>Column: [end_buy_date] */
    @Column(value = "end_buy_date")
    private LocalDate endBuyDate;

    /** 弹性开始时间 HH:mm<br/>Column: [begin_elastic_time] */
    @Column(value = "begin_elastic_time")
    private String beginElasticTime;

    /** 弹性结束时间 HH:mm<br/>Column: [end_elastic_time] */
    @Column(value = "end_elastic_time")
    private String endElasticTime;

    /** 需求备注<br/>Column: [note] */
    @Column(value = "note")
    private String note;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 地域<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 实例大类<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 实例规格<br/>Column: [instance_model] */
    @Column(value = "instance_model")
    private String instanceModel;

    /** ppl需求总磁盘数<br/>Column: [total_disk] */
    @Column(value = "total_disk")
    private Integer totalDisk;

    /** 可接受的其它实例类型，分号隔开<br/>Column: [alternative_instance_type] */
    @Column(value = "alternative_instance_type")
    private String alternativeInstanceType;

    /** 亲和度类型<br/>Column: [affinity_type] */
    @Column(value = "affinity_type")
    private String affinityType;

    /** 亲和度值<br/>Column: [affinity_value] */
    @Column(value = "affinity_value")
    private BigDecimal affinityValue;

    /** 系统盘类型<br/>Column: [system_disk_type] */
    @Column(value = "system_disk_type")
    private String systemDiskType;

    /** 单块系统盘大小，单位G<br/>Column: [system_disk_storage] */
    @Column(value = "system_disk_storage")
    private Integer systemDiskStorage;

    /** 系统盘块数<br/>Column: [system_disk_num] */
    @Column(value = "system_disk_num")
    private Integer systemDiskNum;

    /** 数据盘类型<br/>Column: [data_disk_type] */
    @Column(value = "data_disk_type")
    private String dataDiskType;

    /** 单块数据盘大小，单位G<br/>Column: [data_disk_storage] */
    @Column(value = "data_disk_storage")
    private Integer dataDiskStorage;

    /** 数据盘块数<br/>Column: [data_disk_num] */
    @Column(value = "data_disk_num")
    private Integer dataDiskNum;

    /** 卡型/gpu类型<br/>Column: [gpu_type] */
    @Column(value = "gpu_type")
    private String gpuType;

    /** 单台GPU卡数<br/>Column: [gpu_num] */
    @Column(value = "gpu_num")
    private BigDecimal gpuNum;

    /** GPU总卡数<br/>Column: [total_gpu_num] */
    @Column(value = "total_gpu_num")
    private BigDecimal totalGpuNum;

    /** 是否接受调整卡型，1接受，0不接受<br/>Column: [is_accept_adjust_gpu] */
    @Column(value = "is_accept_adjust_gpu")
    private Integer isAcceptAdjustGpu;

    /** 接受卡型，用;分割<br/>Column: [accept_gpu] */
    @Column(value = "accept_gpu")
    private String acceptGpu;

    /** GPU业务场景<br/>Column: [biz_scene] */
    @Column(value = "biz_scene")
    private String bizScene;

    /** GPU业务详情<br/>Column: [biz_detail] */
    @Column(value = "biz_detail")
    private String bizDetail;

    /** 使用时长，x（月）/长期稳定使用<br/>Column: [service_time] */
    @Column(value = "service_time")
    private String serviceTime;

    /** gpu产品形态 裸金属/CVM<br/>Column: [gpu_product_type] */
    @Column(value = "gpu_product_type")
    private String gpuProductType;

    /** 客户集团id<br/>Column: [cid] */
    @Column(value = "cid")
    private String cid;

    /** 客户集团<br/>Column: [cname] */
    @Column(value = "cname")
    private String cname;

    /** 可用区编码<br/>Column: [zone] */
    @Column(value = "zone")
    private String zone;

    /** 区域名称<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** 磐石战区<br/>Column: [pan_shi_war_zone] */
    @Column(value = "pan_shi_war_zone")
    private String panShiWarZone;

    /** 客户类型<br/>Column: [customer_tab_type] */
    @Column(value = "customer_tab_type")
    private String customerTabType;

    /** 战略集团名称<br/>Column: [strategy_group_name] */
    @Column(value = "strategy_group_name")
    private String strategyGroupName;

    /** 是否被干预，0未被干预，1被干预<br/>Column: [is_comd] */
    @Column(value = "is_comd")
    private Integer isComd;

    /** 源ppl_id<br/>Column: [source_ppl_id] */
    @Column(value = "source_ppl_id")
    private String sourcePplId;

    /** 业务分类（内领业务、外部行业… ）<br/>Column: [category] */
    @Column(value = "category")
    private String category;

    /** uin类型，0内部 1外部<br/>Column: [uin_type] */
    @Column(value = "uin_type")
    private Integer uinType;

    /** 客户类型(从社会角度上看属于哪一类)，0个人 1企业<br/>Column: [customer_society_type] */
    @Column(value = "customer_society_type")
    private Integer customerSocietyType;

    /** 数据同步时间，yyyyMMddHH<br/>Column: [imp_date] */
    @Column(value = "imp_date")
    private Integer impDate;

    @Column(value = "is_spike")
    private Integer isSpike;

    @Column(value = "cbs_is_spike")
    private Integer cbsIsSpike;

    /** 通用客户简称<br/>Column: [common_customer_short_name] */
    @Column(value = "common_customer_short_name")
    private String commonCustomerShortName;

    /** 通用实例类型<br/>Column: [common_instance_type] */
    @Column(value = "common_instance_type")
    private String commonInstanceType;

    /** 数据源: 1.ORDER（订单数据） 2.VERSION（版本数据） 3.EXTEND（继承数据）<br/>Column: [data_source] */
    @Column(value = "data_source")
    private String dataSource;

    /** 继承数据的原始版本号<br/>Column: [extend_data_version] */
    @Column(value = "extend_data_version")
    private String extendDataVersion;

    /** 订单号<br/>Column: [order_number] */
    @Column(value = "order_number")
    private String orderNumber;

    /** 订单共识需求唯一ID<br/>Column: [consensus_demand_version_id] */
    @Column(value = "consensus_demand_version_id")
    private String consensusDemandVersionId;

    /** 满足核心数<br/>Column: [satisfied_total_core] */
    @Column(value = "satisfied_total_core")
    private BigDecimal satisfiedTotalCore;

    /** 购买核心数<br/>Column: [buy_total_core] */
    @Column(value = "buy_total_core")
    private BigDecimal buyTotalCore;

    /** 待购买核心数<br/>Column: [wait_buy_total_core] */
    @Column(value = "wait_buy_total_core")
    private BigDecimal waitBuyTotalCore;

    /** 订单来源<br/>Column: [order_source] */
    @Column(value = "order_source")
    private String orderSource;

    /** 来源PPL<br/>Column: [source_ppl] */
    @Column(value = "source_ppl")
    private String sourcePpl;

    /**
     * 系统盘json 仅订单存在
     */
    @Column(value = "system_disk")
    private String systemDisk;

    /**
     * 数据盘json 仅订单存在
     */
    @Column(value = "data_disk")
    private String dataDisk;

    /**
     * 订单标签 仅订单存在
     * @see cloud.demand.app.modules.order.enums.OrderLabelEnum
     */
    @Column(value = "order_label", insertValueScript = "'(空值)'")
    private String orderLabel;

    /**
     * 报表位数 仅订单存在
     * @see cloud.demand.app.modules.order.enums.OrderReportBiteEnum
     */
    @Column(value = "report_bit_num", insertValueScript = "1023")
    private Integer reportBitNum;

    @Override
    public BigDecimal totalCoreGet() {
        return BigDecimal.valueOf(this.totalCore);
    }

    @Override
    public BigDecimal totalGpuNumGet() {
        return this.totalGpuNum;
    }

    @Override
    public BigDecimal totalSystemDiskCapacityGet() {
        return BigDecimal.valueOf(instanceNum).multiply(BigDecimal.valueOf(systemDiskStorage));
    }

    @Override
    public BigDecimal totalDataDiskCapacityGet() {
        return BigDecimal.valueOf(instanceNum).multiply(BigDecimal.valueOf(dataDiskNum))
                .multiply(BigDecimal.valueOf(dataDiskStorage));
    }

    @Override
    public PplDemandTypeEnum demandTypeEnumGet() {
        return PplDemandTypeEnum.getByCode(this.demandType);
    }

    @Override
    public Ppl13weekProductTypeEnum productEnumGet() {
        return Ppl13weekProductTypeEnum.getByName(this.product);
    }

    @Data
    public static class YearMonthVersionCode {

        @Column(value = "year")
        private Integer year;

        @Column(value = "month")
        private Integer month;

        @Column(value = "version_code")
        private String versionCode;
    }


    public static DwsCrpPplJoinOrderVersionNewestCfDO transform(DwdCrpPplJoinOrderVersionCfDO dwdCrpPplJoinOrderVersionCfDO) {
        DwsCrpPplJoinOrderVersionNewestCfDO result = new DwsCrpPplJoinOrderVersionNewestCfDO();
        BeanUtils.copyProperties(dwdCrpPplJoinOrderVersionCfDO, result);
        return result;
    }
}