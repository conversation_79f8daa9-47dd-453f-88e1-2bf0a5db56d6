package cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis;

import cloud.demand.app.common.utils.DateUtils;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.common.utils.StdUtils;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderSourceEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderSourceTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplWaveProductCategoryEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.StatisticalScopeEnum;
import cloud.demand.app.modules.std_crp.constant.StdCrpConstants;
import cloud.demand.app.modules.std_crp.constant.StdCrpConstants.CustomerSocietyType;
import cloud.demand.app.modules.std_crp.constant.StdCrpConstants.UinType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.nutz.lang.Lang;
import org.springframework.util.CollectionUtils;

/**
 * 波动分析通用请求体
 */
@Data
public class QueryWaveReq {

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM", timezone = "GMT+8")
    @NotNull
    private Date beginYearMonth;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM", timezone = "GMT+8")
    @NotNull
    private Date endYearMonth;

    /** 订单标签
     * @see cloud.demand.app.modules.order.enums.OrderLabelEnum
     * */
    private List<String> orderLabel;

    /**
     * 需求类型
     *
     * @see PplDemandTypeEnum
     */
    private List<String> demandType;

    /**
     * 地域
     */
    private List<String> region;

    /**
     * 战区
     */
    private List<String> warZone;

    /**
     * 实例类型
     */
    private List<String> instanceType;

    /**
     * 需求场景
     */
    private List<String> demandScene;

    /**
     * 行业分类
     */
    private List<String> category;

    /**
     * 行业部门
     */
    private List<String> industryDept;

    /**
     * 客户简称
     */
    private List<String> customerShortName;

    /**
     * UIN
     */
    private List<String> customerUin;

    /**
     * 判断是否个人用户
     */
    private Boolean isPersonal;

    /**
     * 判断是否为内部用户
     */
    private Boolean isInnerCustomerUin;

    private List<Integer> uinType;

    /**
     * 产品大类
     */
    private String productCategory;

    /**
     * 产品
     */
    private List<String> product;

    /**
     * 可用区
     */
    private List<String> zoneName;

    /**
     * 实例规格
     */
    private List<String> instanceModel;

    /**
     * GPU卡型/类型
     */
    private List<String> gpuType;

    /**
     * 境内/境外/未分类
     */
    private List<String> customhouseTitle;

    /**
     * PPL来源（版本预测来源）
     *
     * @see PplOrderSourceEnum
     */
    private List<String> pplSource;

    /**
     * 统计范围
     *
     * @see StatisticalScopeEnum
     */
    private List<String> statisticalScope;

    /**
     * 云盘类型
     */
    private List<String> volumeType;

    /**
     * 系统盘、数据盘
     */
    private List<String> diskType;

    /**
     * 项目类型
     */
    private List<String> projectType;

    /**
     * 生成WhereContent条件
     */
    public ORMUtils.WhereContent getWhereContent() {
        ORMUtils.WhereContent content = new ORMUtils.WhereContent();
        //  涉及到该VO都要处理软删标记
        if (ListUtils.isEmpty(product)) {
            product = PplWaveProductCategoryEnum.getSubProductByCode(productCategory);
        }
        content.addAnd("product in (?)", product);

        //  行业部门特殊处理
        if (ListUtils.isNotEmpty(category)) {
            List<String> industryCategory = ListUtils.filter(category, o -> !"未分类".equals(o));
            List<String> others = ListUtils.filter(category, "未分类"::equals);
            if (!industryCategory.isEmpty() || !others.isEmpty()) {
                ORMUtils.WhereContent dept = new ORMUtils.WhereContent();
                if (!industryCategory.isEmpty()) {
                    dept.addOr("category in (?)", industryCategory);
                }
                if (!others.isEmpty()) {
                    dept.addOr(" category is null or category = ?", StdCrpConstants.EMPTY_STR);
                }
                content.addAnd(dept);
            }
        }

        if (ListUtils.isNotEmpty(industryDept)) {
            content.addAnd("industry_dept in (?)", industryDept);
        }

        if (ListUtils.isNotEmpty(customerShortName)) {
            content.addAnd("customer_short_name in (?)", customerShortName);
        }
        if (ListUtils.isNotEmpty(demandType)) {
            content.addAnd("demand_type in (?)", demandType);
        }
        if (ListUtils.isNotEmpty(region)) {
            content.addAnd("region_name in (?)", region);
        }
        if (ListUtils.isNotEmpty(warZone)) {
            content.addAnd("war_zone in (?)", warZone);
        }
        if (ListUtils.isNotEmpty(instanceType)) {
            content.addAnd("instance_type in (?)", instanceType);
        }
        if (ListUtils.isNotEmpty(demandScene)) {
            content.addAnd("demand_scene in (?)", demandScene);
        }
        if (ListUtils.isNotEmpty(customerUin)) {
            content.addAnd("customer_uin in (?)", customerUin);
        }

        //  判断是否个人
        if (isPersonal != null) {
            content.addAnd("customer_society_type = ?",
                    isPersonal ? CustomerSocietyType.person.getCode() : CustomerSocietyType.enterprise.getCode());
        }

        //  判断是否内部
        if (isInnerCustomerUin != null) {
            content.addAnd("uin_type = ?",
                    isInnerCustomerUin ? UinType.inner.getCode() : UinType.external.getCode());
        }else if (ListUtils.isNotEmpty(uinType)){
             content.addAnd("uin_type in (?)", uinType);
        }

        DateUtils.YearMonth start = DateUtils.parse(
                com.pugwoo.wooutils.lang.DateUtils.format(this.beginYearMonth, "yyyy-MM"));
        if (start != null) {
            content.addAnd("`year` > ? or (`year` = ? and `month` >= ?)",
                    start.getYear(), start.getYear(), start.getMonth());
        }
        DateUtils.YearMonth end = DateUtils.parse(
                com.pugwoo.wooutils.lang.DateUtils.format(this.endYearMonth, "yyyy-MM"));
        if (end != null) {
            content.addAnd("`year` < ? or (`year` = ? and `month` <= ?)",
                    end.getYear(), end.getYear(), end.getMonth());
        }
        // 通用，仅获取未被干预或者被干预后的数据
        content.addAnd("`source` in ('IMPORT', 'COMD_INTERVENE','APPLY_AUTO_FILL','FORECAST') and is_comd != 1");
        return content;
    }

    public ORMUtils.WhereContent yearMonthPartitionCondition() {
        WhereContent content = new WhereContent();
        String start = com.pugwoo.wooutils.lang.DateUtils.format(this.beginYearMonth, "yyyyMM");
        if (start != null) {
            content.addAnd("`year_month` >= ? ", Integer.parseInt(start));
        }
        String end = com.pugwoo.wooutils.lang.DateUtils.format(this.endYearMonth, "yyyyMM");
        if (end != null) {
            content.addAnd("`year_month` <= ? ", Integer.parseInt(end));
        }
        return content;
    }

    /**
     * 生成波动明细查询的WhereContent条件
     */
    public ORMUtils.WhereContent getWaveDetailContent() {
        ORMUtils.WhereContent content = new ORMUtils.WhereContent();
        //  涉及到该VO都要处理软删标记
        if (ListUtils.isEmpty(product)) {
            product = PplWaveProductCategoryEnum.getSubProductByCode(productCategory);
        }
        content.addAnd("product in (?)", product);

        //  行业部门特殊处理
        if (ListUtils.isNotEmpty(category)) {
            List<String> industryCategory = ListUtils.filter(category, o -> !"未分类".equals(o));
            List<String> others = ListUtils.filter(category, "未分类"::equals);
            if (!industryCategory.isEmpty() || !others.isEmpty()) {
                ORMUtils.WhereContent dept = new ORMUtils.WhereContent();
                if (!industryCategory.isEmpty()) {
                    dept.addOr("category in (?)", industryCategory);
                }
                if (!others.isEmpty()) {
                    dept.addOr(" category is null or category = ?", StdCrpConstants.EMPTY_STR);
                }
                content.addAnd(dept);
            }
        }

        if (ListUtils.isNotEmpty(industryDept)) {
            content.addAnd("industry_dept in (?)", industryDept);
        }

        if (ListUtils.isNotEmpty(customerShortName)) {
            content.addAnd("customer_short_name in (?)", customerShortName);
        }
        if (ListUtils.isNotEmpty(demandType)) {
            content.addAnd("demand_type in (?)", demandType);
        }
        if (ListUtils.isNotEmpty(region)) {
            content.addAnd("region_name in (?)", region);
        }
        if (ListUtils.isNotEmpty(warZone)) {
            content.addAnd("war_zone in (?)", warZone);
        }
        if (ListUtils.isNotEmpty(instanceType)) {
            content.addAnd("instance_type in (?)", instanceType);
        }
        if (ListUtils.isNotEmpty(demandScene)) {
            content.addAnd("demand_scene in (?)", demandScene);
        }
        if (ListUtils.isNotEmpty(customerUin)) {
            content.addAnd("customer_uin in (?)", customerUin);
        }
        if (ListUtils.isNotEmpty(zoneName)) {
            content.addAnd("zone_name in (?)", zoneName);
        }
        if (ListUtils.isNotEmpty(instanceModel)) {
            content.addAnd("instance_model in (?)", instanceModel);
        }
        if (ListUtils.isNotEmpty(gpuType)) {
            content.addAnd("gpu_type in (?)", gpuType);
        }

        //  判断是否个人
        if (isPersonal != null) {
            content.addAnd("customer_society_type = ?",
                    isPersonal ? CustomerSocietyType.person.getCode() : CustomerSocietyType.enterprise.getCode());
        }

        //  判断是否内部
        if (isInnerCustomerUin != null) {
            content.addAnd("uin_type = ?",
                    isInnerCustomerUin ? UinType.inner.getCode() : UinType.external.getCode());
        }else if (ListUtils.isNotEmpty(uinType)){
            content.addAnd("uin_type in (?)", uinType);
        }

        DateUtils.YearMonth start = DateUtils.parse(
                com.pugwoo.wooutils.lang.DateUtils.format(this.beginYearMonth, "yyyy-MM"));
        if (start != null) {
            content.addAnd("`year` > ? or (`year` = ? and `month` >= ?)",
                    start.getYear(), start.getYear(), start.getMonth());
        }
        DateUtils.YearMonth end = DateUtils.parse(
                com.pugwoo.wooutils.lang.DateUtils.format(this.endYearMonth, "yyyy-MM"));
        if (end != null) {
            content.addAnd("`year` < ? or (`year` = ? and `month` <= ?)",
                    end.getYear(), end.getYear(), end.getMonth());
        }

        List<String> customhouseTitles = getDimCustomhouseTitles(customhouseTitle);
        if (ListUtils.isNotEmpty(customhouseTitles)) {
            content.addAnd("customhouse_title in(?)", customhouseTitles);
        }

        return content;
    }

    public ORMUtils.WhereContent getOrderInfoContent() {
        ORMUtils.WhereContent content = new ORMUtils.WhereContent();
        //  涉及到该VO都要处理软删标记
        if (ListUtils.isEmpty(product)) {
            product = PplWaveProductCategoryEnum.getSubProductByCode(productCategory);
        }
        content.addAnd("product in (?)", product);

        //  行业部门特殊处理
        if (ListUtils.isNotEmpty(category)) {
            List<String> industryCategory = ListUtils.filter(category, o -> !"未分类".equals(o));
            List<String> others = ListUtils.filter(category, "未分类"::equals);
            if (!industryCategory.isEmpty() || !others.isEmpty()) {
                ORMUtils.WhereContent dept = new ORMUtils.WhereContent();
                if (!industryCategory.isEmpty()) {
                    dept.addOr("industry_dept in (?)", industryCategory);
                }
                if (!others.isEmpty()) {
                    dept.addOr(" industry_dept is null or industry_dept = ?", StdCrpConstants.EMPTY_STR);
                }
                content.addAnd(dept);
            }
        }

        if (ListUtils.isNotEmpty(industryDept)) {
            content.addAnd("industry_dept in (?)", industryDept);
        }

        if (ListUtils.isNotEmpty(customerShortName)) {
            content.addAnd("customer_short_name in (?)", customerShortName);
        }
        if (ListUtils.isNotEmpty(demandType)) {
            content.addAnd("demand_type in (?)", demandType);
        }
        if (ListUtils.isNotEmpty(region)) {
            content.addAnd("region_name in (?)", region);
        }
        if (ListUtils.isNotEmpty(warZone)) {
            content.addAnd("war_zone in (?)", warZone);
        }
        if (ListUtils.isNotEmpty(instanceType)) {
            content.addAnd("instance_type in (?)", instanceType);
        }
        if (ListUtils.isNotEmpty(customerUin)) {
            content.addAnd("customer_uin in (?)", customerUin);
        }
        if (ListUtils.isNotEmpty(zoneName)) {
            content.addAnd("zone_name in (?)", zoneName);
        }
        if (ListUtils.isNotEmpty(instanceModel)) {
            content.addAnd("instance_model in (?)", instanceModel);
        }
        if (ListUtils.isNotEmpty(gpuType)) {
            content.addAnd("gpu_type in (?)", gpuType);
        }

        DateUtils.YearMonth start = DateUtils.parse(
                com.pugwoo.wooutils.lang.DateUtils.format(this.beginYearMonth, "yyyy-MM"));
        if (start != null) {
            content.addAnd("begin_buy_date >= ?", start.getFirstDay());
        }
        DateUtils.YearMonth end = DateUtils.parse(
                com.pugwoo.wooutils.lang.DateUtils.format(this.endYearMonth, "yyyy-MM"));
        if (end != null) {
            content.addAnd("begin_buy_date <= ?", end.getLastDay());
        }

        List<String> customhouseTitles = getDimCustomhouseTitles(customhouseTitle);
        if (ListUtils.isNotEmpty(customhouseTitles)) {
            content.addAnd("customhouse_title in(?)", customhouseTitles);
        }

        return content;
    }

    /**
     * 生成source条件
     *
     * @param content WhereContent条件
     */
    public void setSourceContent(ORMUtils.WhereContent content) {
        // 来源：用户报备/系统补充（若无传参则都查）
        List<String> source = new ArrayList<>();
        if (ListUtils.isEmpty(pplSource) || pplSource.size() > 1) {
            source.add(PplOrderSourceTypeEnum.IMPORT.getCode());
            source.add(PplOrderSourceTypeEnum.COMD_INTERVENE.getCode());

            source.add(PplOrderSourceTypeEnum.SYNC_YUNXIAO.getCode());
            source.add(PplOrderSourceTypeEnum.APPLY_AUTO_FILL.getCode());
            source.add(PplOrderSourceTypeEnum.APPLY_AUTO_FILL_LONGTAIL.getCode());

            source.add(PplOrderSourceTypeEnum.FORECAST.getCode());
        } else {
            if (pplSource.contains(PplOrderSourceEnum.INPUT.getCode())) {
                source.add(PplOrderSourceTypeEnum.IMPORT.getCode());
                source.add(PplOrderSourceTypeEnum.COMD_INTERVENE.getCode());

                source.add(PplOrderSourceTypeEnum.FORECAST.getCode());
            }
            if (pplSource.contains(PplOrderSourceEnum.SYSTEM_INPUT.getCode())) {
                source.add(PplOrderSourceTypeEnum.SYNC_YUNXIAO.getCode());
                source.add(PplOrderSourceTypeEnum.APPLY_AUTO_FILL.getCode());
                source.add(PplOrderSourceTypeEnum.APPLY_AUTO_FILL_LONGTAIL.getCode());
            }
        }
        if (ListUtils.isNotEmpty(source)) {
            content.addAnd("source in (?)", source);
        }

        // 统计范围：行业预测/云运管干预（source在【来源】传参中已设置，统计范围在后续数据全查出后根据is_comd再处理）
        // 获取 行业预测 的数据
        // （云运管版本中，行业同步过来的预测数据，含：运管未干预部分-取行业原始预测数据source=IMPORT/APPLY_AUTO_FILL, is_comd=0、
        //                                                                                 运管干预部分-取干预前的原始数据source=IMPORT, is_comd=1）
        //              source in('IMPORT', 'APPLY_AUTO_FILL')
        // 获取 云运管干预后 的数据
        // （云运管版本中，含：运管未干预部分-取中长尾数据source = FORECAST, is_comd = 0 & 取行业原始预测数据source=IMPORT/APPLY_AUTO_FILL, is_comd=0
        //                                     运管干预部分-取干预后的调整数据source=COMD_INTERVENE, is_comd=0）
        //                          （备注： 云运管只能干预IMPORT来源的ppl单，中长尾数据来源的is_comd只有0）
        //              source in ('FORECAST', 'IMPORT',  'APPLY_AUTO_FILL', 'COMD_INTERVENE') and is_comd != 1
    }


    private List<String> getDimCustomhouseTitles(List<String> customhouseTitle) {
        if (ListUtils.isEmpty(customhouseTitle)) {
            return Collections.emptyList();
        }

        List<String> dimRegionClass = new ArrayList<>();

        ArrayList<String> inner = Lang.list("国内", "内地", "境内", "中国内地");
        ArrayList<String> outer = Lang.list("海外", "境外");

        if (customhouseTitle.contains("境内")) {
            dimRegionClass.addAll(inner);
        }
        if (customhouseTitle.contains("境外")) {
            dimRegionClass.addAll(outer);
        }
        if (customhouseTitle.contains("未分类")) {
            dimRegionClass.add("未分类");
            dimRegionClass.add("未知");
            dimRegionClass.add(StdUtils.EMPTY_STR);
        }
        return dimRegionClass;
    }

    /**
     * 是否计算净增，退回需求和其他需求组合查询时计算净增
     */
    public boolean calcNetGrowth() {
        return ListUtils.isEmpty(this.getDemandType())
                || (this.getDemandType().contains(PplDemandTypeEnum.RETURN.getCode())
                && CollectionUtils.containsAny(this.getDemandType(),
                ListUtils.newArrayList(PplDemandTypeEnum.NEW.getCode(), PplDemandTypeEnum.ELASTIC.getCode())));
    }

}
