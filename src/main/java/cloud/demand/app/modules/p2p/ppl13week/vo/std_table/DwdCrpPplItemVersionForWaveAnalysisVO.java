package cloud.demand.app.modules.p2p.ppl13week.vo.std_table;

import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.SumTotalCoreOrGpu;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import lombok.Data;

@Data
@Table("dwd_crp_ppl_item_version_cf")
public class DwdCrpPplItemVersionForWaveAnalysisVO implements SumTotalCoreOrGpu {

    /** 版本代号<br/>Column: [version_code] */
    @Column(value = "version_code")
    private String versionCode;


    /** 版本开始-年<br/>Column: [version_begin_year] */
    @Column(value = "version_begin_year")
    private Integer versionBeginYear;

    /** 版本开始-月<br/>Column: [version_begin_month] */
    @Column(value = "version_begin_month")
    private Integer versionBeginMonth;

    /** 版本结束-年<br/>Column: [version_end_year] */
    @Column(value = "version_end_year")
    private Integer versionEndYear;

    /** 版本结束-月<br/>Column: [version_end_month] */
    @Column(value = "version_end_month")
    private Integer versionEndMonth;



    /** 版本开始审批时间<br/>Column: [version_start_audit_time] */
    @Column(value = "version_start_audit_time")
    private Date versionStartAuditTime;



    /** pplId,唯一<br/>Column: [ppl_id] */
    @Column(value = "ppl_id")
    private String pplId;



    /** ppl需求台数<br/>Column: [instance_num] */
    @Column(value = "instance_num")
    private Integer instanceNum;

    /** ppl需求总核心数<br/>Column: [total_core] */
    @Column(value = "total_core")
    private Integer totalCore;

    /** ppl来源<br/>Column: [source] */
    @Column(value = "source")
    private String source;



    /** ppl需求-年<br/>Column: [year] */
    @Column(value = "year")
    private Integer year;

    /** ppl需求-月<br/>Column: [month] */
    @Column(value = "month")
    private Integer month;

    /** 客户uin<br/>Column: [customer_uin] */
    @Column(value = "customer_uin")
    private String customerUin;



    /** 行业部门<br/>Column: [industry_dept] */
    @Column(value = "industry_dept")
    private String industryDept;






    /** 客户简称<br/>Column: [customer_short_name] */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /** 战区<br/>Column: [war_zone] */
    @Column(value = "war_zone")
    private String warZone;


    /** 需求所属产品，例如CVM&CBS<br/>Column: [product] */
    @Column(value = "product")
    private String product;

    /** 需求类型，NEW新增，ELASTIC弹性，RETURN退回<br/>Column: [demand_type] */
    @Column(value = "demand_type")
    private String demandType;

    /** 需求场景<br/>Column: [demand_scene] */
    @Column(value = "demand_scene")
    private String demandScene;



    /** 计费模式<br/>Column: [bill_type] */
    @Column(value = "bill_type")
    private String billType;


    /** 开始购买日期<br/>Column: [begin_buy_date] */
    @Column(value = "begin_buy_date")
    private LocalDate beginBuyDate;

    /** 结束购买日期<br/>Column: [end_buy_date] */
    @Column(value = "end_buy_date")
    private LocalDate endBuyDate;







    /** 地域<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 实例大类<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;



    /** ppl需求总磁盘数<br/>Column: [total_disk] */
    @Column(value = "total_disk")
    private Integer totalDisk;

    /** 实例规格<br/>Column: [instance_model] */
    @Column(value = "instance_model")
    private String instanceModel;


    /** 卡型/gpu类型<br/>Column: [gpu_type] */
    @Column(value = "gpu_type")
    private String gpuType;


    /** GPU总卡数<br/>Column: [total_gpu_num] */
    @Column(value = "total_gpu_num")
    private BigDecimal totalGpuNum;





    /** 是否被干预，0未被干预，1被干预 */
    @Column(value = "is_comd")
    private Integer isComd;

    @Column(value = "cbs_is_spike")
    private Integer cbsIsSpike;

    /** 系统盘类型<br/>Column: [system_disk_type] */
    @Column(value = "system_disk_type")
    private String systemDiskType;

    /** 单块系统盘大小，单位G<br/>Column: [system_disk_storage] */
    @Column(value = "system_disk_storage")
    private Integer systemDiskStorage;

    /** 系统盘块数<br/>Column: [system_disk_num] */
    @Column(value = "system_disk_num")
    private Integer systemDiskNum;

    /** 数据盘类型<br/>Column: [data_disk_type] */
    @Column(value = "data_disk_type")
    private String dataDiskType;

    /** 单块数据盘大小，单位G<br/>Column: [data_disk_storage] */
    @Column(value = "data_disk_storage")
    private Integer dataDiskStorage;

    /** 数据盘块数<br/>Column: [data_disk_num] */
    @Column(value = "data_disk_num")
    private Integer dataDiskNum;



    @Override
    public BigDecimal totalCoreGet() {
        return this.totalCore == null ? null : new BigDecimal(totalCore.toString());
    }

    @Override
    public BigDecimal totalGpuNumGet() {
        return this.totalGpuNum;
    }

    @Override
    public BigDecimal totalSystemDiskCapacityGet() {
        return BigDecimal.valueOf(instanceNum).multiply(BigDecimal.valueOf(systemDiskStorage));
    }

    @Override
    public BigDecimal totalDataDiskCapacityGet() {
        return BigDecimal.valueOf(instanceNum).multiply(BigDecimal.valueOf(dataDiskNum))
                .multiply(BigDecimal.valueOf(dataDiskStorage));
    }

    @Override
    public PplDemandTypeEnum demandTypeEnumGet() {
        return PplDemandTypeEnum.getByCode(this.demandType);
    }

    @Override
    public Ppl13weekProductTypeEnum productEnumGet() {
        return Ppl13weekProductTypeEnum.getByName(this.product);
    }

    /**
     * 获取年月
     */
    public String getYearMonth() {
        Integer year = getYear();
        Integer month = getMonth();
        return year + "-" + (month >= 10 ? month : "0" + month);
    }

    /**
     * 获取分组K
     */
    public String getGroupK() {
        return String.join("-",
                this.getYearMonth(), this.getCustomerShortName(), this.getCustomerUin(), this.getDemandScene(),
                this.getBillType(), this.getRegionName(), this.getDemandType(), this.getInstanceType(),
                this.getInstanceModel(), this.getWarZone(), this.getZoneName(), this.getIndustryDept());
    }

    public static DwdCrpPplItemVersionForWaveAnalysisVO copyOne(DwdCrpPplItemVersionForWaveAnalysisVO one) {
        DwdCrpPplItemVersionForWaveAnalysisVO vo = new DwdCrpPplItemVersionForWaveAnalysisVO();
        vo.setDemandType(one.getDemandType());
        vo.setDemandScene(one.getDemandScene());
        vo.setBillType(one.getBillType());
        vo.setRegionName(one.getRegionName());
        vo.setInstanceType(one.getInstanceType());
        vo.setInstanceModel(one.getInstanceModel());
        vo.setZoneName(one.getZoneName());
        vo.setInstanceNum(0);
        vo.setTotalCore(0);
        vo.setTotalDisk(0);

        vo.setYear(one.getYear());
        vo.setMonth(one.getMonth());
        vo.setCustomerShortName(one.getCustomerShortName());
        vo.setCustomerUin(one.getCustomerUin());
        vo.setIndustryDept(one.getIndustryDept());
        vo.setWarZone(one.getWarZone());

        return vo;
    }

}
