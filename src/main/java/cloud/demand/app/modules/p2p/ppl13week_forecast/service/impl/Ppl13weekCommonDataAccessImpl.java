package cloud.demand.app.modules.p2p.ppl13week_forecast.service.impl;


import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.utils.ListUtils2;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.industry_report.entity.IndustryReportAppidInfoLatestWithoutJsonDO;
import cloud.demand.app.modules.mrpv2.entity.Mrpv2CommonInstanceTypeConfigDO;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandIndustryWarZoneDictDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastConfigCustomerDefinesLatestDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastConfigCustomerDefinesYearMonthVersionDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastConfigSpikeThresholdDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictTaskDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastSourceTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekCommonDataAccess;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.redis.RedisHelper;
import com.pugwoo.wooutils.redis.Synchronized;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.jetbrains.annotations.NotNull;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import yunti.boot.exception.BizException;

/**
 * <AUTHOR>
 */
@Service
public class Ppl13weekCommonDataAccessImpl implements Ppl13weekCommonDataAccess {

    @Resource
    RedisHelper redisHelper;
    @Resource
    DBHelper demandDBHelper;
    @Resource
    private DBHelper obsDBHelper;



    @Override
    public  @NotNull Map<String, String> getPurchaseNewInstanceTypeMap() {
        List<Mrpv2CommonInstanceTypeConfigDO> instanceTypeConfig
                = DBList.demandDBHelper.getAll(Mrpv2CommonInstanceTypeConfigDO.class, "where use_forecast=1");
        Map<String, String> purchaseNewTypeMap = new HashMap<>();
        for (Mrpv2CommonInstanceTypeConfigDO config : instanceTypeConfig) {
            String instanceTypes = config.getInstanceTypes();
            String purchaseNewInstanceType = config.getPurchaseNewInstanceType();
            for (String s : instanceTypes.split(",")) {
                if (!Strings.isBlank(s.trim())) {
                    purchaseNewTypeMap.put(s.trim(), purchaseNewInstanceType);
                }
            }
        }
        return purchaseNewTypeMap;
    }


    @Override
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 3600)
    public Set<String> getHeadCustomerShortName() {
        String sql = "select customer_name from cloud_demand.industry_demand_industry_war_zone_dict\n"
                + "where deleted = 0  and customer_name != '' and  is_big_customer = 1";
        List<String> result = demandDBHelper.getRaw(String.class, sql);
        return new HashSet<>(result);
    }

    @Override
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 3600)
    public Set<String> getAllHeadCustomerShortNameForCBS() {

        //获取当月的CBS大客户名单
        LocalDate today = DateUtils.today();
        List<String> all = demandDBHelper.getRaw(String.class,
                "select distinct customer_short_name from ppl_forecast_config_cbs_head_customer\n"
                        + "where deleted = 0 and year = ? and month = ?", today.getYear(), today.getMonthValue());
        if (ListUtils.isEmpty(all)) {
            //当月CBS大客户名单未生成 获取表中最新的CBS大客户名单
            all = demandDBHelper.getRaw(String.class, "select distinct customer_short_name from ppl_forecast_config_cbs_head_customer\n"
                    + "where deleted = 0 and task_id = (select max(task_id) from"
                    + " ppl_forecast_config_cbs_head_customer where deleted = 0)");
        }
        return new HashSet<>(all);
    }

    @Override
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 3600)
    public Map<String, Set<String>> getHeadCustomerShortNameGroup() {
        String whereSql = "where deleted = 0  and customer_name != '' and  is_big_customer = 1";
        List<IndustryDemandIndustryWarZoneDictDO> all =
                demandDBHelper.getAll(IndustryDemandIndustryWarZoneDictDO.class,whereSql);
        return ListUtils2.groupAndApply(all,
                IndustryDemandIndustryWarZoneDictDO::getCommonCustomerName,
                (k, v) -> v.stream().map(IndustryDemandIndustryWarZoneDictDO::getCustomerName)
                        .collect(Collectors.toSet()) );
    }

    @Override
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 3600)
    public List<String> getAllBlackInstanceType() {
        // 2023-09-12 切换黑名单列表
        // 获取黑名单机型，读取中长尾的时候过滤掉
        String blackListSql = "select instance_types from mrpv2_common_instance_type_config where use_forecast = 0";
        List<String> instanceBlackDBList = demandDBHelper.getRaw(String.class, blackListSql);

        return instanceBlackDBList.stream()
                .flatMap(item -> Arrays.stream(item.split(",")))
                .map(String::trim)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    @HiSpeedCache(expireSecond = 60*3)
    public List<PplForecastConfigSpikeThresholdDO> getAllSpikeThreshold() {
        return DBList.demandDBHelper.getAll(PplForecastConfigSpikeThresholdDO.class);
    }

    @Override
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 3600)
    public List<String> getAllBlackInstanceTypeForMiddleForecast() {
        List<String> instanceTypeBlackList = new ArrayList<>();
        List<PplForecastConfigSpikeThresholdDO> blackList = DBList.demandDBHelper.getAll(
                PplForecastConfigSpikeThresholdDO.class, "where threshold<0");
        for (PplForecastConfigSpikeThresholdDO black : blackList) {
            instanceTypeBlackList.addAll(black.getSplitInstanceTypes());
        }
        return instanceTypeBlackList;
    }

    @Override
    public List<PplForecastPredictTaskDO> getPplForecastPredictTaskDO(List<Long> taskIds, boolean throwException) {

        if (taskIds.size() == 0) {
            throw BizException.makeThrow("暂无预测方法");
        }

        WhereContent whereContent = new WhereContent();
        whereContent.andIn(PplForecastPredictTaskDO::getId, taskIds);

        List<PplForecastPredictTaskDO> taskVO = ORMUtils.db(DBList.demandDBHelper)
                .getAll(PplForecastPredictTaskDO.class, whereContent);

        if (taskVO.size() != taskIds.size() && throwException) {
            throw BizException.makeThrow("未找到预测方案: taskId=%s", Strings.join(",", taskIds));
        }

        return taskVO;
    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = "createYearMonthCustomerDefinesKey", throwExceptionIfNotGetLock = false)
    public void createYearMonthCustomerDefines(LocalDate date) {

        WhereContent whereContent = new WhereContent();
        List<PplForecastConfigCustomerDefinesLatestDO> allLatestConfig = DBList.demandDBHelper.getAll(
                PplForecastConfigCustomerDefinesLatestDO.class, whereContent.getSql(), whereContent.getParams());

        long version = getVersion(date);

        appendConfigInYearMonth(date, version, allLatestConfig, Ppl13weekForecastSourceTypeEnum.INDUSTRY);
        appendConfigInYearMonth(date, version, allLatestConfig, Ppl13weekForecastSourceTypeEnum.INNER);
        appendZhanLue(date, version);
    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = "createYearMonthCustomerDefinesKey", throwExceptionIfNotGetLock = false)
    public void createVersionItemYearMonthCustomerDefines(LocalDate date) {

        long version = getVersion(date);
        // 获取上个月的数据
        appendVersionItem(date, version, Ppl13weekForecastSourceTypeEnum.INDUSTRY);
        appendVersionItem(date, version, Ppl13weekForecastSourceTypeEnum.INNER);
    }


    private long getVersion(LocalDate date) {
        long version = redisHelper.getAutoIncrementId("createYearMonthCustomerDefines") + 100;
        WhereContent ymConfigWhere = new WhereContent();
        ymConfigWhere.andEqual(PplForecastConfigCustomerDefinesYearMonthVersionDO::getYear, date.getYear());
        ymConfigWhere.andEqual(PplForecastConfigCustomerDefinesYearMonthVersionDO::getMonth, date.getMonthValue());
        PplForecastConfigCustomerDefinesYearMonthVersionDO one = DBList.demandDBHelper.getOne(
                PplForecastConfigCustomerDefinesYearMonthVersionDO.class,
                ymConfigWhere.getSql(), ymConfigWhere.getParams());
        if (one != null) {
            version = one.getVersion();
        }
        return version;
    }


    private void appendVersionItem(LocalDate date, long version, Ppl13weekForecastSourceTypeEnum sourceType) {
        List<String> product;
        if (sourceType == Ppl13weekForecastSourceTypeEnum.INDUSTRY) {
            product = Lang.list("CVM&CBS");
        } else {
            product = Lang.list("容器服务", "云数据仓库", "弹性MapReduce", "Elasticsearch Service", "CSIG容器平台", "数据湖DLC");
        }
        List<IndustryReportAppidInfoLatestWithoutJsonDO> versionAppInfo = getVersionAppInfo(product
                , date.getYear(), date.getMonthValue());

        if (sourceType == Ppl13weekForecastSourceTypeEnum.INDUSTRY) {
            versionAppInfo.addAll(getVersionAppInfoByCustomerName(product, date.getYear(), date.getMonthValue()));
        }
        versionAppInfo = versionAppInfo.stream().distinct().collect(Collectors.toList());

        // 用5月的数据作为固定名单
        LocalDate date1 = date;
        if (date.isBefore(LocalDate.of(2023, 5, 1))) {
            date1 = LocalDate.of(2023, 5, 1);
        }

        WhereContent ymConfigWhere = new WhereContent();
        ymConfigWhere.andEqual(PplForecastConfigCustomerDefinesYearMonthVersionDO::getYear, date1.getYear());
        ymConfigWhere.andEqual(PplForecastConfigCustomerDefinesYearMonthVersionDO::getSourceType, sourceType.getCode());
        ymConfigWhere.andEqual(PplForecastConfigCustomerDefinesYearMonthVersionDO::getMonth, date1.getMonthValue());
        List<PplForecastConfigCustomerDefinesYearMonthVersionDO> all = DBList.demandDBHelper.getAll(
                PplForecastConfigCustomerDefinesYearMonthVersionDO.class,
                ymConfigWhere.getSql(),
                ymConfigWhere.getParams());

        List<PplForecastConfigCustomerDefinesYearMonthVersionDO> insert = Lang.list();
        for (IndustryReportAppidInfoLatestWithoutJsonDO industryDatum : versionAppInfo) {
            if (ListUtils.contains(all, (o) -> Strings.equals(o.getAppid(), industryDatum.getAppid().toString()))) {
                continue;
            }
            PplForecastConfigCustomerDefinesYearMonthVersionDO trans = trans(industryDatum);
            trans.setMonth(date.getMonthValue());
            trans.setYear(date.getYear());
            trans.setVersion(version);
            trans.setLabel("非固定名单");
            trans.setSourceType(sourceType.getCode());
            trans.setReamrk("系统生成版本中的名单");
            insert.add(trans);
        }

        // 行业的加上战略客户部的
        DBList.demandDBHelper.insert(insert);
    }

    private void appendZhanLue(LocalDate date, long version) {
        List<IndustryReportAppidInfoLatestWithoutJsonDO> zhanLueAppInfo = getZhanLueAppInfo();

        WhereContent ymConfigWhere = new WhereContent();
        ymConfigWhere.andEqual(PplForecastConfigCustomerDefinesYearMonthVersionDO::getYear, date.getYear());
        ymConfigWhere.andEqual(PplForecastConfigCustomerDefinesYearMonthVersionDO::getMonth, date.getMonthValue());
        List<PplForecastConfigCustomerDefinesYearMonthVersionDO> all = DBList.demandDBHelper.getAll(
                PplForecastConfigCustomerDefinesYearMonthVersionDO.class,
                ymConfigWhere.getSql(),
                ymConfigWhere.getParams());

        List<PplForecastConfigCustomerDefinesYearMonthVersionDO> insert = Lang.list();
        for (IndustryReportAppidInfoLatestWithoutJsonDO industryDatum : zhanLueAppInfo) {
            appendIfNotExit(date, version, Ppl13weekForecastSourceTypeEnum.INDUSTRY, all, insert, industryDatum);
            appendIfNotExit(date, version, Ppl13weekForecastSourceTypeEnum.INNER, all, insert, industryDatum);
        }
        DBList.demandDBHelper.insert(insert);
    }

    private void appendIfNotExit(LocalDate date, long version, Ppl13weekForecastSourceTypeEnum sourceType,
            List<PplForecastConfigCustomerDefinesYearMonthVersionDO> all,
            List<PplForecastConfigCustomerDefinesYearMonthVersionDO> insert,
            IndustryReportAppidInfoLatestWithoutJsonDO industryDatum) {
        if (!ListUtils.contains(all, (o) -> Strings.equals(o.getAppid(), industryDatum.getAppid().toString())
                && Strings.equals(o.getSourceType(), sourceType.getCode()))) {
            PplForecastConfigCustomerDefinesYearMonthVersionDO trans = trans(industryDatum);
            trans.setMonth(date.getMonthValue());
            trans.setYear(date.getYear());
            trans.setVersion(version);
            trans.setLabel("固定名单");
            trans.setSourceType(sourceType.getCode());
            trans.setReamrk("系统生成战略客户部");
            insert.add(trans);
        }
    }

    private void appendConfigInYearMonth(LocalDate date,
            long version,
            List<PplForecastConfigCustomerDefinesLatestDO> allLatestConfig,
            Ppl13weekForecastSourceTypeEnum sourceType) {

        List<PplForecastConfigCustomerDefinesLatestDO> industryConfig = ListUtils.filter(allLatestConfig,
                (o) -> Strings.equals(sourceType.getCode(), o.getSourceType()));

        List<String> uin = ListUtils.transform(industryConfig, PplForecastConfigCustomerDefinesLatestDO::getUin);

        List<IndustryReportAppidInfoLatestWithoutJsonDO> allConfig = DBList.demandDBHelper.getAll(
                IndustryReportAppidInfoLatestWithoutJsonDO.class, "where uin in (?)", uin);

        // 行业的使用客户简称去找数据，内领的它是什么就是什么
        if (sourceType == Ppl13weekForecastSourceTypeEnum.INDUSTRY) {
            List<IndustryReportAppidInfoLatestWithoutJsonDO> byUinCustomerShortName =
                    getAppInfoByUinCustomerShortName(uin);
            allConfig.addAll(byUinCustomerShortName);
            allConfig = allConfig.stream().distinct().collect(Collectors.toList());
        }

        WhereContent ymConfigWhere = new WhereContent();
        ymConfigWhere.andEqual(PplForecastConfigCustomerDefinesYearMonthVersionDO::getYear, date.getYear());
        ymConfigWhere.andEqual(PplForecastConfigCustomerDefinesYearMonthVersionDO::getSourceType, sourceType.getCode());
        ymConfigWhere.andEqual(PplForecastConfigCustomerDefinesYearMonthVersionDO::getMonth, date.getMonthValue());
        List<PplForecastConfigCustomerDefinesYearMonthVersionDO> all = DBList.demandDBHelper.getAll(
                PplForecastConfigCustomerDefinesYearMonthVersionDO.class,
                ymConfigWhere.getSql(),
                ymConfigWhere.getParams());

        List<PplForecastConfigCustomerDefinesYearMonthVersionDO> insert = Lang.list();
        for (IndustryReportAppidInfoLatestWithoutJsonDO oneConfig : allConfig) {
            if (ListUtils.contains(all, (o) -> Strings.equals(o.getAppid(), oneConfig.getAppid().toString()))) {
                continue;
            }
            PplForecastConfigCustomerDefinesYearMonthVersionDO trans = trans(oneConfig);
            trans.setMonth(date.getMonthValue());
            trans.setYear(date.getYear());
            trans.setVersion(version);
            trans.setLabel("固定名单");
            trans.setSourceType(sourceType.getCode());
            trans.setReamrk("系统生成用户配置的名单");
            insert.add(trans);
        }
        // 行业的加上战略客户部的
        DBList.demandDBHelper.insert(insert);
    }

    private PplForecastConfigCustomerDefinesYearMonthVersionDO trans(IndustryReportAppidInfoLatestWithoutJsonDO s) {
        PplForecastConfigCustomerDefinesYearMonthVersionDO pplForecastConfigCustomerDefinesYearMonthVersionDO = new PplForecastConfigCustomerDefinesYearMonthVersionDO();
        pplForecastConfigCustomerDefinesYearMonthVersionDO.setUin(String.valueOf(s.getUin()));
        pplForecastConfigCustomerDefinesYearMonthVersionDO.setAppid(String.valueOf(s.getAppid()));
        pplForecastConfigCustomerDefinesYearMonthVersionDO.setCustomerName(s.getCustomerName());
        pplForecastConfigCustomerDefinesYearMonthVersionDO.setCustomerShortName(s.getCustomerShortName());
        return pplForecastConfigCustomerDefinesYearMonthVersionDO;
    }


    public List<IndustryReportAppidInfoLatestWithoutJsonDO> getZhanLueAppInfo() {
        String sql = "where industry_dept='战略客户部'";
        return DBList.demandDBHelper.getAll(
                IndustryReportAppidInfoLatestWithoutJsonDO.class, sql);
    }


    private List<IndustryReportAppidInfoLatestWithoutJsonDO> getVersionAppInfo(List<String> product, int year,
            int month) {
        String sq = "where uin in (select distinct customer_uin\n"
                + "                              from ppl_order\n"
                + "                              where  deleted = 0\n"
                + "                                and source != 'FORECAST'\n"
                + "                                and ppl_order in (select ppl_order\n"
                + "                                                  from ppl_version_group_record_item\n"
                + "                                                  where deleted = 0\n"
                + "                                                    and version_group_record_id in (select max(id)\n"
                + "                                                                                    from ppl_version_group_record\n"
                + "                                                                                    group by version_group_id)\n"
                + "                                                    and product in (?)\n"
                + "                                                    and year(begin_buy_date) = ?\n"
                + "                                                    and month(begin_buy_date) = ?))";

        return DBList.demandDBHelper.getAll(
                IndustryReportAppidInfoLatestWithoutJsonDO.class, sq, product, year, month);
    }

    private List<IndustryReportAppidInfoLatestWithoutJsonDO> getVersionAppInfoByCustomerName(
            List<String> product, int year, int month) {
        String sq = "where customer_short_name in (select distinct customer_short_name\n"
                + "                              from ppl_order\n"
                + "                              where customer_short_name != ''\n"
                + "    and customer_short_name not in ('腾讯计算机系统有限公司','深圳市腾讯计算机系统有限公司','腾讯科技(深圳)有限公司') "
                + "                                and deleted = 0\n"
                + "                                and source != 'FORECAST'\n"
                + "                                and source != 'APPLY_AUTO_FILL_LONGTAIL'\n"
                + "                                and source != 'SYNC_YUNXIAO'\n"
                + "                                and ppl_order in (select ppl_order\n"
                + "                                                  from ppl_version_group_record_item\n"
                + "                                                  where deleted = 0\n"
                + "                                                    and version_group_record_id in (select max(id)\n"
                + "                                                                                    from ppl_version_group_record\n"
                + "                                                                                    group by version_group_id)\n"
                + "                                                    and product in (?)\n"
                + "                                                    and year(begin_buy_date) = ?\n"
                + "                                                    and month(begin_buy_date) = ?))";

        return DBList.demandDBHelper.getAll(
                IndustryReportAppidInfoLatestWithoutJsonDO.class, sq, product, year, month);
    }

    /**
     * withOut 深圳市腾讯计算机系统有限公司,腾讯科技(深圳)有限公司
     *
     * @param orderUin uin
     * @return list
     */
    @Override
    public List<IndustryReportAppidInfoLatestWithoutJsonDO> getAppInfoByUinCustomerShortName(List<String> orderUin) {
        String sq = "where customer_short_name in (\n "
                + "select distinct customer_short_name \n"
                + "from industry_report_appid_info_latest where uin in (?) "
                + "and customer_short_name != ''"
                + "and customer_short_name not in ('腾讯计算机系统有限公司','深圳市腾讯计算机系统有限公司','腾讯科技(深圳)有限公司') )";
        List<IndustryReportAppidInfoLatestWithoutJsonDO> all1 = DBList.demandDBHelper.getAll(
                IndustryReportAppidInfoLatestWithoutJsonDO.class, sq, orderUin);
        List<IndustryReportAppidInfoLatestWithoutJsonDO> all = DBList.demandDBHelper.getAll(
                IndustryReportAppidInfoLatestWithoutJsonDO.class, "where uin in (?)", orderUin);
        all.addAll(all1);
        return new ArrayList<>(ListUtils.toSet(all, (o) -> o));
    }

    @Override
    public void createAllCustomerDefines() {
        LocalDate start = LocalDate.of(2022, 11, 1);
        LocalDate end = LocalDate.of(2023, 5, 1);
        while (!start.isAfter(end)) {
            SpringUtil.getBean(Ppl13weekCommonDataAccessImpl.class).createVersionItemYearMonthCustomerDefines(start);
            start = start.plusMonths(1);
        }
    }

    @Override
    public Map<String, String> getZiyanGinsfamily2DeviceGroup() {
        // version 2
        String sql = "select distinct CvmInstanceTypeCode,CvmInstanceGroup from bas_obs_cloud_cvm_type";
        return ORMUtils.db(obsDBHelper).getKVMap(sql);
    }
}
