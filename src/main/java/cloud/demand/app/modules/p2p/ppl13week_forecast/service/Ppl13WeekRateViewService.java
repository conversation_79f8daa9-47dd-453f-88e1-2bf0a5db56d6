package cloud.demand.app.modules.p2p.ppl13week_forecast.service;


import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryRateViewDetailReq;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryRateViewDetailRsp;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryRateViewFittingPlotCommonReq;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryRateViewFittingPlotReq;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryRateViewFittingPlotRsp;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QuerySpikeDetailReq;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QuerySpikeDetailResp;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QuerySplitDetailForPplReq;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.rsp.QuerySplitDetailForPplResp;


/**
 * <AUTHOR>
 */
public interface Ppl13WeekRateViewService {

    /**
     * 查询宽表的趋势图
     *
     * @param req req
     * @return data
     */
    QueryRateViewFittingPlotRsp queryRateViewFittingPlot(QueryRateViewFittingPlotReq req);

    /**
     * 同 queryRateViewFittingPlot, 有一个参数固定下来，不需要他们传参数
     * @param req req
     * @return data
     */
    QueryRateViewFittingPlotRsp queryRateViewFittingPlotForMrp(QueryRateViewFittingPlotCommonReq req);

    /**
     * 查询拆分的明细数据给ppl
     * @param req req
     * @return ret
     */
    QuerySplitDetailForPplResp querySplitDetailForPpl(QuerySplitDetailForPplReq req);

    /**
     *  查询最明细的数据
     * @param req req
     * @return rsp
     */
    QueryRateViewDetailRsp queryRateViewDetail(QueryRateViewDetailReq req);

    /**
     * 查询毛刺明细
     * @param req req
     * @return rsp
     */
    QuerySpikeDetailResp querySpikeDetail(QuerySpikeDetailReq req);

}
