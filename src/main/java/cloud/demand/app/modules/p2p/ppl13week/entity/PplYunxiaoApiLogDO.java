package cloud.demand.app.modules.p2p.ppl13week.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("ppl_yunxiao_api_log")
public class PplYunxiaoApiLogDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    @Column(value = "url")
    private String url;

    @Column(value = "method")
    private String method;

    /**
     * 请求体<br/>Column: [req]
     */
    @Column(value = "req", maxStringLength = 16777000)
    private String req;

    /**
     * 返回体<br/>Column: [resp]
     */
    @Column(value = "resp", maxStringLength = 16777000)
    private String resp;

}