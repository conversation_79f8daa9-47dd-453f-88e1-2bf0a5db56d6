package cloud.demand.app.modules.p2p.ppl13week.entity.std_table;

import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplCosAZEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplCosStorageEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDataBaseFrameworkEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDatabaseEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDatabaseStorageEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDeloyTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.listener.tool.FieldComparator.ChineseName;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("dws_crp_ppl_item_version_532_new_mif")
public class DwsCrpPplItemVersion532NewMifDO implements SumTotalCoreOrGpu {

    /**
     * 版本代号<br/>Column: [version_code]
     */
    @Column(value = "version_code")
    private String versionCode;

    /**
     * 版本名称<br/>Column: [version_name]
     */
    @Column(value = "version_name")
    private String versionName;

    /**
     * 需求年月，分区维度，例：202308<br/>Column: [year_month]
     */
    @Column(value = "year_month")
    private Integer yearMonth;

    /**
     * 版本所属年<br/>Column: [version_year]
     */
    @Column(value = "version_year")
    private Integer versionYear;

    /**
     * 版本所属月<br/>Column: [version_month]
     */
    @Column(value = "version_month")
    private Integer versionMonth;

    /**
     * 版本预测需求开始年<br/>Column: [version_begin_year]
     */
    @Column(value = "version_begin_year")
    private Integer versionBeginYear;

    /**
     * 版本预测需求开始月<br/>Column: [version_begin_month]
     */
    @Column(value = "version_begin_month")
    private Integer versionBeginMonth;

    /**
     * 月度权值。若版本月为2月，则预测月为5月时，月度权值为0.5；预测月为4月时，月度权值为0.3；预测月为3月时，月度权值为0.2<br/>Column: [month_weight]
     */
    @Column(value = "month_weight")
    private BigDecimal monthWeight;

    /**
     * 月度内版本次数权值。计算方式为 1 / 月度内的版本次数<br/>Column: [month_version_count_weight]
     */
    @Column(value = "month_version_count_weight")
    private BigDecimal monthVersionCountWeight;

    /**
     * pplId,唯一<br/>Column: [ppl_id]
     */
    @Column(value = "ppl_id")
    private String pplId;

    /**
     * ppl单号<br/>Column: [ppl_order]
     */
    @Column(value = "ppl_order")
    private String pplOrder;

    /**
     * ppl更新时间<br/>Column: [update_time]
     */
    @Column(value = "update_time")
    private Date updateTime;

    /**
     * ppl状态，VALID已生效，APPLIED已预约<br/>Column: [status]
     */
    @Column(value = "status")
    private String status;

    /**
     * ppl状态名称<br/>Column: [status_name]
     */
    @Column(value = "status_name")
    private String statusName;

    /**
     * ppl需求台数，原始预测值*月度权重*月度内版本次数权值<br/>Column: [instance_num]
     */
    @Column(value = "instance_num")
    private BigDecimal instanceNum;

    /**
     * ppl需求台数，原始预测值<br/>Column: [original_instance_num]
     */
    @Column(value = "original_instance_num")
    private Integer originalInstanceNum;

    /**
     * ppl需求总核心数，原始预测值*月度权重*月度内版本次数权值<br/>Column: [total_core]
     */
    @Column(value = "total_core")
    private BigDecimal totalCore;

    /**
     * ppl需求总核心数，原始预测值<br/>Column: [original_total_core]
     */
    @Column(value = "original_total_core")
    private Integer originalTotalCore;

    /**
     * ppl来源<br/>Column: [source]
     */
    @Column(value = "source")
    private String source;

    /**
     * ppl来源名称<br/>Column: [source_name]
     */
    @Column(value = "source_name")
    private String sourceName;

    /**
     * ppl需求-年<br/>Column: [year]
     */
    @Column(value = "year")
    private Integer year;

    /**
     * ppl需求-月<br/>Column: [month]
     */
    @Column(value = "month")
    private Integer month;

    /**
     * 客户uin<br/>Column: [customer_uin]
     */
    @Column(value = "customer_uin")
    private String customerUin;

    /**
     * 行业<br/>Column: [industry]
     */
    @Column(value = "industry")
    private String industry;

    /**
     * 行业部门<br/>Column: [industry_dept]
     */
    @Column(value = "industry_dept")
    private String industryDept;

    /**
     * 客户类型<br/>Column: [customer_type]
     */
    @Column(value = "customer_type")
    private String customerType;

    /**
     * 客户类型名称<br/>Column: [customer_type_name]
     */
    @Column(value = "customer_type_name")
    private String customerTypeName;

    /**
     * 客户名称<br/>Column: [customer_name]
     */
    @Column(value = "customer_name")
    private String customerName;

    /**
     * 客户简称<br/>Column: [customer_short_name]
     */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /**
     * 战区<br/>Column: [war_zone]
     */
    @Column(value = "war_zone")
    private String warZone;

    /**
     * 客户来源<br/>Column: [customer_source]
     */
    @Column(value = "customer_source")
    private String customerSource;

    /**
     * 需求提交人，一般叫架构师<br/>Column: [submit_user]
     */
    @Column(value = "submit_user")
    private String submitUser;

    /**
     * 需求所属产品，例如CVM&CBS<br/>Column: [product]
     */
    @Column(value = "product")
    private String product;

    /**
     * 需求类型，NEW新增，ELASTIC弹性，RETURN退回<br/>Column: [demand_type]
     */
    @Column(value = "demand_type")
    private String demandType;

    /**
     * 需求场景名称<br/>Column: [demand_type_name]
     */
    @Column(value = "demand_type_name")
    private String demandTypeName;

    /**
     * 需求场景<br/>Column: [demand_scene]
     */
    @Column(value = "demand_scene")
    private String demandScene;

    /**
     * 项目名称<br/>Column: [project_name]
     */
    @Column(value = "project_name")
    private String projectName;

    /**
     * 计费模式<br/>Column: [bill_type]
     */
    @Column(value = "bill_type")
    private String billType;

    /**
     * 赢率<br/>Column: [win_rate]
     */
    @Column(value = "win_rate")
    private BigDecimal winRate;

    /**
     * 开始购买日期<br/>Column: [begin_buy_date]
     */
    @Column(value = "begin_buy_date")
    private LocalDate beginBuyDate;

    /**
     * 结束购买日期<br/>Column: [end_buy_date]
     */
    @Column(value = "end_buy_date")
    private LocalDate endBuyDate;

    /**
     * 弹性开始时间 HH:mm<br/>Column: [begin_elastic_time]
     */
    @Column(value = "begin_elastic_time")
    private String beginElasticTime;

    /**
     * 弹性结束时间 HH:mm<br/>Column: [end_elastic_time]
     */
    @Column(value = "end_elastic_time")
    private String endElasticTime;

    /**
     * 需求备注<br/>Column: [note]
     */
    @Column(value = "note")
    private String note;

    /**
     * 境内外<br/>Column: [customhouse_title]
     */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /**
     * 地域<br/>Column: [region_name]
     */
    @Column(value = "region_name")
    private String regionName;

    /**
     * 可用区<br/>Column: [zone_name]
     */
    @Column(value = "zone_name")
    private String zoneName;

    /**
     * 实例大类<br/>Column: [instance_type]
     */
    @Column(value = "instance_type")
    private String instanceType;

    /**
     * 实例规格<br/>Column: [instance_model]
     */
    @Column(value = "instance_model")
    private String instanceModel;

    /**
     * ppl需求总磁盘数<br/>Column: [total_disk]
     */
    @Column(value = "total_disk")
    private Integer totalDisk;

    /**
     * 可接受的其它实例类型，分号隔开<br/>Column: [alternative_instance_type]
     */
    @Column(value = "alternative_instance_type")
    private String alternativeInstanceType;

    /**
     * 亲和度类型<br/>Column: [affinity_type]
     */
    @Column(value = "affinity_type")
    private String affinityType;

    /**
     * 亲和度值<br/>Column: [affinity_value]
     */
    @Column(value = "affinity_value")
    private BigDecimal affinityValue;

    /**
     * 系统盘类型<br/>Column: [system_disk_type]
     */
    @Column(value = "system_disk_type")
    private String systemDiskType;

    /**
     * 单块系统盘大小，单位G<br/>Column: [system_disk_storage]
     */
    @Column(value = "system_disk_storage")
    private Integer systemDiskStorage;

    /**
     * 系统盘块数<br/>Column: [system_disk_num]
     */
    @Column(value = "system_disk_num")
    private Integer systemDiskNum;

    /**
     * 数据盘类型<br/>Column: [data_disk_type]
     */
    @Column(value = "data_disk_type")
    private String dataDiskType;

    /**
     * 单块数据盘大小，单位G<br/>Column: [data_disk_storage]
     */
    @Column(value = "data_disk_storage")
    private Integer dataDiskStorage;

    /**
     * 数据盘块数<br/>Column: [data_disk_num]
     */
    @Column(value = "data_disk_num")
    private Integer dataDiskNum;

    /**
     * 卡型/gpu类型<br/>Column: [gpu_type]
     */
    @Column(value = "gpu_type")
    private String gpuType;

    /**
     * 单台GPU卡数<br/>Column: [gpu_num]
     */
    @Column(value = "gpu_num")
    private BigDecimal gpuNum;

    /**
     * GPU总卡数，原始预测值*月度权重*月度内版本次数权值<br/>Column: [total_gpu_num]
     */
    @Column(value = "total_gpu_num")
    private BigDecimal totalGpuNum;

    /**
     * GPU总卡数，原始预测值<br/>Column: [original_total_gpu_num]
     */
    @Column(value = "original_total_gpu_num")
    private BigDecimal originalTotalGpuNum;

    /**
     * 是否接受调整卡型，1接受，0不接受<br/>Column: [is_accept_adjust_gpu]
     */
    @Column(value = "is_accept_adjust_gpu")
    private Integer isAcceptAdjustGpu;

    /**
     * 接受卡型，用;分割<br/>Column: [accept_gpu]
     */
    @Column(value = "accept_gpu")
    private String acceptGpu;

    /**
     * GPU业务场景<br/>Column: [biz_scene]
     */
    @Column(value = "biz_scene")
    private String bizScene;

    /**
     * GPU业务详情<br/>Column: [biz_detail]
     */
    @Column(value = "biz_detail")
    private String bizDetail;

    /**
     * 使用时长，x（月）/长期稳定使用<br/>Column: [service_time]
     */
    @Column(value = "service_time")
    private String serviceTime;

    /**
     * gpu产品形态 裸金属/CVM<br/>Column: [gpu_product_type]
     */
    @Column(value = "gpu_product_type")
    private String gpuProductType;

    /**
     * 客户集团<br/>Column: [cname]
     */
    @Column(value = "cname")
    private String cname;

    /**
     * 客户集团id<br/>Column: [cid]
     */
    @Column(value = "cid")
    private String cid;

    /**
     * 可用区编码<br/>Column: [zone]
     */
    @Column(value = "zone")
    private String zone;

    /**
     * 区域名称<br/>Column: [area_name]
     */
    @Column(value = "area_name")
    private String areaName;

    /**
     * 磐石战区<br/>Column: [pan_shi_war_zone]
     */
    @Column(value = "pan_shi_war_zone")
    private String panShiWarZone;

    /**
     * 客户类型<br/>Column: [customer_tab_type]
     */
    @Column(value = "customer_tab_type")
    private String customerTabType;

    /**
     * 战略集团名称<br/>Column: [strategy_group_name]
     */
    @Column(value = "strategy_group_name")
    private String strategyGroupName;

    /**
     * 是否被干预，0未被干预，1被干预
     */
    @Column(value = "is_comd")
    private Integer isComd;

    /**
     * 源ppl_id
     */
    @Column(value = "source_ppl_id")
    private String sourcePplId;

    /**
     * 业务分类
     */
    @Column(value = "category")
    private String category;

    /**
     * uin类型，0内部 1外部
     */
    @Column(value = "uin_type")
    private Integer uinType;

    /**
     * 客户类型(从社会角度上看属于哪一类)，0个人 1企业
     */
    @Column(value = "customer_society_type")
    private Integer customerSocietyType;

    /**
     * 共识状态
     */
    @Column(value = "consensus_status")
    private String consensus_status;


    /**
     * 数据库名称
     * @see PplDatabaseEnum
     */
    @Column(value = "database_name")
    @ChineseName("数据库名称")
    private String databaseName;

    /**
     *  是否多AZ，数据库产品
     */
    @Column(value = "more_than_one_az")
    private Boolean moreThanOneAZ;

    /**
     * 数据库存储类型，数据库产品
     * @see PplDatabaseStorageEnum
     */
    @Column(value = "database_storage_type")
    @ChineseName("数据库存储类型")
    private String databaseStorageType;

    /**
     * 实例部署类型，数据库产品
     * @see PplDeloyTypeEnum
     */
    @Column(value = "deploy_type")
    @ChineseName("实例部署类型")
    private String deployType;

    /**
     * 实例架构类型，数据库产品
     * @see PplDataBaseFrameworkEnum
     */
    @Column(value = "framework_type")
    @ChineseName("实例架构类型")
    private String frameworkType;

    /**
     *  分片数量，数据库产品
     */
    @Column(value = "slice_num")
    @ChineseName("分片数量")
    private Integer sliceNum;

    /**
     *  副本数量，数据库产品
     */
    @Column(value = "replica_num")
    @ChineseName("副本数量")
    private Integer replicaNum;

    /**
     *  只读数量，数据库产品
     */
    @Column(value = "read_only_num")
    @ChineseName("只读数量")
    private Integer readOnlyNum;

    /**
     *  数据库实例规格（2C16G 这种），数据库产品
     */
    @Column(value = "database_specs")
    @ChineseName("数据库实例规格")
    private String databaseSpecs;

    /**
     *  数据库存储量，单位GB，数据库产品
     */
    @Column(value = "database_storage")
    private BigDecimal databaseStorage;

    /**
     *  总数据库存储量，单位GB，数据库产品
     */
    @Column(value = "total_database_storage")
    @ChineseName("总数据库存储量")
    private BigDecimal totalDatabaseStorage;

    /**
     *  COS存储类型，COS产品
     * @see PplCosStorageEnum
     */
    @Column(value = "cos_storage_type")
    @ChineseName("COS存储类型")
    private String cosStorageType;

    /**
     *  单AZ、多AZ，COS产品
     * @see PplCosAZEnum
     */
    @Column(value = "cos_az")
    private String cosAZ;

    /**
     *  COS存储量，单位PB，COS产品
     */
    @Column(value = "cos_storage")
    private BigDecimal cosStorage;

    /**
     *  带宽，单位 Gbit/s，COS产品
     */
    @Column(value = "bandwidth")
    private Integer bandwidth;

    /**
     * qps，COS产品
     */
    @Column(value = "qps")
    private Integer qps;

    /**
     *  总COS存储量，单位PB，COS产品
     */
    @Column(value = "total_cos_storage")
    @ChineseName("总COS存储量")
    private BigDecimal totalCosStorage;

    /**
     * 单台核心数<br/>
     */
    @Column(value = "instance_model_core_num")
    private Integer instanceModelCoreNum;

    /**
     * 单台内存数，单位GB<br/>
     */
    @Column(value = "instance_model_ram_num")
    private Integer instanceModelRamNum;

    /**
     * 总内存数，单位GB<br/>Column: [total_memory]
     */
    @Column(value = "total_memory")
    @ChineseName("总内存数")
    private Integer totalMemory;


    @Override
    public BigDecimal totalCoreGet() {
        return this.totalCore;
    }

    @Override
    public BigDecimal totalGpuNumGet() {
        return this.totalGpuNum;
    }

    @Override
    public BigDecimal totalSystemDiskCapacityGet() {
        return instanceNum.multiply(BigDecimal.valueOf(systemDiskStorage));
    }

    @Override
    public BigDecimal totalDataDiskCapacityGet() {
        return instanceNum.multiply(BigDecimal.valueOf(dataDiskNum))
                .multiply(BigDecimal.valueOf(dataDiskStorage));
    }

    @Override
    public PplDemandTypeEnum demandTypeEnumGet() {
        return PplDemandTypeEnum.getByCode(this.demandType);
    }

    @Override
    public Ppl13weekProductTypeEnum productEnumGet() {
        return Ppl13weekProductTypeEnum.getByName(this.product);
    }
}
