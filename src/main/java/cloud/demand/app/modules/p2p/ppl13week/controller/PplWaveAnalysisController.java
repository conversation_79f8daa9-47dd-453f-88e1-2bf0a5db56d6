package cloud.demand.app.modules.p2p.ppl13week.controller;

import cloud.demand.app.common.exception.WrongWebParameterException;
import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.QueryVersionInfoRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.QueryWaveReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.detail.QueryIndustryDeptCustomerRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.detail.QueryWaveDetailRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.overview.QueryWaveOverviewDetailRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.overview.QueryWaveOverviewGroupRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.overview.QueryWaveOverviewRsp;
import cloud.demand.app.modules.p2p.ppl13week.service.PplWaveAnalysisService;
import cloud.demand.app.web.model.common.DownloadBean;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

@JsonrpcController("/ppl13week")
@Slf4j
public class PplWaveAnalysisController {

    @Resource
    private PplWaveAnalysisService pplWaveAnalysisService;

    /**
     * 波动分析
     */
    @RequestMapping
    public QueryWaveOverviewRsp compareAnalysis(@JsonrpcParam QueryWaveReq req) {
        //  检查请求参数
        checkParam(req);
        return pplWaveAnalysisService.compareAnalysis(req);
    }

    /**
     * BY维度波动对比概览
     */
    @RequestMapping
    public QueryWaveOverviewGroupRsp waveGroupOverview(@JsonrpcParam QueryWaveReq req) {
        //  检查请求参数
        checkParam(req);
        return pplWaveAnalysisService.waveGroupOverview(req);
    }

    /**
     * BY维度波动对比明细
     */
    @RequestMapping
    public QueryWaveOverviewDetailRsp waveGroupDetail(@JsonrpcParam QueryWaveReq req) {
        //  检查请求参数
        checkParam(req);
        return pplWaveAnalysisService.waveGroupDetail(req);
    }

    /**
     * 波动分析明细
     */
    @RequestMapping
    public QueryWaveDetailRsp waveDetail(@JsonrpcParam QueryWaveReq req) {
        //  检查请求参数
        checkParam(req);
        return pplWaveAnalysisService.waveDetail(req);
    }

    @RequestMapping
    public DownloadBean exportWaveDetail(@JsonrpcParam QueryWaveReq req) {
        return pplWaveAnalysisService.exportWaveDetail(req);
    }

    /**
     * 查询最新By Month的版本号
     */
    @RequestMapping
    public QueryVersionInfoRsp queryNewestVersionInfo() {
        return pplWaveAnalysisService.queryNewestVersionInfo();
    }

    @RequestMapping
    public List<Map<String, Object>> queryAllIndustryDeptForWaveDetail() {
        return pplWaveAnalysisService.queryAllIndustryDeptForWaveDetail();
    }

    @RequestMapping
    public QueryIndustryDeptCustomerRsp queryIndustryDeptCustomerTree(QueryWaveReq req) {
        return pplWaveAnalysisService.queryIndustryDeptCustomerTree(req);
    }

    @RequestMapping
    public Map<String, List<String>> queryWaveProductCategory() {
        return pplWaveAnalysisService.queryWaveProductCategory();
    }

    /**
     * 检查请求参数
     */
    private void checkParam(QueryWaveReq req) {
        if (req == null || req.getBeginYearMonth() == null || req.getEndYearMonth() == null) {
            throw new WrongWebParameterException("需求年月必填，请重试");
        }
        if (req.getBeginYearMonth().after(req.getEndYearMonth())) {
            throw new WrongWebParameterException("开始时间晚于结束时间，参数错误");
        }
    }

}
