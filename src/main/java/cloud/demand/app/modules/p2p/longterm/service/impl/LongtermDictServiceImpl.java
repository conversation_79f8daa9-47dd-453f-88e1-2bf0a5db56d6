package cloud.demand.app.modules.p2p.longterm.service.impl;

import cloud.demand.app.common.config.CacheConfiguration.SynchronizedHiSpeedCache1Second;
import cloud.demand.app.common.utils.EStream;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.entity.plan.StaticGinstypeDO;
import cloud.demand.app.entity.plan.StaticZoneDO;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.p2p.longterm.service.LongtermDictService;
import cloud.demand.app.modules.soe.entitiy.dict.SoeRegionNameCountryDO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;
import yunti.boot.config.DynamicProperty;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Service
public class LongtermDictServiceImpl implements LongtermDictService {

    @Resource
    private DictService dictService;
    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private LongtermDictService self;

    // 标准型:S5,S6; 计算型: M; 分割
    private static final Supplier<String> rainbowRegionInfos
            = DynamicProperty.create("longterm.regionInfos", "");


    @Override
    @HiSpeedCache(expireSecond = 600)
    @SynchronizedHiSpeedCache1Second
    public Map<String, SoeRegionNameCountryDO> getRegionNameInfoMap() {
        return demandDBHelper.getAll(SoeRegionNameCountryDO.class)
                .stream()
                .collect(Collectors.toMap(SoeRegionNameCountryDO::getRegionName, Function.identity(), (a, b) -> a));
    }

    @Override
    public Map<String, List<String>> getCustomhouseTitleToRegionName(Set<String> regionName) {
        Map<String, SoeRegionNameCountryDO> regionNameInfoMap = self.getRegionNameInfoMap();

        Map<String, String> regionToCustomhouseTitle =
                ListUtils.toMap(regionNameInfoMap.entrySet(), (o) -> o.getKey(), o -> o.getValue().getCustomhouseTitle());
        String cs = rainbowRegionInfos.get();
        if (Strings.isNotBlank(cs)) {
            Arrays.stream(cs.split(";")).filter(Strings::isNotBlank)
                    .filter(o -> o.contains(":"))
                    .forEach(o -> {
                        String[] parts = o.split(":");
                        regionToCustomhouseTitle.put(parts[1], parts[0]);
                    });
        }
        List<String> innerRegionName = regionName.stream()
                .filter((o) -> Strings.equals("境内", regionToCustomhouseTitle.get(o)))
                .collect(Collectors.toList());
        List<String> outRegionName = regionName.stream()
                .filter((o) -> Strings.equals("境外", regionToCustomhouseTitle.get(o)))
                .collect(Collectors.toList());
        List<String> otherRegionName = regionName.stream().filter((o) ->
                        !Strings.equals("境外", regionToCustomhouseTitle.get(o))
                                && !Strings.equals("境内", regionToCustomhouseTitle.get(o)))
                .collect(Collectors.toList());
        innerRegionName.add("随机地域");

        Map<String, List<String>> crMap = new HashMap<>();
        crMap.put("境内",innerRegionName);
        crMap.put("境外",outRegionName);
        crMap.put("其它",otherRegionName);
        return crMap;
    }

    @Override
    public Map<String, List<String>> queryRegionNameToZoneName() {

        List<StaticZoneDO> staticZoneDOS = dictService.queryStaticZone();

        Map<String, Set<String>> ret = ListUtils.toMapSet(staticZoneDOS,
                StaticZoneDO::getRegionName, StaticZoneDO::getZoneName);

        String cs = rainbowRegionInfos.get();
        if (Strings.isNotBlank(cs)) {
            Arrays.stream(cs.split(";")).filter(Strings::isNotBlank)
                    .filter(o -> o.contains(":"))
                    .forEach(o -> {
                        String[] parts = o.split(":");
                        String key = parts[1];
                        List<String> valueList = Arrays.stream(parts[2].split(","))
                                .filter(Strings::isNotBlank).distinct().collect(Collectors.toList());
                        if (Strings.isNotBlank(key) && Lang.isNotEmpty(valueList)) {
                            Set<String> orDefault = ret.getOrDefault(key, new HashSet<>());
                            valueList.stream().filter((k) -> !orDefault.contains(k)).forEach(orDefault::add);
                            ret.put(key, orDefault);
                        }
                    });
        }
        return ret.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> EStream.of(entry.getValue()).sorted().toList()));
    }


    // 标准型:S5,S6; 计算型: M; 分割
    private static final Supplier<String> rainbowInstanceInfos
            = DynamicProperty.create("longterm.instanceInfos", "");


    @Override
    public Map<String, List<String>> queryProductToInstanceFamily() {
                return ORMUtils.db(demandDBHelper)
                .getKVListMap("select distinct product,instance_family\n"
                        + "from longterm_config_product_to_instance_family");
    }

    @Override
    public Map<String, List<String>> queryInstanceFamilyToInstanceType() {

        Collection<StaticGinstypeDO> values = dictService.getAllInstanceTypes().values();

        Map<String, Set<String>> ret = ListUtils.toMapSet(values,
                StaticGinstypeDO::getGinskingdomName, StaticGinstypeDO::getGinsfamily);

        String cs = rainbowInstanceInfos.get();
        if (Strings.isNotBlank(cs)) {
            Arrays.stream(cs.split(";")).filter(Strings::isNotBlank)
                    .filter(o -> o.contains(":"))
                    .forEach(o -> {
                        String[] parts = o.split(":");
                        String key = parts[0];
                        List<String> valueList = Arrays.stream(parts[1].split(","))
                                .filter(Strings::isNotBlank).distinct().collect(Collectors.toList());
                        if (Strings.isNotBlank(key) && Lang.isNotEmpty(valueList)) {
                            Set<String> orDefault = ret.getOrDefault(key, new HashSet<>());
                            valueList.stream().filter((k) -> !orDefault.contains(k)).forEach(orDefault::add);
                            ret.put(key, orDefault);
                        }
                    });

        }

        Map<String, List<String>> kvMap = ORMUtils.db(demandDBHelper)
                .getKVListMap("select distinct instance_family,instance_type\n"
                        + "from longterm_config_instance_family_to_instance_type");
        kvMap.forEach((k,v)->{
            Set<String> orDefault = ret.getOrDefault(k, new HashSet<>());
            orDefault.addAll(v);
            ret.put(k, orDefault);
        });

        return ret.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> EStream.of(entry.getValue()).sorted().toList()));
    }


    // 标准型:S5,S6; 计算型: M; 分割
    private static final Supplier<String> rainbowGpuTypes
            = DynamicProperty.create("longterm.gpuTypes", "");

    @Override
    @SynchronizedHiSpeedCache1Second
    @HiSpeedCache(expireSecond = 30)
    public List<String> queryGpuType() {

        String sql = "select distinct gpu_type\n"
                + "from cloud_demand.ppl_gpu_region_zone \n"
                + "where gpu_type IS NOT NULL and deleted = 0";
        List<String> ret = demandDBHelper.getRaw(String.class, sql);
        String cs = rainbowGpuTypes.get();
        if (Strings.isNotBlank(cs)) {
            Arrays.stream(cs.split(";")).filter(Strings::isNotBlank).forEach(ret::add);
        }
        return ret;
    }

    // 内部业务部:ES,云数仓;智慧行业一部:ES,云数仓
    private static final Supplier<String> historyControl
            = DynamicProperty.create("longterm.history.control", "");

    @Override
    public Map<String, List<String>> queryHistoryControl() {
        return convertStringToMap(historyControl.get());
    }

    public static Map<String, List<String>> convertStringToMap(String input) {
        if (input == null || input.trim().isEmpty()) {
            return new HashMap<>(); // 返回空 Map
        }

        return Arrays.stream(input.split(";"))
                .filter(part -> !part.trim().isEmpty()) // 过滤空部门条目
                .map(part -> part.split(":", 2)) // 限制拆分 2 部分，避免错误拆分
                .filter(arr -> arr.length == 2) // 确保格式正确（必须有 key:value）
                .collect(Collectors.toMap(
                        arr -> arr[0].trim(), // 去除部门名前后空格
                        arr -> {
                            String servicesStr = arr[1].trim();
                            if (servicesStr.isEmpty()) {
                                return Collections.emptyList(); // 空业务列表
                            }
                            return Arrays.stream(servicesStr.split(","))
                                    .map(String::trim)
                                    .filter(s -> !s.isEmpty())
                                    .collect(Collectors.toList());
                        },
                        (oldList, newList) -> { // 合并重复 Key 的业务列表
                            List<String> merged = new ArrayList<>(oldList);
                            merged.addAll(newList);
                            return merged;
                        },
                        HashMap::new // 保持插入顺序
                ));
    }


}
