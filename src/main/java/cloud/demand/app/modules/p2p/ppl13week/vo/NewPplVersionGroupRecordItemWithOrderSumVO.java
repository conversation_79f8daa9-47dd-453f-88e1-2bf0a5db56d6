package cloud.demand.app.modules.p2p.ppl13week.vo;

import cloud.demand.app.modules.p2p.ppl13week.vo.std_table.DwdCrpPplItemVersionForWaveViewVO;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class NewPplVersionGroupRecordItemWithOrderSumVO extends DwdCrpPplItemVersionForWaveViewVO {

    /**
     * 总核心数
     */
    private Integer sumDemandCoreNum;

    /**
     * 总卡数
     */
    private Integer sumDemandGpuNum;

    /**
     * 系统盘总容量
     */
    private BigDecimal sumSystemCapacity;

    /**
     * 数据盘总容量
     */
    private BigDecimal sumDataCapacity;

}
