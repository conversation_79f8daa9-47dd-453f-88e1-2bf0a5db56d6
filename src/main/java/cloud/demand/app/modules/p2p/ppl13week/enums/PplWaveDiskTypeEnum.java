package cloud.demand.app.modules.p2p.ppl13week.enums;

import java.util.ArrayList;
import java.util.List;
import lombok.Getter;

@Getter
public enum PplWaveDiskTypeEnum {

    SYSTEM_DISK("SYSTEM_DISK", "系统盘"),

    DATA_DISK("DATA_DISK", "数据盘");


    final private String code;

    final private String name;


    PplWaveDiskTypeEnum (String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PplWaveDiskTypeEnum getByCode(String code) {
        for (PplWaveDiskTypeEnum value : PplWaveDiskTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static List<String> getAllName() {
        List<String> result = new ArrayList<>();
        for (PplWaveDiskTypeEnum value : PplWaveDiskTypeEnum.values()) {
            result.add(value.getName());
        }
        return result;
    }
 }
