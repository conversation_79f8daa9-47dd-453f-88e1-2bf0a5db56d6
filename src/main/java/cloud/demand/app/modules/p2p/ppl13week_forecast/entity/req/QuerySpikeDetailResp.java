package cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 查询毛刺明细响应
 */
@Data
public class QuerySpikeDetailResp {

    /**毛刺列表*/
    private List<Item> spikes;

    @Data
    public static class Item {
        /** 毛刺的年*/
        private Integer year;

        /** 毛刺的月*/
        private Integer month;

        /** 行业部门*/
        private String industryDept;

        /** 客户uin*/
        private String customerUin;

        /** 客户简称，简称不一定有*/
        private String customerShortName;

        /** 机型大类*/
        private String instanceType;

        /** 地域名称*/
        private String regionName;

        /** 可用区*/
        private String zoneName;

        /** 新增核心数*/
        private BigDecimal newCore;

        /** 退回核心数*/
        private BigDecimal retCore;
    }

}
