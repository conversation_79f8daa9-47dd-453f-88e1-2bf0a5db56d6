package cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.excel;

import cloud.demand.app.common.utils.StdUtils;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.AdsMckForecastSummaryDfDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwdCrpPplItemCfDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwdCrpPplJoinOrderVersionCfDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwsCrpPplItemVersion532NewMifDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwsCrpPplItemVersionBaseCfDo;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwsCrpPplItemVersionNewestCfDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwsCrpPplJoinOrderVersionNewestCfDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.StatisticalScopeEnum;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplVersionGroupRecordItemWithOrderSumVO;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class WaveDetailExportExcelDTO {

    @ExcelProperty(index = 0, value = "PPL-ID")
    private String pplId;

    /**
     * 需求类型
     *
     * @see PplDemandTypeEnum
     */
    @ExcelProperty(index = 1, value = "需求类型")
    private String demandType;

    @ExcelProperty(index = 2, value = "产品大类")
    private String productCategory;

    @ExcelProperty(index = 3, value = "产品")
    private String product;

    @ExcelProperty(index = 4, value = "行业部门")
    private String industryDept;

    @ExcelProperty(index = 5, value = "战区")
    private String warZone;

    @ExcelProperty(index = 6, value = "客户简称")
    private String customerShortName;

    @ExcelProperty(index = 7, value = "客户UIN")
    private String customerUin;

    @ExcelProperty(index = 8, value = "可用区")
    private String zoneName;

    @ExcelProperty(index = 9, value = "地域")
    private String regionName;

    @ExcelProperty(index = 10, value = "通用实例类型")
    private String commonInstanceType;

    @ExcelProperty(index = 11, value = "实例类型")
    private String instanceType;

    @ExcelProperty(index = 12, value = "实例规格")
    private String instanceModel;

    @ExcelProperty(index = 13, value = "GPU卡型")
    private String gpuType;

    @ExcelProperty(index = 14, value = "云盘类型")
    private String volumeType;

    @ExcelProperty(index = 15, value = "云盘用途")
    private String diskType;

    /**
     * @see StatisticalScopeEnum
     */
    @ExcelProperty(index = 16, value = "统计范围")
    private String statisticalScope;

    @ExcelProperty(index = 17, value = "一级指标名称")
    private String firstLevelTargetName;

    @ExcelProperty(index = 18, value = "二级指标名称")
    private String secondLevelTargetName;

    @ExcelProperty(index = 19, value = "三级指标名称")
    private String targetName;

    @ExcelProperty(index = 20, value = "版本号")
    private String versionCode;

    @ExcelProperty(index = 21, value = "是否版本周期内数据")
    private String isInnerVersion;

    @ExcelProperty(index = 22, value = "需求年月")
    private String beginYearMonth;

    @ExcelProperty(index = 23, value = "开始购买日期")
    private String beginBuyDate;

    @ExcelProperty(index = 24, value = "结束购买日期")
    private String endBuyDate;

    @ExcelProperty(index = 25, value = "指标值")
    private Integer targetValue;

    @ExcelProperty(index = 26, value = "预约单号")
    private String yunxiaoOrderId;

    @ExcelProperty(index = 27, value = "项目名称")
    private String projectName;

    @ExcelProperty(index = 28, value = "CBS项目类型")
    private String cbsProjectType;

    public static WaveDetailExportExcelDTO transFrom(DwdCrpPplItemCfDO data) {
        WaveDetailExportExcelDTO excelDTO = new WaveDetailExportExcelDTO();
        excelDTO.setPplId(data.getPplId());
        excelDTO.setDemandType(PplDemandTypeEnum.getNameByCode(data.getDemandType()));
        excelDTO.setProduct(data.getProduct());
        excelDTO.setIndustryDept(data.getIndustryDept());
        excelDTO.setWarZone(data.getWarZone());
        excelDTO.setCustomerShortName(data.getCustomerShortName());
        excelDTO.setCustomerUin(data.getCustomerUin());
        excelDTO.setZoneName(data.getZoneName());
        excelDTO.setRegionName(data.getRegionName());
        excelDTO.setInstanceType(data.getInstanceType());
        excelDTO.setInstanceModel(data.getInstanceModel());

        excelDTO.setGpuType(StdUtils.EMPTY_STR.equals(data.getGpuType()) ? "" : data.getGpuType());

        excelDTO.setBeginYearMonth(
                String.format("%d年%d月", data.getYear(), data.getMonth()));
        excelDTO.setBeginBuyDate(data.getBeginBuyDate().toString());
        excelDTO.setEndBuyDate(data.getEndBuyDate().toString());
        excelDTO.setYunxiaoOrderId(data.getYunxiaoOrderId());
        excelDTO.setProjectName(data.getProjectName());

        return excelDTO;
    }

    public static WaveDetailExportExcelDTO transFrom(AdsMckForecastSummaryDfDO data) {
        WaveDetailExportExcelDTO excelDTO = new WaveDetailExportExcelDTO();
//        excelDTO.setPplId(data.getPplId());
        excelDTO.setDemandType(PplDemandTypeEnum.getNameByCode(data.getDemandType()));
        excelDTO.setProduct(data.getProduct());
        excelDTO.setIndustryDept(data.getIndustryDept());
//        excelDTO.setWarZone(data.getWarZone());
        excelDTO.setCustomerShortName(data.getCustomerShortName());
        excelDTO.setCustomerUin(data.getCustomerUin());
        excelDTO.setZoneName(data.getZoneName());
        excelDTO.setRegionName(data.getRegionName());
        excelDTO.setInstanceType(data.getInstanceType());
        excelDTO.setInstanceModel(data.getInstanceModel());

        excelDTO.setGpuType(StdUtils.EMPTY_STR.equals(data.getGpuType()) ? "" : data.getGpuType());

        excelDTO.setBeginYearMonth(
                String.format("%d年%d月", data.getBeginBuyYear(), data.getBeginBuyDate().getMonthValue()));
        excelDTO.setBeginBuyDate(data.getBeginBuyDate().toString());
        excelDTO.setEndBuyDate(data.getEndBuyDate().toString());
        excelDTO.setYunxiaoOrderId(data.getOrderNumber());
        excelDTO.setProjectName(data.getProjectName());

        return excelDTO;
    }

    public static WaveDetailExportExcelDTO transFrom(DwsCrpPplItemVersionBaseCfDo data) {
        WaveDetailExportExcelDTO excelDTO = new WaveDetailExportExcelDTO();
        excelDTO.setVersionCode(data.getVersionCode());

        excelDTO.setPplId(data.getPplId());
        excelDTO.setDemandType(PplDemandTypeEnum.getNameByCode(data.getDemandType()));
        excelDTO.setProduct(data.getProduct());
        excelDTO.setIndustryDept(data.getIndustryDept());
        excelDTO.setWarZone(data.getWarZone());
        excelDTO.setCustomerShortName(data.getCustomerShortName());
        excelDTO.setCustomerUin(data.getCustomerUin());
        excelDTO.setZoneName(data.getZoneName());
        excelDTO.setRegionName(data.getRegionName());
        excelDTO.setInstanceType(data.getInstanceType());
        excelDTO.setInstanceModel(data.getInstanceModel());

        excelDTO.setGpuType(StdUtils.EMPTY_STR.equals(data.getGpuType()) ? "" : data.getGpuType());

        excelDTO.setBeginYearMonth(
                String.format("%d年%d月", data.getYear(), data.getMonth()));
        excelDTO.setBeginBuyDate(data.getBeginBuyDate().toString());
        excelDTO.setEndBuyDate(data.getEndBuyDate().toString());
        excelDTO.setProjectName(data.getProjectName());

        return excelDTO;
    }

    public static WaveDetailExportExcelDTO transFrom(DwsCrpPplItemVersionNewestCfDO data) {
        WaveDetailExportExcelDTO excelDTO = new WaveDetailExportExcelDTO();
        excelDTO.setVersionCode(data.getVersionCode());

        excelDTO.setPplId(data.getPplId());
        excelDTO.setDemandType(PplDemandTypeEnum.getNameByCode(data.getDemandType()));
        excelDTO.setProduct(data.getProduct());
        excelDTO.setIndustryDept(data.getIndustryDept());
        excelDTO.setWarZone(data.getWarZone());
        excelDTO.setCustomerShortName(data.getCustomerShortName());
        excelDTO.setCustomerUin(data.getCustomerUin());
        excelDTO.setZoneName(data.getZoneName());
        excelDTO.setRegionName(data.getRegionName());
        excelDTO.setInstanceType(data.getInstanceType());
        excelDTO.setInstanceModel(data.getInstanceModel());

        excelDTO.setGpuType(StdUtils.EMPTY_STR.equals(data.getGpuType()) ? "" : data.getGpuType());

        excelDTO.setBeginYearMonth(
                String.format("%d年%d月", data.getYear(), data.getMonth()));
        excelDTO.setBeginBuyDate(data.getBeginBuyDate().toString());
        excelDTO.setEndBuyDate(data.getEndBuyDate().toString());
        excelDTO.setProjectName(data.getProjectName());

        return excelDTO;
    }

    public static WaveDetailExportExcelDTO transFrom(DwsCrpPplJoinOrderVersionNewestCfDO data) {
        WaveDetailExportExcelDTO excelDTO = new WaveDetailExportExcelDTO();
        excelDTO.setVersionCode(data.getVersionCode());

        excelDTO.setPplId(data.getPplId());
        excelDTO.setDemandType(PplDemandTypeEnum.getNameByCode(data.getDemandType()));
        excelDTO.setProduct(data.getProduct());
        excelDTO.setIndustryDept(data.getIndustryDept());
        excelDTO.setWarZone(data.getWarZone());
        excelDTO.setCustomerShortName(data.getCustomerShortName());
        excelDTO.setCustomerUin(data.getCustomerUin());
        excelDTO.setZoneName(data.getZoneName());
        excelDTO.setRegionName(data.getRegionName());
        excelDTO.setInstanceType(data.getInstanceType());
        excelDTO.setInstanceModel(data.getInstanceModel());

        excelDTO.setGpuType(StdUtils.EMPTY_STR.equals(data.getGpuType()) ? "" : data.getGpuType());

        excelDTO.setBeginYearMonth(
                String.format("%d年%d月", data.getYear(), data.getMonth()));
        excelDTO.setBeginBuyDate(data.getBeginBuyDate().toString());
        excelDTO.setEndBuyDate(data.getEndBuyDate().toString());
        excelDTO.setCommonInstanceType(data.getCommonInstanceType());
        excelDTO.setYunxiaoOrderId(data.getOrderNumber());
        excelDTO.setProjectName(data.getProjectName());

        return excelDTO;
    }

    public static WaveDetailExportExcelDTO transFrom(PplVersionGroupRecordItemWithOrderSumVO data) {
        WaveDetailExportExcelDTO excelDTO = new WaveDetailExportExcelDTO();
        excelDTO.setVersionCode(data.getVersionCode());

        excelDTO.setPplId(data.getPplId());
        excelDTO.setDemandType(PplDemandTypeEnum.getNameByCode(data.getDemandType()));
        excelDTO.setProduct(data.getProduct());
        excelDTO.setIndustryDept(data.getIndustryDept());
        excelDTO.setWarZone(data.getWarZone());
        excelDTO.setCustomerShortName(data.getCustomerShortName());
        excelDTO.setCustomerUin(data.getCustomerUin());
        excelDTO.setZoneName(data.getZoneName());
        excelDTO.setRegionName(data.getRegionName());
        excelDTO.setInstanceType(data.getInstanceType());
        excelDTO.setInstanceModel(data.getInstanceModel());

        excelDTO.setGpuType(StdUtils.EMPTY_STR.equals(data.getGpuType()) ? "" : data.getGpuType());

        excelDTO.setBeginYearMonth(
                String.format("%d年%d月", data.getYear(), data.getMonth()));
        excelDTO.setBeginBuyDate(data.getBeginBuyDate().toString());
        excelDTO.setEndBuyDate(data.getEndBuyDate().toString());

        return excelDTO;
    }

    public static WaveDetailExportExcelDTO transFrom(DwsCrpPplItemVersion532NewMifDO data) {
        WaveDetailExportExcelDTO excelDTO = new WaveDetailExportExcelDTO();
        excelDTO.setVersionCode(data.getVersionCode());

        excelDTO.setPplId(data.getPplId());
        excelDTO.setDemandType(PplDemandTypeEnum.getNameByCode(data.getDemandType()));
        excelDTO.setProduct(data.getProduct());
        excelDTO.setIndustryDept(data.getIndustryDept());
        excelDTO.setWarZone(data.getWarZone());
        excelDTO.setCustomerShortName(data.getCustomerShortName());
        excelDTO.setCustomerUin(data.getCustomerUin());
        excelDTO.setZoneName(data.getZoneName());
        excelDTO.setRegionName(data.getRegionName());
        excelDTO.setInstanceType(data.getInstanceType());
        excelDTO.setInstanceModel(data.getInstanceModel());

        excelDTO.setGpuType(StdUtils.EMPTY_STR.equals(data.getGpuType()) ? "" : data.getGpuType());

        excelDTO.setBeginYearMonth(
                String.format("%d年%d月", data.getYear(), data.getMonth()));
        excelDTO.setBeginBuyDate(data.getBeginBuyDate().toString());
        excelDTO.setEndBuyDate(data.getEndBuyDate().toString());
        excelDTO.setProjectName(data.getProjectName());

        return excelDTO;
    }

}
