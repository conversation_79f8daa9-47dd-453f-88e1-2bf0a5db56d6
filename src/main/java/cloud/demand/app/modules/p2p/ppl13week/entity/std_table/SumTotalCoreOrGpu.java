package cloud.demand.app.modules.p2p.ppl13week.entity.std_table;

import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplWaveDiskTypeEnum;
import java.math.BigDecimal;

public interface SumTotalCoreOrGpu {

    BigDecimal totalCoreGet();

    BigDecimal totalGpuNumGet();

    BigDecimal totalSystemDiskCapacityGet();

    BigDecimal totalDataDiskCapacityGet();

    PplDemandTypeEnum demandTypeEnumGet();

    Ppl13weekProductTypeEnum productEnumGet();

    /**
     *   按照净增计算：如果{@link #demandTypeEnumGet()} 是退回需求{@link PplDemandTypeEnum#RETURN}，则返回 0 - 总核数(或总卡数) <br/>
     *   不按净增计算：直接返回 总核数(或总卡数) <br/>
     * @param calcNetGrowth 是否按净增计算，true表示按净增计算
     */
    default BigDecimal calc(boolean calcNetGrowth) {
        boolean isReturn = PplDemandTypeEnum.RETURN.equals(demandTypeEnumGet());
        if (Ppl13weekProductTypeEnum.GPU.equals(productEnumGet())) {
            BigDecimal totalGpuNum = totalGpuNumGet();
            if (totalGpuNum == null) {
                return BigDecimal.ZERO;
            }
            return calcNetGrowth && isReturn ? BigDecimal.ZERO.subtract(totalGpuNum) : totalGpuNum;
        } else {
            BigDecimal totalCore = totalCoreGet();
            if (totalCore == null) {
                return BigDecimal.ZERO;
            }
            return calcNetGrowth && isReturn ? BigDecimal.ZERO.subtract(totalCore) : totalCore;
        }
    }

    default BigDecimal calcDiskCapacity(boolean calcNetGrowth, String diskType) {
        boolean isReturn = PplDemandTypeEnum.RETURN.equals(demandTypeEnumGet());
        if (PplWaveDiskTypeEnum.DATA_DISK.getName().equals(diskType)) {
            BigDecimal dataCapacity = totalDataDiskCapacityGet();
            if (dataCapacity == null) {
                return BigDecimal.ZERO;
            }
            return calcNetGrowth && isReturn ? BigDecimal.ZERO.subtract(dataCapacity) : dataCapacity;
        }else {
            BigDecimal systemCapacity = totalSystemDiskCapacityGet();
            if (systemCapacity == null) {
                return BigDecimal.ZERO;
            }
            return calcNetGrowth && isReturn ? BigDecimal.ZERO.subtract(systemCapacity) : systemCapacity;
        }
    }

}
