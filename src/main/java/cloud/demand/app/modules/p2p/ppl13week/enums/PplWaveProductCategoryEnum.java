package cloud.demand.app.modules.p2p.ppl13week.enums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import lombok.Getter;

@Getter
public enum PplWaveProductCategoryEnum {

    CVM("CVM", 0,Arrays.asList("CVM&CBS", "弹性MapReduce", "Elasticsearch Service", "云数据仓库", "EKS官网", "数据湖DLC", "CSIG容器平台")),
    GPU("GPU", 1,Arrays.asList("GPU(裸金属&CVM)")),
    BM("裸金属", 2,Arrays.asList("裸金属")),
    CBS("CBS", 3,Arrays.asList("CVM&CBS", "弹性MapReduce", "Elasticsearch Service", "云数据仓库", "EKS官网", "数据湖DLC", "CSIG容器平台", "GPU(裸金属&CVM)", "裸金属")),
    DATABASE("数据库", 4,Arrays.asList("数据库")),
    EKS("EKS", 5,Arrays.asList("EKS官网", "CSIG容器平台")),
    BIGDATA("大数据", 6,Arrays.asList("弹性MapReduce", "Elasticsearch Service", "云数据仓库", "数据湖DLC"));


    private final String code;
    private final Integer sortNum;
    private final List<String> subProducts;

    PplWaveProductCategoryEnum(String code, int sortNum, List<String> subProducts) {
        this.code = code;
        this.sortNum = sortNum;
        this.subProducts = subProducts;
    }

    public static List<String> getSubProductByCode(String code) {
        for (PplWaveProductCategoryEnum value : PplWaveProductCategoryEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getSubProducts();
            }
        }
        return new ArrayList<>();
    }

    public static Map<String, List<String>> getProductCategoryExpectDataBase() {
        Map<String, List<String>> result = new TreeMap<>(
                Comparator.comparing(key -> {
                    for (PplWaveProductCategoryEnum value : PplWaveProductCategoryEnum.values()) {
                        if (value.getCode().equals(key)) {
                            return value.getSortNum();
                        }
                    }
                    return Integer.MAX_VALUE; // 未找到的key排在最后
                })
        );
        for (PplWaveProductCategoryEnum value : PplWaveProductCategoryEnum.values()) {
            result.put(value.getCode(), value.getSubProducts());
        }

        return result;
    }
}
