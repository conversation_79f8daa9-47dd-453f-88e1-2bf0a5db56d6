package cloud.demand.app.modules.p2p.ppl13week_forecast.service;

import cloud.demand.app.modules.industry_report.entity.IndustryReportAppidInfoLatestWithoutJsonDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastConfigSpikeThresholdDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictTaskDO;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface Ppl13weekCommonDataAccess {

    /**
     * 查询行的采购机型
     * @return map
     */
    Map<String, String> getPurchaseNewInstanceTypeMap();

    /**
     * 查询 2024 的 31-35个头部客户名单，打平形式
     * @return list
     */
    Set<String> getHeadCustomerShortName();

    /**
     * 查询CBS的所有大客户名单
     */
    Set<String> getAllHeadCustomerShortNameForCBS();

    /**
     * 查询 2024 的 31-35个头部客户名单，分组形式
     */
    Map<String, Set<String>> getHeadCustomerShortNameGroup();

    /**
     *  获取所有的实力规格黑名单
     * @return 实例规格黑名单 list
     */
    List<String> getAllBlackInstanceType();

    /**
     * 为了加个缓存， 配置一般不错，全量获取
     * @return all list
     */
    List<PplForecastConfigSpikeThresholdDO> getAllSpikeThreshold();

    /**
     * 腰部模型预测的黑名单，它是来自于ppl_forecast_config_spike_threshold中threshold的值<0的机型
     */
    List<String> getAllBlackInstanceTypeForMiddleForecast();

    /**
     * getPplForecastPredictTaskDO
     * @param taskIds taskIds
     * @param throwException null
     * @return list
     */
    List<PplForecastPredictTaskDO>  getPplForecastPredictTaskDO(List<Long> taskIds, boolean throwException);

    /**
     * 生成ppl_forecast_config_customer_defines_year_month_version表的数据，每个月一份，头部客户名单
     * @param date 同步的日期
     */
    void createYearMonthCustomerDefines(LocalDate date);

    /**
     * 生成ppl_forecast_config_customer_defines_year_month_version表的数据，每个月一份，报备客户名单
     */
    void createVersionItemYearMonthCustomerDefines(LocalDate date);

    /**
     *
     * @param orderUin uin
     * @return list
     */
    List<IndustryReportAppidInfoLatestWithoutJsonDO> getAppInfoByUinCustomerShortName(List<String> orderUin);

    /**
     *
     */
    void createAllCustomerDefines();

    /**
     * 实例类型到机型族
     * @return map
     */
    Map<String, String> getZiyanGinsfamily2DeviceGroup();
}
