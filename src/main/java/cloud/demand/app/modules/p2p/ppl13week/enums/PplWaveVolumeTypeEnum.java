package cloud.demand.app.modules.p2p.ppl13week.enums;

import java.util.ArrayList;
import java.util.List;
import lombok.Getter;

@Getter
public enum PplWaveVolumeTypeEnum {

    SSD("SSD", "SSD"),

    PREMIUM("PREMIUM", "高性能");


    final private String code;

    final private String name;

    PplWaveVolumeTypeEnum (String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PplWaveVolumeTypeEnum getByCode(String code) {
        for (PplWaveVolumeTypeEnum value : PplWaveVolumeTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static List<String> getAllName() {
        List<String> result = new ArrayList<>();
        for (PplWaveVolumeTypeEnum value : PplWaveVolumeTypeEnum.values()) {
            result.add(value.getName());
        }
        return result;
    }

}
