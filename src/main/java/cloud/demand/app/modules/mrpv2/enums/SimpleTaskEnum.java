package cloud.demand.app.modules.mrpv2.enums;

import cloud.demand.app.modules.sop_device.sopTask.frame.enums.ITaskEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;


/** 简单任务 */
@Getter
@AllArgsConstructor
public enum SimpleTaskEnum implements ITaskEnum {
    MRP_V2_RATE_TOTAL("MRP_V2_RATE_TOTAL","行业数据看板总准确率存表"),

    MRP_V2_RATE_CUSTOMER("MRP_V2_RATE_CUSTOMER","行业数据看板客户级别准确率存表"),

    INV_HEALTH_CUSTOMER_WAVE("INV_HEALTH_CUSTOMER_WAVE","安全库存客户波动"),

    DWD_TXY_SCALE_DF_VIEW_FLUSH("DWD_TXY_SCALE_DF_VIEW_FLUSH","日规模表物化视图刷新"),

    DWD_TXY_SCALE_LONG_TAIL_DF_VIEW_FLUSH("DWD_TXY_SCALE_LONG_TAIL_DF_VIEW_FLUSH","日规模中长尾表物化视图刷新"),

    // =============== 分货 ================

    ADS_SOE_ITEM_DISTRIBUTE("ADS_SOE_ITEM_DISTRIBUTE","分货看板ADS-明细"),

    DWS_SOE_DISTRIBUTE("DWS_SOE_DISTRIBUTE","分货看板DWS"),

    DWD_SOE_DISTRIBUTE_ORDER("DWD_SOE_DISTRIBUTE_ORDER","分货看板DWD-预约单"),

    DWD_SOE_DISTRIBUTE_FORECAST("DWD_SOE_DISTRIBUTE_FORECAST","分货看板DWD-预测基准"),

    DWD_SOE_DISTRIBUTE_SUPPLY("DWD_SOE_DISTRIBUTE_SUPPLY","分货看板DWD-供应"),

    // =============== 预测内外 ================

    DWD_FIO_FORECAST_BASE("DWD_FIO_FORECAST_BASE","预测内外DWD-预测基准"),

    DWD_FIO_FORECAST_ORDER("DWD_FIO_FORECAST_ORDER","预测内外DWD-预约单"),

    DWS_FIO_FORECAST("DWS_FIO_FORECAST","预测内外DWS-预测内外"),

    // =============== SOP 全年需求 ================

    SOP_ALL_YEAR_DEMAND("SOP_ALL_YEAR_DEMAND","SOP全年需求"),

    // =============== SOP 需求评审 ================

    SOP_DEMAND_REVIEW("SOP_DEMAND_REVIEW","SOP需求评审"),

    // =============== CVM/CBS 联动 ================
    CVM_CBS_DWS("CVM_CBS_DWS", "CVM/CBS联动，同步数据到DWS层"),

    CVM_CBS_ADS("CVM_CBS_ADS", "CVM/CBS联动，同步DWS数据到ADS层"),

    CVM_ZI_YAN_CBS_DWS("CVM_ZI_YAN_CBS_DWS", "CVM/CBS联动，同步自研CBS数据到DWS层"),

    CVM_CBS_FORECAST_RATE("CVM_CBS_FORECAST_RATE", "CVM/CBS联动，计算预测准确率"),

    // =============== IndustryCockpit V3 ================
    INDUSTRY_COCKPIT_V3_WITHHOLD_DWS("INDUSTRY_COCKPIT_V3_WITHHOLD_DWS", "IndustryCockpit V3 ，同步预扣数据到DWS层"),

    DWS_LOGICAL_TAGS_WITHHOLD("DWS_LOGICAL_TAGS_WITHHOLD", "IndustryCockpit V3 ，同步逻辑区、标签预扣预扣数据到DWS层"),

    PPL_BILLING_SCALE_MONTHLY_VIEW("PPL_BILLING_SCALE_MONTHLY_VIEW", "IndustryCockpit V3 ，同步月均数据"),

    SUPPLY_DELIVERY_DWS("SUPPLY_DELIVERY_DWS", "IndustryCockpit V4 ，同步供应交付数据到DWS层"),

    // =============== 报表代理任务 ================
    REPORT_PROXY_REQ("REPORT_PROXY_REQ", "报表代理请求"),

    ;

    private final String name;
    private final String desc;

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
