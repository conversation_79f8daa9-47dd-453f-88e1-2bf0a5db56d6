package cloud.demand.app.modules.industry_cockpit.v3.service.impl;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.industry_cockpit.util.GroupUtils;
import cloud.demand.app.modules.industry_cockpit.v3.entity.DwdTxyScaleDfDO;
import cloud.demand.app.modules.industry_cockpit.v3.entity.DwsIndustryCockpitV3WithholdDfDO;
import cloud.demand.app.modules.industry_cockpit.v3.entity.DwsLogicalTagsWithholdDfDO;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.LineChartReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.LogicalTagsWithholdDetailTableReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.LogicalTagsWithholdLineChartReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.LogicalTagsWithholdReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.NormalWithholdDetailTableReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.NormalWithholdLineChartReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.NormalWithholdReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.SummaryWithholdDetailTableReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.SummaryWithholdLineChartReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.LogicalTagsWithholdDetailResp;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.NormalWithholdDetailResp;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.OverviewWithholdChartResp;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.SummaryWithholdDetailResp;
import cloud.demand.app.modules.industry_cockpit.v3.model.vo.KeyValueItemVO;
import cloud.demand.app.modules.industry_cockpit.v3.model.vo.LogicalTagsWithholdDetailItemVO;
import cloud.demand.app.modules.industry_cockpit.v3.model.vo.NormalWithholdDetailItemVO;
import cloud.demand.app.modules.industry_cockpit.v3.model.vo.OverviewChartVO;
import cloud.demand.app.modules.industry_cockpit.v3.model.vo.SummaryWithholdDetailItemVO;
import cloud.demand.app.modules.industry_cockpit.v3.service.FieldFillService;
import cloud.demand.app.modules.industry_cockpit.v3.service.WithholdService;
import cloud.demand.app.modules.sop_return.frame.where.SopWhereBuilder;
import cloud.demand.app.modules.sop_util.process.utils.sql.SimpleSqlBuilder;
import cloud.demand.app.web.model.common.StreamDownloadBean;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import yunti.boot.exception.ITException;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/5/23 20:37
 */
@Service
@Slf4j
public class WithholdServiceImpl implements WithholdService {

    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Resource
    private FieldFillService fieldFillService;

    @Override
    public OverviewWithholdChartResp getOverviewNormalWithholdChartData(LineChartReq req) {

        SopWhereBuilder sopWhereBuilder = new SopWhereBuilder(req, DwdTxyScaleDfDO.class);
        ORMUtils.WhereContent whereContent = sopWhereBuilder.where();

        String sql = ORMUtils.getSql("/sql/industry_cockpit/v3/9_withhold_by_stat_time.sql");

        sql = SimpleSqlBuilder.doReplace(sql, "where", whereContent.getSql());
        sql = SimpleSqlBuilder.doReplace(sql, "unit", req.getUnit());
        List<KeyValueItemVO> chartItems = ckcldStdCrpDBHelper.getRaw(KeyValueItemVO.class, sql, whereContent.getParams());

        OverviewChartVO vo = OverviewChartVO.builder(chartItems, req.getStartStatTime(), req.getEndStatTime());
        return OverviewWithholdChartResp.transform(vo);
    }

    @Override
    public NormalWithholdDetailResp getNormalWithholdDetailTable(NormalWithholdDetailTableReq req) {
        List<NormalWithholdDetailItemVO> ret = getNormalWithholdDetailItemVO(req, req.getDims());
        return NormalWithholdDetailResp.builder(ret, req);
    }

    @Override
    public ResponseEntity<InputStreamResource> exportNormalWithholdDetailTable(NormalWithholdDetailTableReq req) {
        List<NormalWithholdDetailItemVO> ret = getNormalWithholdDetailItemVO(req, req.getDims());
        ByteArrayInputStream in;
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            ExcelWriter writer = EasyExcel.write(out).excelType(ExcelTypeEnum.XLSX).build();
            WriteSheet tempSheet = EasyExcel
                    .writerSheet("驾驶舱-历史规模特征-常规预扣明细")
                    .head(NormalWithholdDetailItemVO.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
            writer.write(ret, tempSheet);

            writer.finish();
            in = new ByteArrayInputStream(out.toByteArray());
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ITException("导出excel模版失败");
        }

        String filename = "驾驶舱-历史规模特征-预扣明细-" + DateUtils.format(new Date()) + UUID.randomUUID().toString().substring(0, 8) + ".xlsx";
        return new StreamDownloadBean(filename, in);
    }

    @Override
    public NormalWithholdDetailResp getNormalWithholdLineChart(NormalWithholdLineChartReq req) {
        List<NormalWithholdDetailItemVO> ret = getNormalWithholdDetailItemVO(req, req.getDims());

        return NormalWithholdDetailResp.builder(ret, req);
    }

    @Override
    public LogicalTagsWithholdDetailResp getLogicalTagsWithholdDetailTable(LogicalTagsWithholdDetailTableReq req) {
        List<LogicalTagsWithholdDetailItemVO> ret = getLogicalTagsWithholdDetailItemVO(req, req.getDims());
        return LogicalTagsWithholdDetailResp.builder(ret, req);
    }

    @Override
    public ResponseEntity<InputStreamResource> exportLogicalTagsWithholdDetailTable(LogicalTagsWithholdDetailTableReq req) {
        List<String> dims = ListUtils.newArrayList("withholdType","industryDept","warZone","customerShortName","appId","uin","instanceType","hostIp",
                "regionName","zoneName","zoneId","cvmStandardType","bsiId","stackLabel","logicalInfo","tagInfo","gpuCardType");
        List<LogicalTagsWithholdDetailItemVO> ret = getLogicalTagsWithholdDetailItemVO(req, dims);
        ByteArrayInputStream in;
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            ExcelWriter writer = EasyExcel.write(out).excelType(ExcelTypeEnum.XLSX).build();
            WriteSheet tempSheet = EasyExcel
                    .writerSheet("驾驶舱-历史规模特征-逻辑区&标签预扣明细")
                    .head(LogicalTagsWithholdDetailItemVO.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
            writer.write(ret, tempSheet);

            writer.finish();
            in = new ByteArrayInputStream(out.toByteArray());
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ITException("导出excel模版失败");
        }

        String filename = "驾驶舱-历史规模特征-逻辑区&标签预扣明细-" + DateUtils.format(new Date()) + UUID.randomUUID().toString().substring(0, 8) + ".xlsx";
        return new StreamDownloadBean(filename, in);
    }

    @Override
    public LogicalTagsWithholdDetailResp getLogicalTagsWithholdLineChart(LogicalTagsWithholdLineChartReq req) {
        List<LogicalTagsWithholdDetailItemVO> ret = getLogicalTagsWithholdDetailItemVO(req, req.getDims());

        return LogicalTagsWithholdDetailResp.builder(ret, req);
    }

    @Override
    public SummaryWithholdDetailResp getSummaryWithholdDetailTable(SummaryWithholdDetailTableReq req) {
        List<SummaryWithholdDetailItemVO> ret = getSummaryWithholdDetailItemVO(req,req.getDims());
        return SummaryWithholdDetailResp.builder(ret, req);
    }

    @Override
    public ResponseEntity<InputStreamResource> exportSummaryWithholdDetailTable(SummaryWithholdDetailTableReq req) {
        List<String> dims = ListUtils.newArrayList("industryDept","warZone","customerShortName","appId","uin","instanceType","gpuCardType",
                "regionName","zoneName");
        List<SummaryWithholdDetailItemVO> ret = getSummaryWithholdDetailItemVO(req,dims);
        ByteArrayInputStream in;
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            ExcelWriter writer = EasyExcel.write(out).excelType(ExcelTypeEnum.XLSX).build();
            WriteSheet tempSheet = EasyExcel
                    .writerSheet("驾驶舱-历史规模特征-预扣总览")
                    .head(SummaryWithholdDetailItemVO.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
            writer.write(ret, tempSheet);

            writer.finish();
            in = new ByteArrayInputStream(out.toByteArray());
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ITException("导出excel模版失败");
        }

        String filename = "驾驶舱-历史规模特征-预扣总览-" + DateUtils.format(new Date()) + UUID.randomUUID().toString().substring(0, 8) + ".xlsx";
        return new StreamDownloadBean(filename, in);
    }

    @Override
    public SummaryWithholdDetailResp getSummaryWithholdLineChart(SummaryWithholdLineChartReq req) {
        List<SummaryWithholdDetailItemVO> ret = getSummaryWithholdDetailItemVO(req,req.getDims());
        return SummaryWithholdDetailResp.builder(ret, req);
    }

    @NotNull
    private List<NormalWithholdDetailItemVO> getNormalWithholdDetailItemVO(NormalWithholdReq req, List<String> dims) {
        SopWhereBuilder sopWhereBuilder = new SopWhereBuilder(req, DwsIndustryCockpitV3WithholdDfDO.class);
        ORMUtils.WhereContent whereContent = sopWhereBuilder.where();

        String sql = ORMUtils.getSql("/sql/industry_cockpit/v3/10_normal_withhold_detail.sql");
        sql = SimpleSqlBuilder.doReplace(sql, "unit", req.getUnit());
        sql = SimpleSqlBuilder.doReplace(sql, "where", whereContent.getSql());

        List<String> fieldNames = Arrays.stream(DwsIndustryCockpitV3WithholdDfDO.class.getDeclaredFields()).map(item -> item.getName()).collect(Collectors.toList());

        sql = fieldFillService.fillInstanceFamily(req, dims, sql, fieldNames);

        sql = SimpleSqlBuilder.buildDims(sql, new HashSet<>(fieldNames), dims);

        return ckcldStdCrpDBHelper.getRaw(NormalWithholdDetailItemVO.class, sql, whereContent.getParams());
    }

    @NotNull
    private List<LogicalTagsWithholdDetailItemVO> getLogicalTagsWithholdDetailItemVO(LogicalTagsWithholdReq req, List<String> dims) {
        SopWhereBuilder sopWhereBuilder = new SopWhereBuilder(req, DwsLogicalTagsWithholdDfDO.class);
        ORMUtils.WhereContent whereContent = sopWhereBuilder.where();

        String sql = ORMUtils.getSql("/sql/industry_cockpit/v3/10_logical_tags_withhold_detail.sql");
        sql = SimpleSqlBuilder.doReplace(sql, "where", whereContent.getSql());

        List<String> fieldNames = Arrays.stream(DwsLogicalTagsWithholdDfDO.class.getDeclaredFields()).map(item -> item.getName()).collect(Collectors.toList());
        sql = SimpleSqlBuilder.buildDims(sql, new HashSet<>(fieldNames), dims);

        return ckcldStdCrpDBHelper.getRaw(LogicalTagsWithholdDetailItemVO.class, sql, whereContent.getParams());
    }

    @NotNull
    private List<SummaryWithholdDetailItemVO> getSummaryWithholdDetailItemVO(LogicalTagsWithholdReq req, List<String> dims) {
        List<SummaryWithholdDetailItemVO> dataList = ListUtils.newArrayList();
        List<LogicalTagsWithholdDetailItemVO> logicalTagsWithholdList = getLogicalTagsWithholdDetailItemVO(req, dims);
        dataList.addAll(ListUtils.transform(logicalTagsWithholdList, SummaryWithholdDetailItemVO::transform));

        List<NormalWithholdDetailItemVO> normalWithholdList = getNormalWithholdDetailItemVO(NormalWithholdDetailTableReq.transform(req), dims);
        dataList.addAll(ListUtils.transform(normalWithholdList, SummaryWithholdDetailItemVO::transform));

        List<String> sumFields = ListUtils.newArrayList("normalWithholdNum", "normalWithholdNumLe14", "normalWithholdNumGt14", "elasticityWithholdNum",
                "totalNormalWithholdNum", "logicalWithholdNum", "tagsWithholdNum", "totalWithholdNum");

        List<String> groupDims = ListUtils.newArrayList();
        groupDims.add("statTime");
        if(ListUtils.isNotEmpty(dims)){
            groupDims.addAll(dims);
        }
        List<SummaryWithholdDetailItemVO> retList = ListUtils.newArrayList();
        ListUtils.groupBy(dataList, item -> GroupUtils.getDimsGroupKey(item, groupDims))
                .forEach((k, vList) -> {
                    SummaryWithholdDetailItemVO temp = GroupUtils.mergeList(vList, groupDims, sumFields);
                    retList.add(temp);
                });

        return retList;
    }
}
