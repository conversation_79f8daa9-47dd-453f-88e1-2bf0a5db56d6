package cloud.demand.app.modules.industry_cockpit.v3.parse;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.industry_cockpit.v3.enums.ProductEnum;
import cloud.demand.app.modules.sop_return.frame.where.IWhereParser;
import cloud.demand.app.modules.sop_return.frame.where.SopWhereBuilder;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/8/14 11:32
 */
public class ProductForLogicalTagsWithholdParse implements IWhereParser {
    @Override
    public void parse(ORMUtils.WhereContent content, SopWhereBuilder.SopWhere sopWhere, Object t) {
        String product = (String) sopWhere.getV();

        String cpuOrGpu = ProductEnum.getByName(product).getGpuOrCpu();
        String bizType = StringUtils.equals(cpuOrGpu, "GPU") ? "GPU" : "CVM";
        content.addAnd(" biz_type = ? ", bizType);
    }
}
