package cloud.demand.app.modules.industry_cockpit.v3.service;

import cloud.demand.app.modules.industry_cockpit.v3.model.req.*;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.*;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @since 2024/8/14 11:17
 */
public interface IndustryCockpitV3ReportService {

    /**
     * 获取总览大盘图表数据
     * @param req OverviewChartReq
     * @return OverviewChartResp
     */
    OverviewBillServiceChartResp getOverviewChartData(LineChartReq req);

    /**
     * 获取总览环形图数据
     * @param req OverviewChartReq
     * @return OverviewCircularGraphResp
     */
    OverviewCircularGraphResp getOverviewCircularGraphData(ProfileReq req);


    /**
     * 获取历史月度表格数据
     * @param req HistoricalScaleMonthlyReq
     * @param caliber 月切变化量、月均变化量、月末切片规模
     * @return HistoricalScaleMonthlyTableResp
     */
    HistoricalScaleMonthlyTableResp getHistoricalScaleMonthlyTable(HistoricalScaleMonthlyReq req, String caliber);

    /**
     * 获取历史月度趋势柱状图数据
     * @param req HistoricalScaleMonthlyReq
     * @param caliber 月切变化量、月均变化量、月末切片规模
     * @return HistoricalScaleMonthlyHistogramResp
     */
    HistoricalScaleMonthlyHistogramResp getHistoricalScaleMonthlyHistogram(HistoricalScaleMonthlyReq req, String caliber);


    /**
     * 获取历史月度趋势柱状图数据（for 规格）
     * @param req HistoricalScaleMonthlyReq
     * @param caliber 月切变化量、月末切片规模
     * @return HistoricalScaleMonthlyHistogramResp
     */
    HistoricalScaleMonthlyHistogramResp getHistoricalScaleMonthlyHistogramForSpecifications(HistoricalScaleMonthlyReq req, String caliber);

    /**
     * 获取行业大盘历史规模日变化表数据
     *
     * @param req LineChartReq
     * @return HistoricalScaleDailyChartResp
     */
    HistoricalScaleDailyChartResp getHistoricalScaleDailyChart(@JsonrpcParam @Valid LineChartReq req);

    /**
     * 获取行业大盘历史规模日变化柱状图数据
     * @param req ProfileReq
     * @return HistoricalScaleDailyHistogramResp
     */
    HistoricalScaleDailyHistogramResp getHistoricalScaleDailyProfileData(ProfileReq req);

    FutureScaleTableResp getFutureScaleTableData(IndustryCockpitV3FutureReq request);

    ResponseEntity<InputStreamResource> exportFutureScaleTableData(IndustryCockpitV3FutureReq req,boolean isShortTerm);

    /**
     * 中长期预测
     * */
    FutureScaleTableResp getLongTermFutureScaleTableData(IndustryCockpitV3FutureReq request);

    ResponseEntity<InputStreamResource> exportLongTermFutureScaleTableData(IndustryCockpitV3FutureReq req);
}
