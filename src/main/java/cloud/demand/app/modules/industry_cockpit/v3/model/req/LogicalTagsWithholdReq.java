package cloud.demand.app.modules.industry_cockpit.v3.model.req;

import cloud.demand.app.modules.industry_cockpit.auth.AuthCheckParam;
import cloud.demand.app.modules.industry_cockpit.v3.parse.ProductForLogicalTagsWithholdParse;
import cloud.demand.app.modules.sop_return.frame.where.anno.SopReportWhere;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/26 16:45
 */
@Data
public class LogicalTagsWithholdReq extends IndustryCockpitV3HistoricalReq {

    @NotNull(message = "产品不能为空")
    @SopReportWhere(parsers = {ProductForLogicalTagsWithholdParse.class})
    @AuthCheckParam
    private String product;

    @SopReportWhere
    @AuthCheckParam(authField = "industry")
    private List<String> industryDept; // 行业部门 支持多选

    private List<String> appRole;// 应用角色 支持多选
}
