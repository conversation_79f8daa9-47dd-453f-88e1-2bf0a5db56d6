package cloud.demand.app.modules.industry_cockpit.v3.model.resp;

import cloud.demand.app.modules.industry_cockpit.v3.enums.WithholdDurationEnum;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.NormalWithholdDetailTableReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.NormalWithholdLineChartReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.vo.NormalWithholdDetailItemVO;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/8/26 16:52
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NormalWithholdDetailResp {

    private List<NormalWithholdDetailItemVO> detailItemList;
    private int count;

    public static NormalWithholdDetailResp builder(List<NormalWithholdDetailItemVO> list, NormalWithholdDetailTableReq req) {
        List<NormalWithholdDetailItemVO> itemList = ListUtils.newList();
        if (ListUtils.isEmpty(list)) {
            return new NormalWithholdDetailResp(itemList, itemList.size());
        }
        // 设置枚举值
        list.forEach(item -> {
            if(StringUtils.isNotBlank(item.getWithholdDuration())){
                WithholdDurationEnum durationEnum = WithholdDurationEnum.getByCode(item.getWithholdDuration());
                item.setWithholdDurationEnum(durationEnum);
            }
        });

        if(Objects.isNull(req.getTopN()) || req.getTopN() > list.size()){
            return new NormalWithholdDetailResp(list, list.size());
        }
        // 排序
        list.sort(NormalWithholdDetailItemVO::compareTo);
        for (int i = 0; i < req.getTopN(); i++) {
            itemList.add(list.get(i));
        }
        NormalWithholdDetailItemVO otherItem = new NormalWithholdDetailItemVO();
        int otherNormalWithholdNum = list.stream().mapToInt(NormalWithholdDetailItemVO::getNormalWithholdNum).sum() - itemList.stream().mapToInt(NormalWithholdDetailItemVO::getNormalWithholdNum).sum();
        int otherElasticityWithholdNum = list.stream().mapToInt(NormalWithholdDetailItemVO::getElasticityWithholdNum).sum() - itemList.stream().mapToInt(NormalWithholdDetailItemVO::getElasticityWithholdNum).sum();
        otherItem.setNormalWithholdNum(otherNormalWithholdNum);
        otherItem.setElasticityWithholdNum(otherElasticityWithholdNum);
        otherItem.setTotalWithholdNum(otherElasticityWithholdNum + otherNormalWithholdNum);
        for (String dim : req.getDims()) {
            Arrays.asList(NormalWithholdDetailItemVO.class.getDeclaredFields()).forEach(field -> {
                if (field.getName().equals(dim)) {
                    try {
                        field.setAccessible(true);
                        field.set(otherItem, "其他");
                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                    }
                }
            });
        }
        otherItem.setOtherRaw(true);
        itemList.add(otherItem);
        return new NormalWithholdDetailResp(itemList, itemList.size());
    }

    public static NormalWithholdDetailResp builder(List<NormalWithholdDetailItemVO> list, NormalWithholdLineChartReq req) {
        List<NormalWithholdDetailItemVO> itemList = ListUtils.newList();
        if (ListUtils.isEmpty(list)) {
            return new NormalWithholdDetailResp(itemList, itemList.size());
        }

        // 排除topN
        List<NormalWithholdLineChartReq.ExcludeWithholdItem> excludeWithholdItems = req.getExcludeWithholdItems();
        list = list.stream().filter(item ->  !ListUtils.contains(excludeWithholdItems,
                o -> StringUtils.equals(o.getGroupKey(),item.getGroupKey()))).collect(Collectors.toList());
        Map<String,List<NormalWithholdDetailItemVO>> group = ListUtils.groupBy(list, NormalWithholdDetailItemVO::getStatTime);

        List<NormalWithholdDetailItemVO> ret = ListUtils.newArrayList();
        for (Map.Entry<String,List<NormalWithholdDetailItemVO>> entry : group.entrySet()) {
            List<NormalWithholdDetailItemVO> entryValue = entry.getValue();
            NormalWithholdDetailItemVO item = new NormalWithholdDetailItemVO();
            item.setStatTime(entry.getKey());
            item.setNormalWithholdNum(entryValue.stream().mapToInt(NormalWithholdDetailItemVO::getNormalWithholdNum).sum());
            item.setNormalWithholdNumLe14(entryValue.stream().map(NormalWithholdDetailItemVO::getNormalWithholdNumLe14).reduce(BigDecimal.ZERO,BigDecimal::add));
            item.setNormalWithholdNumGt14(entryValue.stream().map(NormalWithholdDetailItemVO::getNormalWithholdNumGt14).reduce(BigDecimal.ZERO,BigDecimal::add));
            item.setElasticityWithholdNum(entryValue.stream().mapToInt(NormalWithholdDetailItemVO::getElasticityWithholdNum).sum());
            item.setTotalWithholdNum(entryValue.stream().mapToInt(NormalWithholdDetailItemVO::getTotalWithholdNum).sum());
            ret.add(item);
        }
        ret.sort(Comparator.comparing(NormalWithholdDetailItemVO::getStatTime));

        return new NormalWithholdDetailResp(ret, ret.size());
    }
}
