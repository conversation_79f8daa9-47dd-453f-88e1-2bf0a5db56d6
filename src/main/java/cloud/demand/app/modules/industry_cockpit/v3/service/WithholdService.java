package cloud.demand.app.modules.industry_cockpit.v3.service;

import cloud.demand.app.modules.industry_cockpit.v3.model.req.LineChartReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.LogicalTagsWithholdDetailTableReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.LogicalTagsWithholdLineChartReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.NormalWithholdDetailTableReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.NormalWithholdLineChartReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.SummaryWithholdDetailTableReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.SummaryWithholdLineChartReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.LogicalTagsWithholdDetailResp;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.NormalWithholdDetailResp;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.OverviewWithholdChartResp;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.SummaryWithholdDetailResp;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;

/**
 * <AUTHOR>
 * @since 2025/5/23 20:36
 */
public interface WithholdService {

    /**
     * 获取总览大盘图表数据（预扣）
     * @param req OverviewChartReq
     * @return OverviewChartResp
     */
    OverviewWithholdChartResp getOverviewNormalWithholdChartData(LineChartReq req);

    /**
     * 获取预扣明细表数据
     * @param req WithholdDetailTableReq
     * @return WithholdDetailResp
     */
    NormalWithholdDetailResp getNormalWithholdDetailTable(NormalWithholdDetailTableReq req);

    /**
     * 导出预扣明细表数据
     * @param req WithholdDetailTableReq
     * @return ResponseEntity<InputStreamResource>
     */
    ResponseEntity<InputStreamResource> exportNormalWithholdDetailTable(NormalWithholdDetailTableReq req);

    /**
     * 获取预扣趋势图数据
     * @param req WithholdLineChartReq
     * @return WithholdDetailResp
     */
    NormalWithholdDetailResp getNormalWithholdLineChart(NormalWithholdLineChartReq req);

    /**
     * 获取逻辑区预扣明细表数据
     * @param req LogicalTagsWithholdDetailTableReq
     * @return LogicalTagsWithholdDetailResp
     */
    LogicalTagsWithholdDetailResp getLogicalTagsWithholdDetailTable(LogicalTagsWithholdDetailTableReq req);

    /**
     * 导出逻辑区预扣明细表数据
     * @param req LogicalTagsWithholdDetailTableReq
     * @return ResponseEntity<InputStreamResource>
     */
    ResponseEntity<InputStreamResource> exportLogicalTagsWithholdDetailTable(LogicalTagsWithholdDetailTableReq req);

    /**
     * 获取逻辑区预扣趋势图数据
     * @param req LogicalTagsWithholdLineChartReq
     * @return LogicalTagsWithholdDetailResp
     */
    LogicalTagsWithholdDetailResp getLogicalTagsWithholdLineChart(LogicalTagsWithholdLineChartReq req);


    /**
     * 获取总揽预扣明细表数据
     * @param req SummaryWithholdDetailTableReq
     * @return SummaryWithholdDetailResp
     */
    SummaryWithholdDetailResp getSummaryWithholdDetailTable(SummaryWithholdDetailTableReq req);

    /**
     * 导出总览预扣明细表数据
     * @param req SummaryWithholdDetailTableReq
     * @return ResponseEntity<InputStreamResource>
     */
    ResponseEntity<InputStreamResource> exportSummaryWithholdDetailTable(SummaryWithholdDetailTableReq req);

    /**
     * 获取总览预扣趋势图数据
     * @param req SummaryWithholdDetailTableReq
     * @return SummaryWithholdDetailResp
     */
    SummaryWithholdDetailResp getSummaryWithholdLineChart(SummaryWithholdLineChartReq req);

}
