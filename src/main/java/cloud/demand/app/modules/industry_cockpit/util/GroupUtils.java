package cloud.demand.app.modules.industry_cockpit.util;

import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.lang3.StringUtils;
import yunti.boot.exception.BizException;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/4/12 22:58
 */
public class GroupUtils {

    public static <T> String getDimsGroupKey(T entity, List<String> dims) {
        StringBuffer bf = new StringBuffer();
        Map<String, Object> map = new HashMap<>();
        Class<?> clazz = entity.getClass();
        while (clazz != Object.class) {
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                if (map.containsKey(field.getName())) {
                    continue;
                }
                field.setAccessible(true);
                try {
                    map.put(field.getName(), field.get(entity));
                } catch (IllegalAccessException e) {
                    throw new BizException("不合法的维度");
                }
            }
            clazz = clazz.getSuperclass();
        }
        for (String dim : dims) {
            bf.append("@").append(map.get(dim));
        }
        return bf.toString();
    }

    public static <T> T mergeList(List<T> list, List<String> dimsFields, List<String> sumFields) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        try {
            List<String> dimsList = ListUtils.newArrayList();
            List<String> sumList = ListUtils.newArrayList();
            T mergedInstance = (T) list.get(0).getClass().getDeclaredConstructor().newInstance();
            Class clazz = list.get(0).getClass();
            while (clazz != Object.class) {
                Field[] fields = clazz.getDeclaredFields();
                for (Field field : fields) {
                    if(StringUtils.isNotBlank(field.getName()) && dimsFields.contains(field.getName()) && !dimsList.contains(field.getName())){
                        field.setAccessible(true);
                        field.set(mergedInstance, field.get(list.get(0)));
                        dimsList.add(field.getName());
                    }
                    if(StringUtils.isNotBlank(field.getName()) && sumFields.contains(field.getName()) && !sumList.contains(field.getName())){
                        field.setAccessible(true);
                        Object sum = null;
                        for (T item : list) {
                            Object value = field.get(item);
                            if (sum == null) {
                                sum = value;
                            } else {
                                if (field.getType() == Integer.class) {
                                    sum = (Integer) sum + (Integer) value;
                                } else if (field.getType() == Double.class) {
                                    sum = (Double) sum + (Double) value;
                                } else if (field.getType() == Long.class) {
                                    sum = (Long) sum + (Long) value;
                                } else if (field.getType() == Float.class) {
                                    sum = (Float) sum + (Float) value;
                                } else if (field.getType() == BigDecimal.class) {
                                    sum = SoeCommonUtils.addWithNull((BigDecimal) sum, (BigDecimal) value);
                                } else {
                                    throw new BizException("暂不支持求和类型。。。:" + field.getType().getName());
                                }
                            }
                        }
                        field.set(mergedInstance, sum);
                        sumList.remove(field.getName());
                    }
                }
                clazz = clazz.getSuperclass();
            }
            return mergedInstance;
        } catch (InvocationTargetException | InstantiationException | IllegalAccessException |
                 NoSuchMethodException e) {
            throw new BizException("不支持维度");
        }
    }
}
