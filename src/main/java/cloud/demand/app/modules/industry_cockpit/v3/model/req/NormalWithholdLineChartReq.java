package cloud.demand.app.modules.industry_cockpit.v3.model.req;

import cloud.demand.app.modules.industry_cockpit.v3.model.vo.IWithholdDetailItemGroupKey;
import cloud.demand.app.modules.sop_return.frame.where.anno.SopReportWhere;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/26 16:45
 */
@Data
public class NormalWithholdLineChartReq extends NormalWithholdReq {


    @SopReportWhere(sql = "stat_time >= ?")
    private LocalDate startStatTime = LocalDate.now().plusMonths(-3).with(TemporalAdjusters.firstDayOfYear());

    @SopReportWhere(sql = "stat_time <= ?")
    private LocalDate endStatTime = LocalDate.now();

    private List<String> dims = ListUtils.newArrayList();// 维度:industryDept、warZone、customerShortName、instanceType、gpuCardType、regionName、
    // zoneName、appId、uin、instanceModel、reserveMode、withholdDuration

    private List<ExcludeWithholdItem> excludeWithholdItems;

    @SopReportWhere
    private String withholdDuration;

    @SopReportWhere
    private Integer withholdDays;

    @Data
    public static class ExcludeWithholdItem implements IWithholdDetailItemGroupKey {
        private String industryDept;

        private String warZone;

        private String customerShortName;

        private String instanceType;

        private String instanceFamily;

        private String gpuCardType;

        private String regionName;

        private String zoneName;

        private String appId;

        private String uin;

        private String instanceModel;

        private String reserveMode;

        private String withholdDuration;

        public String getGroupKey() {
            return StringUtils.joinWith("@", industryDept, warZone, customerShortName, instanceType, instanceFamily, gpuCardType, regionName, zoneName, appId, uin, instanceModel, reserveMode,withholdDuration);
        }

        @Override
        public String getCustomhouseTitle() {
            return null;
        }

        @Override
        public String getYearMonth() {
            return null;
        }

        @Override
        public String getDemandType() {
            return null;
        }
    }
}
