package cloud.demand.app.modules.industry_cockpit.v3.model.vo;

import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Objects;

@Data
public class SummaryWithholdDetailItemVO implements Comparable<SummaryWithholdDetailItemVO> {

    @ExcelProperty(value = "切片时间", index = 0)
    @ContentStyle(dataFormat = 49)
    private String statTime;

    @ExcelProperty(value = "行业部门", index = 1)
    private String industryDept;

    @ExcelProperty(value = "战区", index = 2)
    private String warZone;

    @ExcelProperty(value = "客户简称", index = 3)
    private String customerShortName;

    @ExcelProperty(value = "实例类型", index = 4)
    private String instanceType;

    @ExcelProperty(value = "实例族", index = 5)
    private String instanceFamily;

    @ExcelProperty(value = "GPU卡型", index = 6)
    private String gpuCardType;

    @ExcelProperty(value = "地域", index = 7)
    private String regionName;

    @ExcelProperty(value = "可用区", index = 8)
    private String zoneName;

    @ExcelProperty(value = "appId", index = 9)
    private String appId;

    @ExcelProperty(value = "uin", index = 10)
    private String uin;

    @ExcelProperty(value = "普通预扣核数\n(A)", index = 11)
    private BigDecimal normalWithholdNum = BigDecimal.ZERO;

    @ExcelIgnore
    private BigDecimal normalWithholdNumLe14 = BigDecimal.ZERO;//普通预扣核数<=14天

    @ExcelIgnore
    private BigDecimal normalWithholdNumGt14 = BigDecimal.ZERO;//普通预扣核数>14天

    @ExcelProperty(value = "弹性预扣核数\n(B)", index = 12)
    private BigDecimal elasticityWithholdNum = BigDecimal.ZERO;

    @ExcelProperty(value = "常规预扣核数\n(A+B)", index = 13)
    private BigDecimal totalNormalWithholdNum = BigDecimal.ZERO;

    @ExcelProperty(value = "逻辑区预扣核数\n(C)", index = 14)
    private BigDecimal logicalWithholdNum = BigDecimal.ZERO;

    @ExcelProperty(value = "标签预扣核数\\n(C)", index = 15)
    private BigDecimal tagsWithholdNum = BigDecimal.ZERO;

    @ExcelProperty(value = "总库存核数\n(A+B+C+D)", index = 16)
    private BigDecimal totalWithholdNum = BigDecimal.ZERO;

    @ExcelIgnore
    private boolean otherRaw = false;

    @Override
    public int compareTo(@NotNull SummaryWithholdDetailItemVO o) {
        return o.totalWithholdNum.compareTo(this.totalWithholdNum);
    }

    @JsonIgnore
    public String getGroupKey() {
        return StringUtils.joinWith("@", industryDept, warZone, customerShortName, instanceType, instanceFamily, gpuCardType, regionName, zoneName, appId, uin);
    }

    public static SummaryWithholdDetailItemVO transform(NormalWithholdDetailItemVO vo) {
        SummaryWithholdDetailItemVO ret = new SummaryWithholdDetailItemVO();
        BeanUtils.copyProperties(vo, ret);
        BigDecimal normalWithholdNum = Objects.isNull(vo.getNormalWithholdNum()) ? BigDecimal.ZERO :  BigDecimal.valueOf(vo.getNormalWithholdNum());
        BigDecimal elasticityWithholdNum = Objects.isNull(vo.getElasticityWithholdNum()) ? BigDecimal.ZERO : BigDecimal.valueOf(vo.getElasticityWithholdNum());
        ret.setNormalWithholdNum(normalWithholdNum);
        ret.setElasticityWithholdNum(elasticityWithholdNum);
        ret.setTotalNormalWithholdNum(SoeCommonUtils.addWithNull(normalWithholdNum,elasticityWithholdNum));
        ret.setTotalWithholdNum(ret.getTotalNormalWithholdNum());
        return ret;
    }

    public static SummaryWithholdDetailItemVO transform(LogicalTagsWithholdDetailItemVO vo) {
        SummaryWithholdDetailItemVO ret = new SummaryWithholdDetailItemVO();
        BeanUtils.copyProperties(vo, ret);
        ret.setTagsWithholdNum(vo.getTagsWithholdNum());
        ret.setLogicalWithholdNum(vo.getLogicalWithholdNum());
        ret.setTotalWithholdNum(SoeCommonUtils.addWithNull(ret.getLogicalWithholdNum(),ret.getTagsWithholdNum()));
        return ret;
    }
}