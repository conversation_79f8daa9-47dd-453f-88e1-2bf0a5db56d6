package cloud.demand.app.modules.industry_cockpit.service.filler.hander;

import cloud.demand.app.common.utils.StdUtils;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.industry_cockpit.service.filler.CountryNameFiller;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.core.FillerHandler;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class CountryNameFillerHandler implements FillerHandler<CountryNameFiller> {

    @Override
    public int getExecOrder() {
        return FillerHandler.countryNameFillerHandlerOrder;
    }

    @Resource
    private DictService dictService;

    @Override
    public void fill(List<CountryNameFiller> obj) {
        Map<String, String> region2CountryMap = dictService.getRegion2CountryMapping();
        Map<String, String> regionName2CountryMap = dictService.getRegionName2CountryMapping();
        for (CountryNameFiller filler : obj) {
            if (filler == null) {
                continue;
            }
            if(StringUtils.isNotBlank(filler.provideRegion())){
                String countryName = region2CountryMap.get(filler.provideRegion());
                if (StringUtils.isNotBlank(countryName)) {
                    filler.fillCountryName(countryName);
                    continue;
                }
            }
            if(StringUtils.isNotBlank(filler.provideRegionName())){
                String countryName = regionName2CountryMap.get(filler.provideRegionName());
                if (StringUtils.isNotBlank(countryName)) {
                    filler.fillCountryName(countryName);
                    continue;
                }
            }
            filler.fillCountryName(StdUtils.EMPTY_STR);
        }
    }
}
