package cloud.demand.app.modules.industry_cockpit.service.filler.hander;

import cloud.demand.app.common.utils.BatchUtil;
import cloud.demand.app.common.utils.StdUtils;
import cloud.demand.app.modules.industry_cockpit.service.filler.CustomerInfoByAppIdFiller;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.core.FillerHandler;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.string.StringTools;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class CustomerInfoByAppIdFillerHandler implements FillerHandler<CustomerInfoByAppIdFiller> {

    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Override
    public int getExecOrder() {
        return FillerHandler.customerInfoByAppIdFillerHandlerOrder;
    }

    @Override
    public void fill(List<CustomerInfoByAppIdFiller> result) {
        // 改为分批操作，提高效率(之前的做法查询dwd_txy_appid_info_cf全表数据量太大了)，降低内存消耗
        BatchUtil.syncBatchExec(result, 20000, this::fillOneBatch);
    }

    private void fillOneBatch(List<CustomerInfoByAppIdFiller> oneBatchList) {
        Map<String, List<CustomerInfoByAppIdFiller>> groupData = oneBatchList.stream()
                .collect(Collectors.groupingBy(CustomerInfoByAppIdFiller::provideAppId));
        String sql = "select toString(appid) as appid,toString(uin) as uin,uin_type,customer_type, customer_name, customer_short_name, war_zone, industry_dept "
                + "from dwd_txy_appid_info_cf where appid in (?)";
        List<Map> infoList = ckcldStdCrpDBHelper.getRaw(Map.class, sql, groupData.keySet());

        if (ListUtils.isEmpty(infoList)) {
            return;
        }
        List<CustomerInfoByAppIdFiller> fillers;
        for (Map item : infoList) {
            if (item == null || MapUtils.getString(item, "appid") == null) {
                continue;
            }
            String appid = MapUtils.getString(item, "appid");
            fillers = groupData.get(appid);
            if (ListUtils.isEmpty(fillers)) {
                continue;
            }
            String uin = MapUtils.getString(item, "uin");
            String customerName = handleStr(MapUtils.getString(item, "customer_name"));
            String customerShortName = handleStr(MapUtils.getString(item, "customer_short_name"));
            String warZone = handleStr(MapUtils.getString(item, "war_zone"));
            String industryDept = handleStr(MapUtils.getString(item, "industry_dept"));
            Integer uinType = MapUtils.getInteger(item, "uin_type");
            if (Objects.isNull(uinType)) {
                uinType = 1;
            }
            Integer isInner = uinType == 0 ? 1 : 0;
            Integer customerType = MapUtils.getInteger(item, "customer_type");
            if (Objects.isNull(customerType)) {
                customerType = -1;
            }
            for (CustomerInfoByAppIdFiller data : fillers) {
                data.fillUin(uin);
                data.fillCustomerName(customerName);
                data.fillCustomerShortName(customerShortName);
                data.fillPanShiWarZone(warZone);
                data.fillIndustryDept(industryDept);
                data.fillIsInner(isInner);
                data.fillUinType(uinType);
                data.fillCustomerType(customerType);
            }
        }
    }

    private static String handleStr(String str) {
        if (StringTools.isBlank(str)) {
            return StdUtils.EMPTY_STR;
        }
        return str;
    }
}
