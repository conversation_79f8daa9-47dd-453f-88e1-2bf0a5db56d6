package cloud.demand.app.modules.industry_cockpit.v3.model.resp;

import cloud.demand.app.modules.industry_cockpit.v3.model.req.LogicalTagsWithholdDetailTableReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.LogicalTagsWithholdLineChartReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.vo.LogicalTagsWithholdDetailItemVO;
import cloud.demand.app.modules.industry_resource_month_report.utils.ReportUtils;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import yunti.boot.exception.BizException;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/8/26 16:52
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LogicalTagsWithholdDetailResp {

    private List<LogicalTagsWithholdDetailItemVO> detailItemList;
    private int count;


    public static LogicalTagsWithholdDetailResp builder(List<LogicalTagsWithholdDetailItemVO> list, LogicalTagsWithholdDetailTableReq req) {
        if(Objects.isNull(req.getTopN()) || req.getTopN() > list.size()) {
            return new LogicalTagsWithholdDetailResp(list, list.size());
        }
        LogicalTagsWithholdDetailItemVO other = new LogicalTagsWithholdDetailItemVO();
        for (String dim : req.getDims()) {
            try {
                Field field = LogicalTagsWithholdDetailItemVO.class.getDeclaredField(dim);
                field.setAccessible(true);
                field.set(other, ReportUtils.OTHER);
            } catch (NoSuchFieldException | IllegalAccessException e) {
                throw new BizException("维度不存在 或 字段访问异常");
            }
        }
        other.setOtherRaw(true);
        List<LogicalTagsWithholdDetailItemVO> topN = ReportUtils.getTopN(list, req.getTopN(), other, (o1, o2) -> {
            o1.setCpuCoreBad(SoeCommonUtils.addWithNull(o1.getCpuCoreBad(), o2.getCpuCoreBad()));
            o1.setCpuCoreGood(SoeCommonUtils.addWithNull(o1.getCpuCoreGood(), o2.getCpuCoreGood()));
            o1.setCpuCoreIdle(SoeCommonUtils.addWithNull(o1.getCpuCoreIdle(), o2.getCpuCoreIdle()));
            o1.setCpuCoreTotal(SoeCommonUtils.addWithNull(o1.getCpuCoreTotal(), o2.getCpuCoreTotal()));
            o1.setLogicalWithholdNum(SoeCommonUtils.addWithNull(o1.getLogicalWithholdNum(), o2.getLogicalWithholdNum()));
            o1.setTagsWithholdNum(SoeCommonUtils.addWithNull(o1.getTagsWithholdNum(), o2.getTagsWithholdNum()));
            o1.setTotalWithholdNum(SoeCommonUtils.addWithNull(o1.getTotalWithholdNum(), o2.getTotalWithholdNum()));
        });
        return new LogicalTagsWithholdDetailResp(topN,topN.size());
    }

    public static LogicalTagsWithholdDetailResp builder(List<LogicalTagsWithholdDetailItemVO> list, LogicalTagsWithholdLineChartReq req) {
        List<LogicalTagsWithholdDetailItemVO> itemList = ListUtils.newList();
        if (ListUtils.isEmpty(list)) {
            return new LogicalTagsWithholdDetailResp(itemList, itemList.size());
        }

        // 排除topN
        List<LogicalTagsWithholdDetailItemVO> excludeWithholdItems = req.getExcludeWithholdItems();
        list = list.stream().filter(item ->  !ListUtils.contains(excludeWithholdItems,
                o -> StringUtils.equals(o.getGroupKey(),item.getGroupKey()))).collect(Collectors.toList());
        Map<String,List<LogicalTagsWithholdDetailItemVO>> group = ListUtils.groupBy(list, LogicalTagsWithholdDetailItemVO::getStatTime);

        List<LogicalTagsWithholdDetailItemVO> ret = ListUtils.newArrayList();
        for (Map.Entry<String,List<LogicalTagsWithholdDetailItemVO>> entry : group.entrySet()) {
            List<LogicalTagsWithholdDetailItemVO> entryValue = entry.getValue();
            LogicalTagsWithholdDetailItemVO item = new LogicalTagsWithholdDetailItemVO();
            item.setStatTime(entry.getKey());
            item.setCpuCoreTotal(entryValue.stream().map(LogicalTagsWithholdDetailItemVO::getCpuCoreTotal).reduce(BigDecimal.ZERO,BigDecimal::add));
            item.setCpuCoreIdle(entryValue.stream().map(LogicalTagsWithholdDetailItemVO::getCpuCoreIdle).reduce(BigDecimal.ZERO,BigDecimal::add));
            item.setCpuCoreBad(entryValue.stream().map(LogicalTagsWithholdDetailItemVO::getCpuCoreBad).reduce(BigDecimal.ZERO,BigDecimal::add));
            item.setCpuCoreGood(entryValue.stream().map(LogicalTagsWithholdDetailItemVO::getCpuCoreGood).reduce(BigDecimal.ZERO,BigDecimal::add));
            item.setTagsWithholdNum(entryValue.stream().map(LogicalTagsWithholdDetailItemVO::getTagsWithholdNum).reduce(BigDecimal.ZERO,BigDecimal::add));
            item.setLogicalWithholdNum(entryValue.stream().map(LogicalTagsWithholdDetailItemVO::getLogicalWithholdNum).reduce(BigDecimal.ZERO,BigDecimal::add));
            item.setTotalWithholdNum(entryValue.stream().map(LogicalTagsWithholdDetailItemVO::getTotalWithholdNum).reduce(BigDecimal.ZERO,BigDecimal::add));
            ret.add(item);
        }
        ret.sort(Comparator.comparing(LogicalTagsWithholdDetailItemVO::getStatTime));

        return new LogicalTagsWithholdDetailResp(ret,ret.size());
    }
}
