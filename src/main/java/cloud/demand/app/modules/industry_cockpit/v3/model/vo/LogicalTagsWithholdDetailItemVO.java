package cloud.demand.app.modules.industry_cockpit.v3.model.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class LogicalTagsWithholdDetailItemVO implements Comparable<LogicalTagsWithholdDetailItemVO> {

    @ExcelProperty(value = "切片时间", index = 0)
    @ContentStyle(dataFormat = 49)
    @Column("stat_time")
    private String statTime;

    @ExcelIgnore
    @Column("any_biz_type")
    private String bizType;

    @ExcelProperty(value = "预扣类型", index = 1)
    @Column("any_withhold_type")
    private String withholdType;

    @ExcelProperty(value = "行业部门", index = 2)
    @Column("any_industry_dept")
    private String industryDept;

    @ExcelProperty(value = "战区", index = 3)
    @Column("any_war_zone")
    private String warZone;

    @ExcelProperty(value = "客户简称", index = 4)
    @Column("any_customer_short_name")
    private String customerShortName;

    @ExcelProperty(value = "APP_ID", index = 5)
    @Column("any_app_id")
    private String appId;

    @ExcelProperty(value = "UIN", index = 6)
    @Column("any_uin")
    private String uin;

    @ExcelIgnore
    @Column("any_instance_family")
    private String instanceFamily;

    @ExcelProperty(value = "实例类型", index = 7)
    @Column("any_instance_type")
    private String instanceType;

    @ExcelProperty(value = "母机IP", index = 8)
    @Column("any_host_ip")
    private String hostIp;

    /**
     * 境内外<br/>Column: [customhouse_title]
     */
    //@ExcelProperty(value = "境内外", index = 9)
    @ExcelIgnore
    @Column(value = "any_customhouse_title")
    private String customhouseTitle;

    /**
     * 国家<br/>Column: [country_name]
     */
    //@ExcelProperty(value = "国家", index = 10)
    @ExcelIgnore
    @Column(value = "any_country_name")
    private String countryName;

    /**
     * 区域<br/>Column: [area_name]
     */
    //@ExcelProperty(value = "区域", index = 11)

    @Column(value = "any_area_name")
    @ExcelIgnore
    private String areaName;

    /**
     * 地域<br/>Column: [region_name]
     */
    @ExcelProperty(value = "地域", index = 9)
    @Column(value = "any_region_name")
    private String regionName;

    /**
     * 可用区<br/>Column: [zone_name]
     */
    @ExcelProperty(value = "可用", index = 10)
    @Column(value = "any_zone_name")
    private String zoneName;

    @ExcelProperty(value = "可用区ID", index = 11)
    @Column("any_zone_id")
    private String zoneId;

    @ExcelProperty(value = "设备类型", index = 12)
    @Column("any_cvm_standard_type")
    private String cvmStandardType;

    /**
     * 逻辑区appmask<br/>Column: [app_mask]
     */
    @ExcelProperty(value = "逻辑区appmask", index = 13)
    @Column(value = "any_app_mask")
    private String appMask;

    /**
     * CMDB四级模块id<br/>Column: [bsi_id]
     */
    @ExcelProperty(value = "CMDB四级模块id", index = 14)
    @Column(value = "any_bsi_id")
    private String bsiId;

    /**
     * 库存标签<br/>Column: [stack_label]
     */
    @ExcelProperty(value = "库存标签", index = 15)
    @Column(value = "any_stack_label")
    private String stackLabel;

    /**
     * 总核心数<br/>Column: [cpu_core_total]
     */
    @ExcelProperty(value = "总库存", index = 16)
    @Column(value = "sum_cpu_core_total")
    private BigDecimal cpuCoreTotal;

    /**
     * 好料核心数<br/>Column: [cpu_core_good]
     */
    @ExcelProperty(value = "好料库存", index = 17)
    @Column(value = "sum_cpu_core_good")
    private BigDecimal cpuCoreGood;

    /**
     * 差料核心数<br/>Column: [cpu_core_bad]
     */
    @ExcelProperty(value = "差料库存", index = 18)
    @Column(value = "sum_cpu_core_bad")
    private BigDecimal cpuCoreBad;

    /**
     * 呆料核心数<br/>Column: [cpu_core_idle]
     */
    @ExcelProperty(value = "呆料库存", index = 19)
    @Column(value = "sum_cpu_core_idle")
    private BigDecimal cpuCoreIdle;

    /**
     * 逻辑区 核心数<br/>Column: [sum_cpu_core_logical]
     */
    @ExcelIgnore
    @Column(value = "sum_cpu_core_logical")
    private BigDecimal logicalWithholdNum;

    /**
     * 标签核心数<br/>Column: [sum_cpu_core_tag]
     */
    @ExcelIgnore
    @Column(value = "sum_cpu_core_tag")
    private BigDecimal tagsWithholdNum;


    @ExcelIgnore
    @Column(value = "sum_cpu_core")
    private BigDecimal totalWithholdNum;

    /**
     * 逻辑区信息<br/>Column: [logical_info]
     */
    @ExcelProperty(value = "逻辑区信息", index = 20)
    @Column(value = "any_logical_info")
    private String logicalInfo;



    /**
     * 标签信息<br/>Column: [tag_info]
     */
    @ExcelProperty(value = "标签信息", index = 21)
    @Column(value = "any_tag_info")
    private String tagInfo;

    @ExcelProperty(value = "GPU卡型", index = 22)
    @Column("any_gpu_card_type")
    private String gpuCardType;

    @ExcelIgnore
    private boolean otherRaw = false;

    @Override
    public int compareTo(@NotNull LogicalTagsWithholdDetailItemVO o) {
        return o.cpuCoreTotal.compareTo(this.cpuCoreTotal);
    }

    @JsonIgnore
    public String getGroupKey() {
        return StringUtils.joinWith("@", industryDept, warZone, customerShortName, instanceType, instanceFamily, gpuCardType, customhouseTitle,
                countryName, areaName, regionName, zoneName, appId, uin);
    }

}