package cloud.demand.app.modules.industry_cockpit.service.filler.hander;

import cloud.demand.app.common.utils.StdUtils;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.industry_cockpit.service.InstanceGroupFiller;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.core.FillerHandler;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class InstanceGroupFillerHandler implements FillerHandler<InstanceGroupFiller> {

    @Resource
    private DictService dictService;

    @Override
    public void fill(List<InstanceGroupFiller> obj) {
        Map<String, String> map = dictService.queryInstanceTypeToGroup();
        for (InstanceGroupFiller filler : obj) {
            if (filler == null) {
                continue;
            }
            if (StringUtils.isBlank(filler.provideInstanceType())) {
                continue;
            }
            String instanceGroup = map.get(filler.provideInstanceType());
            if (StringUtils.isNotBlank(instanceGroup)) {
                filler.fillInstanceGroup(instanceGroup);
            }else {
                filler.fillInstanceGroup(StdUtils.EMPTY_STR);
            }
        }

    }
}
