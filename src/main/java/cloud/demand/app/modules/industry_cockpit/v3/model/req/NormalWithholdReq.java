package cloud.demand.app.modules.industry_cockpit.v3.model.req;

import cloud.demand.app.modules.industry_cockpit.auth.AuthCheckParam;
import cloud.demand.app.modules.industry_cockpit.v3.parse.ProductParse;
import cloud.demand.app.modules.sop_return.frame.where.anno.SopReportWhere;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/26 16:45
 */
@Data
public class NormalWithholdReq extends IndustryCockpitV3HistoricalReq {

    @NotNull(message = "产品不能为空")
    @SopReportWhere(parsers = {ProductParse.class})
    @AuthCheckParam
    private String product;

    @SopReportWhere
    @AuthCheckParam(authField = "industry")
    private List<String> industryDept; // 行业部门 支持多选
}
