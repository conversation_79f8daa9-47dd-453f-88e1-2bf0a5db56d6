package cloud.demand.app.modules.industry_cockpit.v3.task.work;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.industry_cockpit.v3.entity.DwsLogicalTagsWithholdDfDO;
import cloud.demand.app.modules.industry_cockpit.v3.model.vo.SyncDwsLogicalTagsWithholdVO;
import cloud.demand.app.modules.industry_cockpit.v3.task.process.DwsLogicalTagsWithholdProcess;
import cloud.demand.app.modules.mrpv2.entity.SimpleCommonTask;
import cloud.demand.app.modules.mrpv2.enums.SimpleTaskEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.core.FillerService;
import cloud.demand.app.modules.sop.util.CkDBUtils;
import cloud.demand.app.modules.sop_device.sopTask.frame.enums.ITaskEnum;
import cloud.demand.app.modules.sop_device.sopTask.frame.process.ISopProcess;
import cloud.demand.app.modules.sop_device.sopTask.frame.work.AbstractSopWork;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/8/26 11:11
 */
@Service
@Slf4j(topic = "行业驾驶舱-V3-逻辑区预扣-dsm数据：")
public class DwsLogicalTagsWithholdWork extends AbstractSopWork<SimpleCommonTask> {

    @Resource
    private DwsLogicalTagsWithholdProcess process;

    @Resource
    private DBHelper ckcldStdCrpDBHelper;
    @Resource
    private DBHelper planDBHelper;

    @Resource
    private FillerService fillerService;

    @Override
    public ISopProcess<SimpleCommonTask> getTask() {
        return process;
    }

    @Override
    public ITaskEnum getEnum() {
        return SimpleTaskEnum.DWS_LOGICAL_TAGS_WITHHOLD;
    }

    @TaskLog(taskName = "DwsLogicalTagsWithholdWork")
    @Scheduled(fixedRate = 30 * 1000)
    @Override
    public void work() {
        super.work();
    }


    @Override
    public void doWork(SimpleCommonTask simpleCommonTask) {
        String version = simpleCommonTask.getVersion();
        // 需要用到TaskLog，所以用bean取调用
        SpringUtil.getBean(DwsLogicalTagsWithholdWork.class).genData(version);
    }

    @TaskLog(taskName = "industryCockpitV3/logical-whithhold@DWS")
    public void genData(String statTime) {
        // 查询数据
        String sql = ORMUtils.getSql("/sql/industry_cockpit/v3/8_sync_logical_tag_withhold_data.sql");
        List<SyncDwsLogicalTagsWithholdVO> dbList = planDBHelper.getRaw(SyncDwsLogicalTagsWithholdVO.class, sql, statTime);

        List<DwsLogicalTagsWithholdDfDO>  retList = ListUtils.transform(dbList, DwsLogicalTagsWithholdDfDO::transform);
        // 过滤掉无appId的数据
        retList = retList.stream().filter(x -> Objects.nonNull(x.getAppId())).collect(Collectors.toList());
        // 清洗数据
        fillerService.fill(retList);
        // 删除分区
        CkDBUtils.delete(ckcldStdCrpDBHelper, statTime, DwsLogicalTagsWithholdDfDO.class);
        // 添加
        log.info(" size :" + retList.size());
        CkDBUtils.saveBatch(ckcldStdCrpDBHelper, retList);
    }

    /**
     * 每天凌晨8点执行一次
     */
    @Scheduled(cron = "0 0 8 * * ? ")
    public void initTask() {
        initTask(null);
    }

    /**
     * 初始化任务
     */
    public void initTask(LocalDate statTime) {
        if (statTime == null) {
            //昨天
            statTime = LocalDate.now().plusDays(-1);
        }
        SimpleCommonTask simpleCommonTask = new SimpleCommonTask();
        simpleCommonTask.setVersion(DateUtils.format(statTime));
        process.initTask(simpleCommonTask, getEnum());
    }
}
