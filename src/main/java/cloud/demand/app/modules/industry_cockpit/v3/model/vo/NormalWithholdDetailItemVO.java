package cloud.demand.app.modules.industry_cockpit.v3.model.vo;

import cloud.demand.app.modules.industry_cockpit.v3.enums.WithholdDurationEnum;
import cloud.demand.app.modules.industry_cockpit.v3.parse.WithholdDurationConverter;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class NormalWithholdDetailItemVO implements Comparable<NormalWithholdDetailItemVO>,IWithholdDetailItemGroupKey {

    @ExcelProperty(value = "切片时间", index = 0)
    @ContentStyle(dataFormat = 49)
    @Column("stat_time")
    private String statTime;

    @ExcelProperty(value = "行业部门", index = 1)
    @Column("any_industry_dept")
    private String industryDept;

    @ExcelProperty(value = "战区", index = 2)
    @Column("any_war_zone")
    private String warZone;

    @ExcelProperty(value = "客户简称", index = 3)
    @Column("any_customer_short_name")
    private String customerShortName;

    @ExcelProperty(value = "实例类型", index = 4)
    @Column("any_instance_type")
    private String instanceType;

    @ExcelProperty(value = "实例族", index = 5)
    @Column("any_instance_family")
    private String instanceFamily;

    @ExcelProperty(value = "GPU卡型", index = 6)
    @Column("any_gpu_card_type")
    private String gpuCardType;

    @ExcelProperty(value = "地域", index = 7)
    @Column("any_region_name")
    private String regionName;

    @ExcelProperty(value = "可用区", index = 8)
    @Column("any_zone_name")
    private String zoneName;

    @ExcelProperty(value = "appId", index = 9)
    @Column("any_app_id")
    private String appId;

    @ExcelProperty(value = "uin", index = 10)
    @Column("any_uin")
    private String uin;

    @ExcelProperty(value = "实例型号", index = 11)
    @Column("any_instance_model")
    private String instanceModel;

    @ExcelProperty(value = "预扣类型", index = 12)
    @Column("any_reserve_mode")
    private String reserveMode;

    @Column("any_withhold_duration")
    @ExcelProperty(value = "预扣时长", index = 13, converter = WithholdDurationConverter.class)
    private String withholdDuration;

    @Column("any_withhold_days")
    @ExcelProperty(value = "预扣天数", index = 14)
    private String withholdDays;

    @ExcelIgnore
    private WithholdDurationEnum withholdDurationEnum;

    @ExcelProperty(value = "普通预扣核数/卡数", index = 15)
    @Column("normal_withhold_num")
    private Integer normalWithholdNum;

    @ExcelIgnore
    @Column("normal_withhold_num_le_14")
    private BigDecimal normalWithholdNumLe14;//普通预扣核数<=14天

    @ExcelIgnore
    @Column("normal_withhold_num_gt_14")
    private BigDecimal normalWithholdNumGt14;//普通预扣核数>14天

    @ExcelProperty(value = "弹性预扣核数/卡数", index = 16)
    @Column("elasticity_withhold_num")
    private Integer elasticityWithholdNum;

    @ExcelProperty(value = "总预扣核数/卡数", index = 17)
    @Column("total_withhold_num")
    private Integer totalWithholdNum;

    @ExcelIgnore
    @Column("avg_withhold_days")
    private BigDecimal avgWithholdDays;

    @ExcelIgnore
    @Column("total_withhold_days")
    private Integer totalWithholdDays;

    @ExcelIgnore
    private boolean otherRaw = false;

    @Override
    public int compareTo(@NotNull NormalWithholdDetailItemVO o) {
        return o.totalWithholdNum - this.totalWithholdNum;
    }

    @JsonIgnore
    public String getGroupKey() {
        return StringUtils.joinWith("@", industryDept, warZone, customerShortName, instanceType, instanceFamily, gpuCardType, regionName, zoneName, appId, uin, instanceModel, reserveMode,withholdDuration,withholdDays);
    }

    @Override
    public String getCustomhouseTitle() {
        return null;
    }

    @Override
    public String getYearMonth() {
        return null;
    }

    @Override
    public String getDemandType() {
        return null;
    }
}