package cloud.demand.app.modules.industry_cockpit.service.filler.hander;

import cloud.demand.app.common.utils.StdUtils;
import cloud.demand.app.modules.industry_cockpit.service.filler.GpuTypeFiller;
import cloud.demand.app.modules.industry_cockpit.v3.service.IndustryCockpitV3DictService;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.core.FillerHandler;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class GpuTypeFillerHandler implements FillerHandler<GpuTypeFiller> {

    @Override
    public int getExecOrder() {
        return FillerHandler.countryNameFillerHandlerOrder;
    }

    @Resource
    private IndustryCockpitV3DictService industryCockpitV3DictService;

    @Override
    public void fill(List<GpuTypeFiller> obj) {
        Map<String,String> instanceTypeGpuCardTypeMap = industryCockpitV3DictService.getInstanceTypeGpuCardTypeMapping();
        for (GpuTypeFiller filler : obj) {
            if (filler == null) {
                continue;
            }
            if(StringUtils.isNotBlank(filler.provideInstanceType())){
                String gpuType = instanceTypeGpuCardTypeMap.getOrDefault(filler.provideInstanceType(),StdUtils.EMPTY_STR);
                filler.fillGpuType(gpuType);
            }else {
                 filler.fillGpuType(StdUtils.EMPTY_STR);
            }
        }
    }
}
