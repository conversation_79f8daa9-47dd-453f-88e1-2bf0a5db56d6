package cloud.demand.app.modules.industry_cockpit.v3.web;

import cloud.demand.app.modules.industry_cockpit.v3.model.req.IndustryCockpitV3TaskReq;
import cloud.demand.app.modules.industry_cockpit.v3.task.work.DwdPplBillingScaleMonthlyViewWork;
import cloud.demand.app.modules.industry_cockpit.v3.task.work.DwsIndustryCockpitV3WithholdWork;
import cloud.demand.app.modules.industry_cockpit.v3.task.work.DwsLogicalTagsWithholdWork;
import cloud.demand.app.modules.mrpv2.entity.SimpleCommonTask;
import cloud.demand.app.modules.sop.domain.ReturnT;
import com.pugwoo.wooutils.lang.DateUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.exception.ITException;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;

/**
 * <AUTHOR>
 * @since 2024/8/26 15:38
 */
@JsonrpcController("/ops/industry-cockpit-v3/task")
public class IndustryCockpitV3TaskController {

    @Resource
    private DwsIndustryCockpitV3WithholdWork withholdWork;

    @Resource
    private DwsLogicalTagsWithholdWork logicalTagsWithholdWork;

    @Resource
    private DwdPplBillingScaleMonthlyViewWork billingWork;

    /**
     * 初始化定时任务
     */
    @RequestMapping
    public ReturnT<String> genDataDoWork(@JsonrpcParam @Valid IndustryCockpitV3TaskReq req) {
        LocalDate startTime = req.getStartTime();
        LocalDate endTime = req.getEndTime();
        if (startTime != null && endTime != null && !startTime.isAfter(endTime)) {
            while (!startTime.isAfter(endTime)) {
                SimpleCommonTask simpleCommonTask = new SimpleCommonTask();
                simpleCommonTask.setVersion(DateUtils.format(startTime));
                if (req.getTaskName().equals("withhold")) {
                    withholdWork.doWork(simpleCommonTask);
                } else if (req.getTaskName().equals("logical_tags_withhold")) {
                    logicalTagsWithholdWork.doWork(simpleCommonTask);
                } else if (req.getTaskName().equals("pplBillScale")) {
                    if (startTime.isEqual(startTime.with(TemporalAdjusters.firstDayOfMonth()))) {
                        billingWork.doWork(simpleCommonTask);
                    }
                }
                startTime = startTime.plusDays(1);
            }
        } else {
            throw new ITException("时间范围格式异常，起始和结束时间不能为空且起始时间不能大于结束时间");
        }
        return ReturnT.ok();
    }

    @RequestMapping
    public ReturnT<String> genDataInitTask(@JsonrpcParam @Valid IndustryCockpitV3TaskReq req) {
        LocalDate startTime = req.getStartTime();
        LocalDate endTime = req.getEndTime();
        if (startTime != null && endTime != null && !startTime.isAfter(endTime)) {
            while (!startTime.isAfter(endTime)) {
                if (req.getTaskName().equals("withhold")) {
                    withholdWork.initTask(startTime);
                } else if (req.getTaskName().equals("logical_tags_withhold")) {
                    logicalTagsWithholdWork.initTask(startTime);
                } else if (req.getTaskName().equals("pplBillScale")) {
                    if (startTime.isEqual(startTime.with(TemporalAdjusters.firstDayOfMonth()))) {
                        billingWork.initTask(startTime);
                    }

                }
                startTime = startTime.plusDays(1);
            }
        } else {
            throw new ITException("时间范围格式异常，起始和结束时间不能为空且起始时间不能大于结束时间");
        }
        return ReturnT.ok();
    }
}
