package cloud.demand.app.modules.industry_cockpit.v3.model.req;

import cloud.demand.app.modules.sop_return.frame.where.anno.SopReportWhere;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/26 16:45
 */
@Data
public class LogicalTagsWithholdDetailTableReq extends LogicalTagsWithholdReq implements IInstanceFamilyDimReq {


    @NotNull(message = "statTime不能为空")
    @SopReportWhere
    private LocalDate statTime;

    private List<String> dims = ListUtils.newArrayList();// 维度:instanceType、gpuCardType、industryDept、warZone、customerShortName、customhouseTitle、countryName、areaName、regionName、zoneName

    private Integer topN;
}
