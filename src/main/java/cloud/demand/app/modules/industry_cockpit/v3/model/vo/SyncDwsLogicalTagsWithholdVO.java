package cloud.demand.app.modules.industry_cockpit.v3.model.vo;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

@Data
@ToString
public class SyncDwsLogicalTagsWithholdVO {

    /** 分区键，代表数据版本<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private String statTime;

    /** 所属业务：CVM、CPU<br/>Column: [biz_type] */
    @Column(value = "biz_type")
    private String bizType;


    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 区域<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** 地域<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /**
     * 可用区ID<br/>Column: [zone_id]
     */
    @Column(value = "zone_id")
    private String zoneId;

    /**
     * 母机IP<br/>Column: [host_ip]
     */
    @Column(value = "host_ip")
    private String hostIp;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 母机投放类型<br/>Column: [cvm_type] */
    @Column(value = "cvm_type")
    private String cvmType;

    /** 母机标准机型<br/>Column: [cvm_standard_type] */
    @Column(value = "cvm_standard_type")
    private String cvmStandardType;

    /** 逻辑区appmask<br/>Column: [app_mask] */
    @Column(value = "app_mask")
    private String appMask;

    /** CMDB四级模块id<br/>Column: [bsi_id] */
    @Column(value = "bsi_id")
    private String bsiId;

    /** 库存标签<br/>Column: [stack_label] */
    @Column(value = "stack_label")
    private String stackLabel;

    /** 总核心数<br/>Column: [cpu_core_total] */
    @Column(value = "cpu_core_total")
    private BigDecimal cpuCoreTotal;

    /** 好料核心数<br/>Column: [cpu_core_good] */
    @Column(value = "cpu_core_good")
    private BigDecimal cpuCoreGood;

    /** 差料核心数<br/>Column: [cpu_core_bad] */
    @Column(value = "cpu_core_bad")
    private BigDecimal cpuCoreBad;

    /** 呆料核心数<br/>Column: [cpu_core_idle] */
    @Column(value = "cpu_core_idle")
    private BigDecimal cpuCoreIdle;

    /** 逻辑区信息<br/>Column: [logical_info] */
    @Column(value = "logical_info")
    private String logicalInfo;

    /** 标签信息<br/>Column: [tag_info] */
    @Column(value = "tag_info")
    private String tagInfo;

}