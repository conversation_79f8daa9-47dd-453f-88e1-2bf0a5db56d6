package cloud.demand.app.modules.industry_cockpit.v3.model.req;

import cloud.demand.app.modules.industry_cockpit.v3.model.vo.LogicalTagsWithholdDetailItemVO;
import cloud.demand.app.modules.sop_return.frame.where.anno.SopReportWhere;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;

import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/26 16:45
 */
@Data
public class LogicalTagsWithholdLineChartReq extends LogicalTagsWithholdReq {


    @SopReportWhere(sql = "stat_time >= ?")
    private LocalDate startStatTime = LocalDate.now().plusMonths(-3).with(TemporalAdjusters.firstDayOfYear());

    @SopReportWhere(sql = "stat_time <= ?")
    private LocalDate endStatTime = LocalDate.now();

    private List<String> dims = ListUtils.newArrayList();// 维度:industryDept、warZone、customerShortName、instanceType、gpuCardType、regionName、
    // zoneName、appId、uin、instanceModel、reserveMode、withholdDuration

    private List<LogicalTagsWithholdDetailItemVO> excludeWithholdItems;

}
