package cloud.demand.app.modules.industry_cockpit.v3.web;

import cloud.demand.app.modules.industry_cockpit.auth.AuthCheck;
import cloud.demand.app.modules.industry_cockpit.v3.constant.IndustryCockpitV3Constant;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.BillServiceLineChartReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.DailyLineChartReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.HistoricalScaleMonthlyAvgReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.HistoricalScaleMonthlySliceReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.IndustryCockpitV3FutureReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.LogicalTagsWithholdDetailTableReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.LogicalTagsWithholdLineChartReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.NormalWithholdDetailTableReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.NormalWithholdLineChartReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.OrderOverviewReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.ProfileReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.SummaryWithholdDetailTableReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.SummaryWithholdLineChartReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.FutureScaleTableResp;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.HistoricalScaleDailyChartResp;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.HistoricalScaleDailyHistogramResp;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.HistoricalScaleMonthlyHistogramResp;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.HistoricalScaleMonthlyTableResp;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.LogicalTagsWithholdDetailResp;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.NormalWithholdDetailResp;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.OrderOverviewListResp;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.OverviewBillServiceChartResp;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.OverviewCircularGraphResp;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.OverviewWithholdChartResp;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.SummaryWithholdDetailResp;
import cloud.demand.app.modules.industry_cockpit.v3.service.IndustryCockpitV3ReportService;
import cloud.demand.app.modules.industry_cockpit.v3.service.OrderOverviewService;
import cloud.demand.app.modules.industry_cockpit.v3.service.WithholdService;
import cloud.demand.app.modules.industry_cockpit.v3.validator.ValidListStringToLong;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;

/**
 * <AUTHOR>
 * @since 2024/8/14 15:47
 */
@JsonrpcController("/industry-cockpit-v3/report")
@Slf4j
public class IndustryCockpitV3ReportController {
    @Resource
    private IndustryCockpitV3ReportService industryCockpitV3ReportService;

    @Resource
    private OrderOverviewService orderOverviewService;

    @Resource
    private WithholdService withholdService;

    /////////////////////////////////////总览//////////////////////////////////////////////

    /**
     * 获取行业大盘图表数据
     *
     * @param req LineChartReq
     * @return OverviewChartResp
     */
    @RequestMapping
    @AuthCheck
    public OverviewBillServiceChartResp getOverviewChartData(@JsonrpcParam @Valid @ValidListStringToLong BillServiceLineChartReq req) {
        return industryCockpitV3ReportService.getOverviewChartData(req);
    }

    /**
     * 获取行业大盘图表数据（预扣）
     *
     * @param req LineChartReq
     * @return OverviewChartResp
     */
    @RequestMapping
    @AuthCheck
    public OverviewWithholdChartResp getOverviewWithholdChartData(@JsonrpcParam @Valid @ValidListStringToLong DailyLineChartReq req) {
        
        req.setStartStatTime(LocalDate.now().minusYears(1).with(TemporalAdjusters.firstDayOfYear()));
        return withholdService.getOverviewNormalWithholdChartData(req);
    }

    /**
     * 获取行业大盘环形图数据
     *
     * @param req OverviewCircularGraphReq
     * @return OverviewCircularGraphResp
     */
    @RequestMapping
    @AuthCheck
    public OverviewCircularGraphResp getOverviewCircularGraphData(@JsonrpcParam @Valid @ValidListStringToLong ProfileReq req) {
        return industryCockpitV3ReportService.getOverviewCircularGraphData(req);
    }
    /////////////////////////////////////预扣明细//////////////////////////////////////////////

    /**
     * 查询预扣明细表格
     *
     * @param req WithholdDetailTableReq
     * @return WithholdDetailTableResp
     */
    @RequestMapping
    @AuthCheck
    public NormalWithholdDetailResp getWithholdDetailTable(@JsonrpcParam @Valid @ValidListStringToLong NormalWithholdDetailTableReq req) {
        return withholdService.getNormalWithholdDetailTable(req);
    }

    @RequestMapping
    @AuthCheck
    public ResponseEntity<InputStreamResource> exportWithholdDetailTable(@JsonrpcParam @Valid @ValidListStringToLong NormalWithholdDetailTableReq req) {
        return withholdService.exportNormalWithholdDetailTable(req);
    }

    /**
     * 查询预扣明细折线图
     *
     * @param req WithholdDetailTableReq
     * @return WithholdDetailTableResp
     */
    @RequestMapping
    @AuthCheck
    public NormalWithholdDetailResp getWithholdLineChart(@JsonrpcParam @Valid @ValidListStringToLong NormalWithholdLineChartReq req) {
        
        return withholdService.getNormalWithholdLineChart(req);

    }


    /**
     * 查询预扣明细表格
     *
     * @param req WithholdDetailTableReq
     * @return WithholdDetailTableResp
     */
    @RequestMapping
    @AuthCheck
    public LogicalTagsWithholdDetailResp getLogicalTagsWithholdDetailTable(@JsonrpcParam @Valid @ValidListStringToLong LogicalTagsWithholdDetailTableReq req) {
        return withholdService.getLogicalTagsWithholdDetailTable(req);
    }

    @RequestMapping
    @AuthCheck
    public ResponseEntity<InputStreamResource> exportLogicalTagsWithholdDetailTable(@JsonrpcParam @Valid @ValidListStringToLong LogicalTagsWithholdDetailTableReq req) {
        return withholdService.exportLogicalTagsWithholdDetailTable(req);
    }

    /**
     * 查询预扣明细折线图
     *
     * @param req WithholdDetailTableReq
     * @return WithholdDetailTableResp
     */
    @RequestMapping
    @AuthCheck
    public LogicalTagsWithholdDetailResp getLogicalTagsWithholdLineChart(@JsonrpcParam @Valid @ValidListStringToLong LogicalTagsWithholdLineChartReq req) {

        return withholdService.getLogicalTagsWithholdLineChart(req);

    }

    /**
     * 查询预扣明细表格
     *
     * @param req SummaryWithholdDetailTableReq
     * @return SummaryWithholdDetailResp
     */
    @RequestMapping
    @AuthCheck
    public SummaryWithholdDetailResp getSummaryWithholdDetailTable(@JsonrpcParam @Valid @ValidListStringToLong SummaryWithholdDetailTableReq req) {
        return withholdService.getSummaryWithholdDetailTable(req);
    }

    @RequestMapping
    @AuthCheck
    public ResponseEntity<InputStreamResource> exportSummaryWithholdDetailTable(@JsonrpcParam @Valid @ValidListStringToLong SummaryWithholdDetailTableReq req) {
        return withholdService.exportSummaryWithholdDetailTable(req);
    }

    /**
     * 查询预扣明细折线图
     *
     * @param req SummaryWithholdLineChartReq
     * @return SummaryWithholdDetailResp
     */
    @RequestMapping
    @AuthCheck
    public SummaryWithholdDetailResp getSummaryWithholdLineChart(@JsonrpcParam @Valid @ValidListStringToLong SummaryWithholdLineChartReq req) {

        return withholdService.getSummaryWithholdLineChart(req);

    }

    /////////////////////////////////////历史规模月度表//////////////////////////////////////////////

    /**
     * 获取行业大盘历史规模月度变化表数据-月切变化量
     *
     * @param req HistoricalScaleMonthlySliceReq
     * @return HistoricalScaleMonthlyTableResp
     */
    @RequestMapping
    @AuthCheck
    public HistoricalScaleMonthlyTableResp getHistoricalScaleMonthlySliceChangeTable(@JsonrpcParam @Valid @ValidListStringToLong HistoricalScaleMonthlySliceReq req) {
        req.setYearMonth(IndustryCockpitV3Constant.MONTHLY_SLICE_CHANG);
        
        return industryCockpitV3ReportService.getHistoricalScaleMonthlyTable(req, IndustryCockpitV3Constant.MONTHLY_SLICE_CHANG);
    }

    /**
     * 获取行业大盘历史规模月度变化表数据-月均变化量
     *
     * @param req HistoricalScaleMonthlyAvgReq
     * @return HistoricalScaleMonthlyTableResp
     */
    @RequestMapping
    @AuthCheck
    public HistoricalScaleMonthlyTableResp getHistoricalScaleMonthlyAvgChangeTable(@JsonrpcParam @Valid @ValidListStringToLong HistoricalScaleMonthlyAvgReq req) {
        req.setYearMonth(IndustryCockpitV3Constant.MONTHLY_AVG_CHANG);
        
        return industryCockpitV3ReportService.getHistoricalScaleMonthlyTable(req, IndustryCockpitV3Constant.MONTHLY_AVG_CHANG);
    }

    /**
     * 获取行业大盘历史规模月度变化表数据-月末切片规模
     *
     * @param req HistoricalScaleMonthlySliceReq
     * @return HistoricalScaleMonthlyTableResp
     */
    @RequestMapping
    @AuthCheck
    public HistoricalScaleMonthlyTableResp getHistoricalScaleMonthlySliceTable(@JsonrpcParam @Valid @ValidListStringToLong HistoricalScaleMonthlySliceReq req) {
        req.setYearMonth(IndustryCockpitV3Constant.MONTHLY_SLICE_SCALE);
        
        return industryCockpitV3ReportService.getHistoricalScaleMonthlyTable(req, IndustryCockpitV3Constant.MONTHLY_SLICE_SCALE);
    }

    /**
     * 获取行业大盘历史规模月度变化表数据-月均切片规模
     *
     * @param req HistoricalScaleMonthlySliceReq
     * @return HistoricalScaleMonthlyTableResp
     */
    @RequestMapping
    @AuthCheck
    public HistoricalScaleMonthlyTableResp getHistoricalScaleMonthlyAvgTable(@JsonrpcParam @Valid @ValidListStringToLong HistoricalScaleMonthlyAvgReq req) {
        req.setYearMonth(IndustryCockpitV3Constant.MONTHLY_AVG_SCALE);
        
        return industryCockpitV3ReportService.getHistoricalScaleMonthlyTable(req, IndustryCockpitV3Constant.MONTHLY_AVG_SCALE);
    }

    /////////////////////////////////////历史规模月度趋势图//////////////////////////////////////////////

    /**
     * 获取行业大盘历史规模月度变化趋势图数据-月切变化量
     *
     * @param req HistoricalScaleMonthlySliceReq
     * @return HistoricalScaleMonthlyHistogramResp
     */
    @RequestMapping
    @AuthCheck
    public HistoricalScaleMonthlyHistogramResp getHistoricalScaleMonthlySliceChangeHistogram(@JsonrpcParam @Valid @ValidListStringToLong HistoricalScaleMonthlySliceReq req) {
        
        return industryCockpitV3ReportService.getHistoricalScaleMonthlyHistogram(req, IndustryCockpitV3Constant.MONTHLY_SLICE_CHANG);
    }

    /**
     * 获取行业大盘历史规模月度变化趋势图数据-月切变化量(for 规格)
     *
     * @param req HistoricalScaleMonthlySliceReq
     * @return HistoricalScaleMonthlyHistogramResp
     */
    @RequestMapping
    @AuthCheck
    public HistoricalScaleMonthlyHistogramResp getHistoricalScaleMonthlySliceChangeHistogramForSpecifications(@JsonrpcParam @Valid @ValidListStringToLong HistoricalScaleMonthlySliceReq req) {
        
        return industryCockpitV3ReportService.getHistoricalScaleMonthlyHistogramForSpecifications(req, IndustryCockpitV3Constant.MONTHLY_SLICE_CHANG);
    }

    /**
     * 获取行业大盘历史规模月度变化趋势图数据-月均变化量
     *
     * @param req HistoricalScaleMonthlyAvgReq
     * @return HistoricalScaleMonthlyHistogramResp
     */
    @RequestMapping
    @AuthCheck
    public HistoricalScaleMonthlyHistogramResp getHistoricalScaleMonthlyAvgChangeHistogram(@JsonrpcParam @Valid @ValidListStringToLong HistoricalScaleMonthlyAvgReq req) {
        
        return industryCockpitV3ReportService.getHistoricalScaleMonthlyHistogram(req, IndustryCockpitV3Constant.MONTHLY_AVG_CHANG);
    }

    /**
     * 获取行业大盘历史规模月度变化趋势图数据-月末切片规模
     *
     * @param req HistoricalScaleMonthlySliceReq
     * @return HistoricalScaleMonthlyHistogramResp
     */
    @RequestMapping
    @AuthCheck
    public HistoricalScaleMonthlyHistogramResp getHistoricalScaleMonthlySliceHistogram(@JsonrpcParam @Valid @ValidListStringToLong HistoricalScaleMonthlySliceReq req) {
        
        return industryCockpitV3ReportService.getHistoricalScaleMonthlyHistogram(req, IndustryCockpitV3Constant.MONTHLY_SLICE_SCALE);
    }

    /**
     * 获取行业大盘历史规模月度变化趋势图数据-月末切片规模(for 规格)
     *
     * @param req HistoricalScaleMonthlySliceReq
     * @return HistoricalScaleMonthlyHistogramResp
     */
    @RequestMapping
    @AuthCheck
    public HistoricalScaleMonthlyHistogramResp getHistoricalScaleMonthlySliceHistogramForSpecifications(@JsonrpcParam @Valid @ValidListStringToLong HistoricalScaleMonthlySliceReq req) {
        
        return industryCockpitV3ReportService.getHistoricalScaleMonthlyHistogramForSpecifications(req, IndustryCockpitV3Constant.MONTHLY_SLICE_SCALE);
    }

    /////////////////////////////////////历史规模日规模表格//////////////////////////////////////////////

    /**
     * 获取行业大盘历史规模日变化表数据
     *
     * @param req LineChartReq
     * @return HistoricalScaleDailyChartResp
     */
    @RequestMapping
    @AuthCheck
    public HistoricalScaleDailyChartResp getHistoricalScaleDailyChart(@JsonrpcParam @Valid @ValidListStringToLong DailyLineChartReq req) {
        
        return industryCockpitV3ReportService.getHistoricalScaleDailyChart(req);
    }

    /**
     * 获取行业大盘历史日规模柱状图数据
     *
     * @param req ProfileReq
     * @return HistoricalScaleDailyProfileResp
     */
    @RequestMapping
    @AuthCheck
    public HistoricalScaleDailyHistogramResp getHistoricalScaleDailyProfileData(@JsonrpcParam @Valid @ValidListStringToLong ProfileReq req) {
        
        return industryCockpitV3ReportService.getHistoricalScaleDailyProfileData(req);
    }

    /**
     * 获取短期未来规模预测表数据
     *
     * @param request
     * @return
     */
    @RequestMapping
    @AuthCheck
    public FutureScaleTableResp getShortTermFutureScaleTableData(@JsonrpcParam @Valid @ValidListStringToLong IndustryCockpitV3FutureReq request) {
        //填充时间
        request.fillShortTermDate();
        request.checkReq();
        //request.filterAuth(industryCockpitAuthService.getAuthVO());
        return industryCockpitV3ReportService.getFutureScaleTableData(request);
    }

    @RequestMapping
    @AuthCheck
    public ResponseEntity<InputStreamResource> exportShortTermFutureScaleTableData(@JsonrpcParam @Valid @ValidListStringToLong IndustryCockpitV3FutureReq req) {
        //填充时间
        req.fillShortTermDate();
        req.checkReq();
        return industryCockpitV3ReportService.exportFutureScaleTableData(req, true);
    }

    /**
     * 获取长期未来规模预测表数据
     *
     * @param request
     * @return
     */
    @RequestMapping
    @AuthCheck
    public FutureScaleTableResp getLongTermFutureScaleTableData(@JsonrpcParam @Valid @ValidListStringToLong IndustryCockpitV3FutureReq request) {
        //填充时间
        request.fillLongTermDate();
        request.checkReq();
        return industryCockpitV3ReportService.getLongTermFutureScaleTableData(request);
    }

    @RequestMapping
    @AuthCheck
    public ResponseEntity<InputStreamResource> exportLongTermFutureScaleTableData(@JsonrpcParam @Valid @ValidListStringToLong IndustryCockpitV3FutureReq req) {
        req.fillLongTermDate();
        req.checkReq();
        return industryCockpitV3ReportService.exportLongTermFutureScaleTableData(req);
    }

    /**
     * 获取订单概览表数据
     *
     * @param req
     * @return
     */
    @RequestMapping
    @AuthCheck
    public OrderOverviewListResp getOrderOverviewList(@JsonrpcParam @Valid @ValidListStringToLong OrderOverviewReq req) {
        
        return orderOverviewService.getOrderOverviewList(req);
    }

    @RequestMapping
    @AuthCheck
    public ResponseEntity<InputStreamResource> exportOrderOverviewList(@JsonrpcParam @Valid @ValidListStringToLong OrderOverviewReq req) {
        
        return orderOverviewService.exportOrderOverviewList(req);
    }
}
