package cloud.demand.app.modules.industry_cockpit.v3.service.impl;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.industry_cockpit.v3.constant.IndustryCockpitV3Constant;
import cloud.demand.app.modules.industry_cockpit.v3.entity.DwdTxyScaleDfDO;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.FutureScaleTableReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.HistoricalScaleMonthlyReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.HistoricalScaleMonthlySliceReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.IndustryCockpitV3FutureReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.LineChartReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.ProfileReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.FutureScaleTableResp;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.HistoricalScaleDailyChartResp;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.HistoricalScaleDailyHistogramResp;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.HistoricalScaleMonthlyHistogramResp;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.HistoricalScaleMonthlyTableResp;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.OverviewBillServiceChartResp;
import cloud.demand.app.modules.industry_cockpit.v3.model.resp.OverviewCircularGraphResp;
import cloud.demand.app.modules.industry_cockpit.v3.model.vo.BizTypeVO;
import cloud.demand.app.modules.industry_cockpit.v3.model.vo.DwdTxyScaleDfViewVO;
import cloud.demand.app.modules.industry_cockpit.v3.model.vo.ExportFutureScaleDataVO;
import cloud.demand.app.modules.industry_cockpit.v3.model.vo.ExportFutureScaleVO;
import cloud.demand.app.modules.industry_cockpit.v3.model.vo.FutureScaleMonthlyVO;
import cloud.demand.app.modules.industry_cockpit.v3.model.vo.HistoricalScaleDailyChartVO;
import cloud.demand.app.modules.industry_cockpit.v3.model.vo.HistoricalScaleDailyHistogramVO;
import cloud.demand.app.modules.industry_cockpit.v3.model.vo.HistoricalScaleMonthlyVO;
import cloud.demand.app.modules.industry_cockpit.v3.model.vo.KeyValueItemVO;
import cloud.demand.app.modules.industry_cockpit.v3.service.FutureScaleService;
import cloud.demand.app.modules.industry_cockpit.v3.service.HistoricalScaleMonthlyService;
import cloud.demand.app.modules.industry_cockpit.v3.service.IndustryCockpitV3ReportService;
import cloud.demand.app.modules.p2p.common.P2PInstanceModelParse;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandIndustryWarZoneDictDO;
import cloud.demand.app.modules.sop_return.frame.where.SopWhereBuilder;
import cloud.demand.app.modules.sop_util.process.utils.sql.SimpleSqlBuilder;
import cloud.demand.app.web.model.common.StreamDownloadBean;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import io.vavr.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import yunti.boot.exception.ITException;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/8/14 11:18
 */
@Service
@Slf4j
public class IndustryCockpitV3ReportServiceImpl implements IndustryCockpitV3ReportService {

    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Resource
    private DictService dictService;

    @Resource
    @Qualifier("historicalScaleMonthlyAvgChangeServiceImpl")
    private HistoricalScaleMonthlyService historicalScaleMonthlyAvgChangeService;

    @Resource
    @Qualifier("historicalScaleMonthlySliceChangeServiceImpl")
    private HistoricalScaleMonthlyService historicalScaleMonthlySliceChangeService;

    @Resource
    @Qualifier("historicalScaleMonthlySliceServiceImpl")
    private HistoricalScaleMonthlyService historicalScaleMonthlySliceService;

    @Resource
    @Qualifier("historicalScaleMonthlyAvgServiceImpl")
    private HistoricalScaleMonthlyService historicalScaleMonthlyAvgService;

    @Resource
    private FutureScaleService futureScaleService;

    @Override
    public OverviewBillServiceChartResp getOverviewChartData(LineChartReq req) {
        List<BizTypeVO> ret = getBizTypeVOS(req);
        return OverviewBillServiceChartResp.builder(ret, req.getStartStatTime(), req.getEndStatTime());
    }

    private List<BizTypeVO> getBizTypeVOS(LineChartReq req) {
        SopWhereBuilder sopWhereBuilder = new SopWhereBuilder(req, DwdTxyScaleDfViewVO.class);
        ORMUtils.WhereContent whereContent = sopWhereBuilder.where();

        String sql = ORMUtils.getSql("/sql/industry_cockpit/v3/1_bill_or_service_by_stat_time.sql");

        sql = SimpleSqlBuilder.doReplace(sql, "queryRange", req.getQueryRange());
        sql = SimpleSqlBuilder.doReplace(sql, "unit", req.getUnit());
        sql = SimpleSqlBuilder.doReplace(sql, "where", whereContent.getSql());
        List<BizTypeVO> bizTypeVOList = ckcldStdCrpDBHelper.getRaw(BizTypeVO.class, sql, whereContent.getParams());
        return bizTypeVOList;
    }


    @Override
    public OverviewCircularGraphResp getOverviewCircularGraphData(ProfileReq req) {
        SopWhereBuilder sopWhereBuilder = new SopWhereBuilder(req, DwdTxyScaleDfDO.class);
        ORMUtils.WhereContent whereContent = sopWhereBuilder.where();

        String sql = ORMUtils.getSql("/sql/industry_cockpit/v3/2_bill_or_service_circular_graph.sql");

        sql = SimpleSqlBuilder.doReplace(sql, "queryRange", req.getQueryRange());
        sql = SimpleSqlBuilder.doReplace(sql, "profileType", req.getProfileType());
        sql = SimpleSqlBuilder.doReplace(sql, "unit", req.getUnit());
        sql = SimpleSqlBuilder.doReplace(sql, "where", whereContent.getSql());

        List<KeyValueItemVO> chartItems = ckcldStdCrpDBHelper.getRaw(KeyValueItemVO.class, sql, whereContent.getParams());

        BigDecimal total = chartItems.stream().map(KeyValueItemVO::getValue).reduce(BigDecimal.ZERO, BigDecimal::add);
        return new OverviewCircularGraphResp(chartItems, total.intValue());
    }



    @Override
    public HistoricalScaleMonthlyTableResp getHistoricalScaleMonthlyTable(HistoricalScaleMonthlyReq req, String caliber) {
        List<HistoricalScaleMonthlyVO> ret = getHistoricalScaleMonthlyVOListForView(req, caliber);
        return HistoricalScaleMonthlyTableResp.parse(req, ret, caliber);
    }

    @Override
    public HistoricalScaleMonthlyHistogramResp getHistoricalScaleMonthlyHistogram(HistoricalScaleMonthlyReq req, String caliber) {
        List<HistoricalScaleMonthlyVO> ret = getHistoricalScaleMonthlyVOListForView(req, caliber);
        return HistoricalScaleMonthlyHistogramResp.parse(ret);
    }

    @Override
    public HistoricalScaleMonthlyHistogramResp getHistoricalScaleMonthlyHistogramForSpecifications(HistoricalScaleMonthlyReq req, String caliber) {
        List<HistoricalScaleMonthlyVO> ret = getHistoricalScaleMonthlyVOList(req, caliber);

        List<HistoricalScaleMonthlyVO> newRet = ListUtils.newArrayList();
        ret.stream().collect(Collectors.groupingBy(o -> o.getYearMonth() + "@" + o.getKey(o))).forEach((key, list) -> {
            HistoricalScaleMonthlyVO copy = new HistoricalScaleMonthlyVO();
            BeanUtils.copyProperties(list.get(0), copy);
            copy.setAmount(list.stream().map(HistoricalScaleMonthlyVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            newRet.add(copy);
        });
        return HistoricalScaleMonthlyHistogramResp.parse(newRet);
    }

    @Override
    public HistoricalScaleDailyChartResp getHistoricalScaleDailyChart(LineChartReq req) {
        //日期往前一天
        LocalDate beforeStartStatTime = req.getStartStatTime().plusDays(-1);
        req.setStartStatTime(req.getStartStatTime().plusDays(-1));
        SopWhereBuilder sopWhereBuilder = new SopWhereBuilder(req, DwdTxyScaleDfDO.class);
        ORMUtils.WhereContent whereContent = sopWhereBuilder.where();

        String sql = ORMUtils.getSql("/sql/industry_cockpit/v3/6_bill_or_service_by_change_stat_time.sql");

        sql = SimpleSqlBuilder.doReplace(sql, "unit", req.getUnit());
        sql = SimpleSqlBuilder.doReplace(sql, "queryRange", req.getQueryRange());
        sql = SimpleSqlBuilder.doReplace(sql, "where", whereContent.getSql());

        List<HistoricalScaleDailyChartVO> chartItems = ckcldStdCrpDBHelper.getRaw(HistoricalScaleDailyChartVO.class, sql, whereContent.getParams());
        for (int i = 1; i < chartItems.size(); i++) {
            HistoricalScaleDailyChartVO item = chartItems.get(i);
            HistoricalScaleDailyChartVO beforeItem = chartItems.get(i - 1);
            item.setChangeAmount(item.getCurAmount().subtract(beforeItem.getCurAmount()));
            item.setReturnAmount(item.getChangeAmount().subtract(item.getNewAmount()).abs());
        }
        // 去掉第一条数据
        String before = beforeStartStatTime.format(DateTimeFormatter.ISO_LOCAL_DATE);
        chartItems = chartItems.stream().filter(o -> !o.getStatTime().equals(before)).collect(Collectors.toList());
        return new HistoricalScaleDailyChartResp(chartItems, chartItems.size());
    }

    @Override
    public HistoricalScaleDailyHistogramResp getHistoricalScaleDailyProfileData(ProfileReq req) {
        SopWhereBuilder sopWhereBuilder = new SopWhereBuilder(req, DwdTxyScaleDfDO.class);
        ORMUtils.WhereContent whereContent = sopWhereBuilder.where();

        String sql = ORMUtils.getSql("/sql/industry_cockpit/v3/7_bill_or_service_specifications_histogram.sql");

        sql = SimpleSqlBuilder.doReplace(sql, "queryRange", req.getQueryRange());
        sql = SimpleSqlBuilder.doReplace(sql, "profileType", req.getProfileType());
        sql = SimpleSqlBuilder.doReplace(sql, "unit", req.getUnit());
        sql = SimpleSqlBuilder.doReplace(sql, "where", whereContent.getSql());


        List<IndustryDemandIndustryWarZoneDictDO> customerConfig = dictService.queryEnableIndustryWarZoneCustomerConfig();
        sql = SimpleSqlBuilder.doReplace(sql, "crp_war_zone", req.getCrpWarZoneSelectCase(customerConfig));
        sql = SimpleSqlBuilder.doReplace(sql, "un_customer_short_name", req.getUnCustomerShortNameSelectCase(customerConfig));

        List<HistoricalScaleDailyHistogramVO> profileVOList = ckcldStdCrpDBHelper.getRaw(HistoricalScaleDailyHistogramVO.class, sql, whereContent.getParams());

        return HistoricalScaleDailyHistogramResp.parse(profileVOList);
    }

    @Override
    public FutureScaleTableResp getFutureScaleTableData(IndustryCockpitV3FutureReq request) {
        StopWatch stopWatch = new StopWatch("futureScaleTable");
        //预测版本数据
        stopWatch.start("预测版本数据1");
        FutureScaleTableReq futureScaleTableReq = FutureScaleTableReq.transform(request, false);
        List<FutureScaleMonthlyVO> pplPredictList = futureScaleService.getFutureScaleMonthly(futureScaleTableReq);
        stopWatch.stop();
        //历史数据
        stopWatch.start("预测版本数据2");
        futureScaleTableReq.setVersionInfo(request.getCompareVersion());
        List<FutureScaleMonthlyVO> pplCompareList = futureScaleService.getFutureScaleMonthly(futureScaleTableReq);
        stopWatch.stop();
        stopWatch.start("历史数据");
        HistoricalScaleMonthlySliceReq req = HistoricalScaleMonthlySliceReq.transform(request, false);
        List<HistoricalScaleMonthlyVO> historyList = getHistoricalScaleMonthlyVOListForView(req,IndustryCockpitV3Constant.MONTHLY_SLICE_CHANG);
        stopWatch.stop();
        log.info("futureScaleTableData:{}",stopWatch.prettyPrint());
        return FutureScaleTableResp.builder(historyList, pplPredictList, pplCompareList,request.getPage());
    }

    @Override
    public ResponseEntity<InputStreamResource> exportFutureScaleTableData(IndustryCockpitV3FutureReq request, boolean isShortTerm) {
        String term = isShortTerm ? "13周" : "中长期";
        Map<String, ExportFutureScaleDataVO> dataMap = new HashMap<>();
        List<String> demandTypeList = ListUtils.newArrayList("新增", "弹性", "退回");

        StopWatch stopWatch = new StopWatch("exportFutureScaleTableData");
        for (String demandType : demandTypeList) {
            request.setDemandType(demandType);
            if (!StringUtils.equals("弹性", demandType)) {
                //历史数据
                stopWatch.start("历史数据"+ demandType);
                HistoricalScaleMonthlySliceReq req = HistoricalScaleMonthlySliceReq.transform(request, true);
                List<HistoricalScaleMonthlyVO> historyList = getHistoricalScaleMonthlyVOListForView(req,IndustryCockpitV3Constant.MONTHLY_SLICE_CHANG);
                Function<HistoricalScaleMonthlyVO, String> groupKeyFunction = (item) -> StringUtils.joinWith("@", item.getIndustryDept(), item.getWarZone(),
                        item.getCustomerShortName(), item.getInstanceType(),item.getInstanceFamily(), item.getGpuCardType(),item.getCustomhouseTitle(), item.getRegionName(), item.getZoneName(), item.getYearMonth(), demandType);
                fillData(dataMap, historyList, groupKeyFunction, HistoricalScaleMonthlyVO::getAmount, ExportFutureScaleDataVO::setScaleAmount, demandType);
                stopWatch.stop();
            }
            stopWatch.start("预测版本数据1"+ demandType);
            Function<FutureScaleMonthlyVO, String> groupKeyFunction = (item) -> StringUtils.joinWith("@", item.getIndustryDept(), item.getWarZone(),
                    item.getCustomerShortName(), item.getInstanceType(), item.getInstanceFamily(), item.getGpuCardType(),item.getCustomhouseTitle(), item.getRegionName(), item.getZoneName(), item.getYearMonth(), demandType);
            //预测版本数据
            FutureScaleTableReq futureScaleTableReq = FutureScaleTableReq.transform(request, true);
            List<FutureScaleMonthlyVO> pplPredictList = futureScaleService.getFutureScaleMonthly(futureScaleTableReq);
            fillData(dataMap, pplPredictList, groupKeyFunction, FutureScaleMonthlyVO::getAmount, ExportFutureScaleDataVO::setPredictAmount, demandType);
            stopWatch.stop();
            //比较版本数据
            stopWatch.start("预测版本数据2"+ demandType);
            futureScaleTableReq.setVersionInfo(request.getCompareVersion());
            List<FutureScaleMonthlyVO> pplCompareList = futureScaleService.getFutureScaleMonthly(futureScaleTableReq);
            fillData(dataMap, pplCompareList, groupKeyFunction, FutureScaleMonthlyVO::getAmount, ExportFutureScaleDataVO::setCompareAmount, demandType);
            stopWatch.stop();
        }
        ExportFutureScaleVO exportData = ExportFutureScaleVO.build(dataMap.values().stream().collect(Collectors.toList()), request.getPredictVersion().getVersionCode(), request.getCompareVersion().getVersionCode(), request.getQueryRange());
        log.info("exportFutureScaleTableData:{}",stopWatch.prettyPrint());
        ByteArrayInputStream in;
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            EasyExcel.write(out)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .head(exportData.getHeadList()).sheet("数据")
                    .doWrite(exportData.getDataList());
            in = new ByteArrayInputStream(out.toByteArray());
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ITException("导出excel模版失败");
        }

        String filename = String.format("驾驶舱-未来增量特征-%s预览明细-", term) + DateUtils.format(new Date()) + UUID.randomUUID().toString().substring(0, 8) + ".xlsx";
        return new StreamDownloadBean(filename, in);
    }

    @Override
    public FutureScaleTableResp getLongTermFutureScaleTableData(IndustryCockpitV3FutureReq request) {
        StopWatch stopWatch = new StopWatch("longTermFutureScaleTable");
        //预测版本数据
        stopWatch.start("中长期预测版本数据1");
        FutureScaleTableReq futureScaleTableReq = FutureScaleTableReq.transform(request, false);
        List<FutureScaleMonthlyVO> pplPredictList = futureScaleService.getLongTermFutureScaleMonthly(futureScaleTableReq);
        stopWatch.stop();
        //历史数据t
        stopWatch.start("中长期预测版本数据2");
        futureScaleTableReq.setVersionInfo(request.getCompareVersion());
        futureScaleTableReq.setLongTermVersionCode(request.getCompareLongTermVersionCode());
        List<FutureScaleMonthlyVO> pplCompareList = futureScaleService.getLongTermFutureScaleMonthly(futureScaleTableReq);
        stopWatch.stop();
        stopWatch.start("历史数据");
        HistoricalScaleMonthlySliceReq req = HistoricalScaleMonthlySliceReq.transform(request, false);
        List<HistoricalScaleMonthlyVO> historyList = getHistoricalScaleMonthlyVOListForView(req,IndustryCockpitV3Constant.MONTHLY_SLICE_CHANG);
        stopWatch.stop();
        log.info("futureScaleTableData:{}",stopWatch.prettyPrint());
        return FutureScaleTableResp.builder(historyList, pplPredictList, pplCompareList,request.getPage());
    }

    @Override
    public ResponseEntity<InputStreamResource> exportLongTermFutureScaleTableData(IndustryCockpitV3FutureReq request) {
        String term = "中长期";
        Map<String, ExportFutureScaleDataVO> dataMap = new HashMap<>();
        List<String> demandTypeList = ListUtils.newArrayList("新增", "弹性", "退回");

        StopWatch stopWatch = new StopWatch("exportLongTermFutureScaleTableData");
        for (String demandType : demandTypeList) {
            request.setDemandType(demandType);
            if (!StringUtils.equals("弹性", demandType)) {
                //历史数据
                stopWatch.start("历史数据"+ demandType);
                HistoricalScaleMonthlySliceReq req = HistoricalScaleMonthlySliceReq.transform(request, true);
                List<HistoricalScaleMonthlyVO> historyList = getHistoricalScaleMonthlyVOListForView(req,IndustryCockpitV3Constant.MONTHLY_SLICE_CHANG);
                Function<HistoricalScaleMonthlyVO, String> groupKeyFunction = (item) -> StringUtils.joinWith("@", item.getIndustryDept(), item.getWarZone(),
                        item.getCustomerShortName(), item.getInstanceType(),item.getInstanceFamily(), item.getGpuCardType(), item.getRegionName(), item.getZoneName(), item.getYearMonth(), demandType);
                fillData(dataMap, historyList, groupKeyFunction, HistoricalScaleMonthlyVO::getAmount, ExportFutureScaleDataVO::setScaleAmount, demandType);
                stopWatch.stop();
            }
            stopWatch.start("预测版本数据1"+ demandType);
            Function<FutureScaleMonthlyVO, String> groupKeyFunction = (item) -> StringUtils.joinWith("@", item.getIndustryDept(), item.getWarZone(),
                    item.getCustomerShortName(), item.getInstanceType(), item.getInstanceFamily(), item.getGpuCardType(), item.getRegionName(), item.getZoneName(), item.getYearMonth(), demandType);
            //预测版本数据
            FutureScaleTableReq futureScaleTableReq = FutureScaleTableReq.transform(request, true);
            List<FutureScaleMonthlyVO> pplPredictList = futureScaleService.getLongTermFutureScaleMonthly(futureScaleTableReq);
            fillData(dataMap, pplPredictList, groupKeyFunction, FutureScaleMonthlyVO::getAmount, ExportFutureScaleDataVO::setPredictAmount, demandType);
            stopWatch.stop();
            //比较版本数据
            stopWatch.start("预测版本数据2"+ demandType);
            futureScaleTableReq.setVersionInfo(request.getCompareVersion());
            futureScaleTableReq.setLongTermVersionCode(request.getCompareLongTermVersionCode());
            List<FutureScaleMonthlyVO> pplCompareList = futureScaleService.getLongTermFutureScaleMonthly(futureScaleTableReq);
            fillData(dataMap, pplCompareList, groupKeyFunction, FutureScaleMonthlyVO::getAmount, ExportFutureScaleDataVO::setCompareAmount, demandType);
            stopWatch.stop();
        }
        ExportFutureScaleVO exportData = ExportFutureScaleVO.build(dataMap.values().stream().collect(Collectors.toList()), request.getLongTermVersionCode(), request.getCompareLongTermVersionCode(), request.getQueryRange());
        log.info("exportLongTermFutureScaleTableData:{}",stopWatch.prettyPrint());
        ByteArrayInputStream in;
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            EasyExcel.write(out)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .head(exportData.getHeadList()).sheet("数据")
                    .doWrite(exportData.getDataList());
            in = new ByteArrayInputStream(out.toByteArray());
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ITException("导出excel模版失败");
        }

        String filename = String.format("驾驶舱-未来增量特征-%s预览明细-", term) + DateUtils.format(new Date()) + UUID.randomUUID().toString().substring(0, 8) + ".xlsx";
        return new StreamDownloadBean(filename, in);
    }


    private List<HistoricalScaleMonthlyVO> getHistoricalScaleMonthlyVOList(HistoricalScaleMonthlyReq req, String caliber) {
        List<HistoricalScaleMonthlyVO> ret;

        if (StringUtils.equals(IndustryCockpitV3Constant.MONTHLY_AVG_CHANG, caliber)) {
            req.setStatTime(req.getFirstDayOfMonthList());
            ret = historicalScaleMonthlyAvgChangeService.getHistoricalScaleMonthly(req);
        } else if (StringUtils.equals(IndustryCockpitV3Constant.MONTHLY_SLICE_CHANG, caliber)) {
            req.setStatTime(req.getLastDayOfMonthList());
            ret = historicalScaleMonthlySliceChangeService.getHistoricalScaleMonthly(req);
        } else if (StringUtils.equals(IndustryCockpitV3Constant.MONTHLY_SLICE_SCALE, caliber)) {
            req.setStatTime(req.getLastDayOfMonthList());
            ret = historicalScaleMonthlySliceService.getHistoricalScaleMonthly(req);
        }else if (StringUtils.equals(IndustryCockpitV3Constant.MONTHLY_AVG_SCALE, caliber)) {
            req.setStatTime(req.getFirstDayOfMonthList());
            ret = historicalScaleMonthlyAvgService.getHistoricalScaleMonthly(req);
        } else {
            ret = ListUtils.newArrayList();
        }
        return ret;
    }

    private List<HistoricalScaleMonthlyVO> getHistoricalScaleMonthlyVOListForView(HistoricalScaleMonthlyReq req, String caliber) {
        HistoricalScaleMonthlyService service;
        List<String> statTimeList;
        if (StringUtils.equals(IndustryCockpitV3Constant.MONTHLY_AVG_CHANG, caliber)) {
            statTimeList = req.getFirstDayOfMonthList();
            service = historicalScaleMonthlyAvgChangeService;
        } else if (StringUtils.equals(IndustryCockpitV3Constant.MONTHLY_SLICE_CHANG, caliber)) {
            statTimeList = req.getLastDayOfMonthList();
            service = historicalScaleMonthlySliceChangeService;
        } else if (StringUtils.equals(IndustryCockpitV3Constant.MONTHLY_AVG_SCALE, caliber)) {
            statTimeList = req.getFirstDayOfMonthList();
            service = historicalScaleMonthlyAvgService;
        } else if (StringUtils.equals(IndustryCockpitV3Constant.MONTHLY_SLICE_SCALE, caliber)) {
            statTimeList = req.getLastDayOfMonthList();
            service = historicalScaleMonthlySliceService;
        } else {
            return ListUtils.newArrayList();
        }
        if(ListUtils.isEmpty(req.getStatTime())) {
            req.setStatTime(statTimeList);
        }
        List<HistoricalScaleMonthlyVO> ret = service.getHistoricalScaleMonthlyForView(req);
        return ret;
    }


    /**
     * 根据实例model 返回规格
     *
     * @param instanceModel
     */
    private String getSpecifications(String instanceModel) {
        Tuple2<Integer, Integer> tuple2 = P2PInstanceModelParse.parseInstanceModel(instanceModel);
        return tuple2._1 + "C" + tuple2._2 + "G";
    }

    private <T> void fillData(Map<String, ExportFutureScaleDataVO> dataMap, List<T> list, Function<T, String> keyFunction, Function<T, BigDecimal> amountFunction,
                              BiConsumer<ExportFutureScaleDataVO, BigDecimal> setterFunction, String demandType) {
        for (T t : list) {
            ExportFutureScaleDataVO vo = dataMap.get(keyFunction.apply(t));
            if (Objects.isNull(vo)) {
                vo = new ExportFutureScaleDataVO();
                dataMap.put(keyFunction.apply(t), vo);
                BeanUtils.copyProperties(t, vo);
                vo.setDemandType(demandType);
            }
            setterFunction.accept(vo, amountFunction.apply(t));
        }
    }
}
