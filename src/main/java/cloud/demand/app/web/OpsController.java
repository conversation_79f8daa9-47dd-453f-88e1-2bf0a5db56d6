package cloud.demand.app.web;

import cloud.demand.app.common.exception.WrongWebParameterException;
import cloud.demand.app.common.utils.AlarmRobotUtil;
import cloud.demand.app.common.utils.EnvUtils;
import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.entity.plan.StaticZoneDO;
import cloud.demand.app.entity.rrp.ReportErpServerDiffDetailDO;
import cloud.demand.app.modules.asset_diff_detail.CalAssetDiffService;
import cloud.demand.app.modules.asset_diff_detail.HasOrderNoDiffService;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.common.service.SyncOuterService;
import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.common.web.CommonController.QueryDeviceTypeInfoListReq;
import cloud.demand.app.modules.cvmjxc.service.CalculateService;
import cloud.demand.app.modules.cvmjxc.service.cmongo.CalculateCMongoIndicatorService;
import cloud.demand.app.modules.end_to_end_report.service.EndToEndReportGenDataService;
import cloud.demand.app.modules.erp_transfer_return.service.CvmApplyReturnDetailService;
import cloud.demand.app.modules.erp_transfer_return.service.ErpDetailService;
import cloud.demand.app.modules.erp_transfer_return.service.ReturnPlanService;
import cloud.demand.app.modules.erp_transfer_return.service.ReturnReportService;
import cloud.demand.app.modules.industry_report.service.IndustryReportGenDataService;
import cloud.demand.app.modules.mrpv3.task.sync.SyncIndustryWarZoneTask;
import cloud.demand.app.modules.operation_view.inventory_health.entity.DwsNewTurnoverInventoryDataDfDO;
import cloud.demand.app.modules.operation_view.inventory_health.service.ForecastViewService;
import cloud.demand.app.modules.operation_view.inventory_health.service.SyncYunxiaoRubikGridService;
import cloud.demand.app.modules.operation_view.operation_view2.entity.DwsApiSuccessDataDfLocalDO;
import cloud.demand.app.modules.operation_view.operation_view2.entity.DwsSoldOutDataDfDO;
import cloud.demand.app.modules.operation_view.operation_view2.service.InstanceModelManualConfigService;
import cloud.demand.app.modules.operation_view.operation_view2.service.InventoryHealthGenService;
import cloud.demand.app.modules.operation_view.operation_view2.service.ManualConfigService;
import cloud.demand.app.modules.operation_view.operation_view2.service.ThresholdTransferService;
import cloud.demand.app.modules.operation_view.operation_view2.service.impl.InventoryHealthGenServiceImpl;
import cloud.demand.app.modules.order.entity.OrderInfoDO;
import cloud.demand.app.modules.order.enums.OrderAvailableStatusEnum;
import cloud.demand.app.modules.order.enums.OrderStatusEnum;
import cloud.demand.app.modules.order.job.OrderCommonTask;
import cloud.demand.app.modules.order.service.OrderCommonOperateService;
import cloud.demand.app.modules.order.service.OrderCommonService;
import cloud.demand.app.modules.order.service.OrderOperateService;
import cloud.demand.app.modules.order.service.OrderSatisfyRateService;
import cloud.demand.app.modules.order.service.PerformanceTrackService;
import cloud.demand.app.modules.order.service.PreDeductOrderService;
import cloud.demand.app.modules.order.service.SupplyPlanOperateService;
import cloud.demand.app.modules.order.service.SupplyPlanQueryService;
import cloud.demand.app.modules.order.service.impl.SupplyPlanOperateServiceImpl;
import cloud.demand.app.modules.p2p.industry_demand.dto.FileNameAndBytesDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.QueryDemandInfoReq;
import cloud.demand.app.modules.p2p.industry_demand.service.AtpIndustryDataService;
import cloud.demand.app.modules.p2p.industry_demand.service.IndustryDemandDictService;
import cloud.demand.app.modules.p2p.industry_demand.service.IndustryDemandInputGroupService;
import cloud.demand.app.modules.p2p.industry_demand.service.IndustryDemandService;
import cloud.demand.app.modules.p2p.ppl13week.dto.InitProcessItemReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.consensus.PplSupplyConsensusReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.QueryPplDraftReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.RefreshGpuGapReq;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderDraftQueryTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.job.SendMailTask;
import cloud.demand.app.modules.p2p.ppl13week.service.PplApplyService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplCommonService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplConsensusService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDraftService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplIndustryPackageBaseDataService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplIndustryProcessService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplInnerProcessService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplInnerVersionService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplStdTableService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplStockSupplyService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplVersionGroupService;
import cloud.demand.app.modules.plan_detail.service.PlanDetailCommonService;
import cloud.demand.app.modules.plan_detail.service.PlanDetailService;
import cloud.demand.app.modules.repo_show.service.DecisionViewService;
import cloud.demand.app.modules.repo_show.service.QueryRepoShowService;
import cloud.demand.app.modules.repo_show.service.ThirdPartService;
import cloud.demand.app.modules.soe.model.req.OperationViewReq;
import cloud.demand.app.modules.soe.service.SoeCommonService;
import cloud.demand.app.modules.tencent_cloud_utils.AccountUtil;
import cloud.demand.app.modules.xy_purchase_order.service.PurchaseOrderDetailService;
import cloud.demand.app.web.model.common.DownloadBean;
import cloud.demand.app.web.model.common.StreamDownloadBean;
import cloud.demand.app.web.model.demand.req.AddProductVersionReq;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.ImmutableMap;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.wooutils.cache.HiSpeedCacheContext;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.net.NetUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.string.StringTools;
import java.io.PipedInputStream;
import java.io.PipedOutputStream;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import javax.annotation.Resource;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 内部运维接口（含提供给脚本箱的定时任务）
 */
@Slf4j
@RestController
@RequestMapping("/ops")
public class OpsController {

    @Resource
    ThirdPartService thirdPartService;
    @Resource
    QueryRepoShowService queryRepoShowService;
    @Resource
    HasOrderNoDiffService hasOrderNoDiffService;
    @Resource
    IndustryDemandService industryDemandService;
    @Resource
    PplCommonService pplCommonService;
    @Resource
    PplDictService pplDictService;
    @Resource
    ManualConfigService manualConfigService;

    @Resource
    OrderCommonService orderCommonService;
    @Resource
    SendMailTask sendMailTask;
    @Resource
    private PurchaseOrderDetailService purchaseOrderDetailService;
    @Resource
    private ErpDetailService erpDetailService;
    @Autowired
    private PlanDetailService planDetailService;
    @Autowired
    private CalculateService calculateService;
    @Autowired
    private PlanDetailCommonService planDetailCommonService;
    @Resource
    private ReturnPlanService returnPlanService;
    @Resource
    private ReturnReportService returnReportService;
    @Resource
    private CvmApplyReturnDetailService cvmApplyReturnDetailService;
    @Resource
    private CalAssetDiffService calAssetDiffService;
    @Resource
    private CalculateCMongoIndicatorService cMongoIndicatorService;
    @Resource
    private DictService dictService;
    @Resource
    private PplApplyService pplApplyService;
    @Resource
    private DecisionViewService decisionViewService;
    @Resource
    private IndustryDemandInputGroupService industryDemandInputGroupService;
    @Resource
    private IndustryDemandDictService industryDemandDictService;
    @Resource
    private IndustryReportGenDataService industryReportGenDataService;
    @Resource
    private EndToEndReportGenDataService endToEndReportGenDataService;
    @Resource
    private ForecastViewService forecastViewService;
    @Autowired
    private InventoryHealthGenService inventoryHealthGenService;
    @Resource
    private InstanceModelManualConfigService instanceModelManualConfigService;
    @Resource
    private PplStdTableService pplStdTableService;
    @Resource
    private ThresholdTransferService thresholdTransferService;
    @Resource
    private PplStockSupplyService pplStockSupplyService;
    @Resource
    private SyncYunxiaoRubikGridService syncYunxiaoRubikGridService;
    @Resource
    private PplConsensusService pplConsensusService;
    @Resource
    private PplInnerProcessService pplInnerProcessService;
    @Resource
    private PplDraftService pplDraftService;
    @Resource
    private PplVersionGroupService pplVersionGroupService;
    @Resource
    private PplIndustryProcessService industryProcessService;
    @Resource
    private IndustryReportGenDataService genDataService;
    @Resource
    private OrderCommonTask orderCommonTask;
    @Resource
    private OrderOperateService orderOperateService;
    @Resource
    private SoeCommonService soeCommonService;
    @Resource
    private PreDeductOrderService preDeductOrderService;
    @Resource
    private OrderCommonOperateService orderCommonOperateService;
    @Resource
    private SupplyPlanOperateService supplyPlanOperateService;
    @Resource
    private OrderSatisfyRateService orderSatisfyRateService;
    @Resource
    private PplIndustryPackageBaseDataService pplIndustryPackageBaseDataService;
    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private DBHelper ckcldDBHelper;

    @Resource
    private DBHelper cdCommonDbHelper;
    @Resource
    PplInnerVersionService pplInnerVersionService;

    @Resource
    SupplyPlanQueryService supplyPlanQueryService;

    @Resource
    private SyncIndustryWarZoneTask syncIndustryWarZoneTask;

    @Resource
    private PerformanceTrackService performanceTrackService;

    @Resource
    private SyncOuterService syncOuterService;

    @Resource
    private AtpIndustryDataService atpIndustryDataService;

    @Resource
    private PplVersionGroupService versionGroupService;

    @GetMapping("/excelSpeedTest")
    @SneakyThrows
    public ResponseEntity<InputStreamResource> excelSpeedTest() {
        PipedInputStream input = new PipedInputStream();
        PipedOutputStream output = new PipedOutputStream();
        output.connect(input);
        CompletableFuture.runAsync(() -> {
            try {
                // 先写
                for (int i = 0; i < 1024 * 5 + 1; i++) { // 8k
                    output.write("A".getBytes(StandardCharsets.UTF_8));
                }
                // 隔一会再写
                Thread.sleep(10 * 1000);
                for (int i = 0; i < 1024 * 4; i++) {
                    output.write("B".getBytes(StandardCharsets.UTF_8));
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            } finally {
                try {
                    output.close();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        });
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/octet-stream");

        return new StreamDownloadBean("test", input, headers);
    }

    @GetMapping("/genIndustryReportData")
    public String genIndustryReportData(String ym) {
        genDataService.genBillingScaleData(ym);
        genDataService.syncData2ClickHouse2NewCk(ym + "-01");
        return "done";
    }

    @GetMapping("/list")
    public List<String> list() {
        List<String> interfaceList = new ArrayList<>();
        // 获取OpsController类中所有的@GetMapping接口路径，并添加到interfaceList中
        Method[] methods = OpsController.class.getDeclaredMethods();
        for (Method method : methods) {
            GetMapping getMapping = method.getAnnotation(GetMapping.class);
            if (getMapping != null) {
                String[] values = getMapping.value();
                if (values.length > 0) {
                    String path = values[0];
                    interfaceList.add(path);
                }
            }
        }
        ListUtils.sortAscNullLast(interfaceList, Function.identity());
        return interfaceList;
    }

    @GetMapping("/getEnvStage")
    public String getEnvStage() {
        return EnvUtils.getStage();
    }

    @GetMapping("/ppl13_total_amount")
    public String updatePplTotalAmount() {
        return pplCommonService.updateTotalAmount();
    }

    // 仅测试用，上线后删除
    @GetMapping("/export_demand")
    public DownloadBean exportDemandInfoExcel(Long groupId) {
        if (groupId == null) {
            groupId = 2L;
        }

        QueryDemandInfoReq req = new QueryDemandInfoReq();
        req.setGroupId(groupId);

        FileNameAndBytesDTO fileNameAndBytesDTO = industryDemandService.exportDemandDetail(req);
        return new DownloadBean(fileNameAndBytesDTO.getFileName(), fileNameAndBytesDTO.getBytes());
    }

    @GetMapping("/")
    public String index() {
        try {
            List<String> ipv4IPs = NetUtils.getIpv4IPs();
            return String.format("ok(ip:%s)", String.join(";", ipv4IPs));
        } catch (Exception e) {
            return String.format("error(msg:%s)", e.getMessage());
        }
    }

    @GetMapping("/getcampus2zone")
    public String getcampus2zone() {
        return dictService.getZoneIdByCampusName().toString();
    }

    @GetMapping("/getmodule2zone")
    public String getmodule2zone() {
        return dictService.getZoneIdByModuleName().toString();
    }

    @Synchronized(waitLockMillisecond = 1000)
    @GetMapping("/runNewJxc")
    public String genNewJxcData(String startDate, String endDate) {
        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            calculateService.genNewJxcData(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }

        return "done";
    }

    @Synchronized(waitLockMillisecond = 1000)
    @GetMapping("/runJxc")
    public String runJxc(String startDate, String endDate) {
        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            calculateService.genJxcData(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }

        return "done";
    }

    @Synchronized(waitLockMillisecond = 1000)
    @GetMapping("/runJxc2")
    public String runJxc2(String startDate, String endDate) {
        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            calculateService.genJxcData(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }

        return "done";
    }

    @Synchronized(waitLockMillisecond = 1000)
    @GetMapping("/runJxc3")
    public String runJxc3(String startDate, String endDate) {
        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            calculateService.genJxcData(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }

        return "done";
    }

    @Synchronized(waitLockMillisecond = 1000)
    @GetMapping("/runJxc4")
    public String runJxc4(String startDate, String endDate) {
        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            calculateService.genJxcData(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }

        return "done";
    }

    @Synchronized(waitLockMillisecond = 1000)
    @GetMapping("/runJxc5")
    public String runJxc5(String startDate, String endDate) {
        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            calculateService.genJxcData(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }

        return "done";
    }

    @Synchronized(waitLockMillisecond = 1000)
    @GetMapping("/parseRZIDictInstanceModel")
    public String parseRZIDictInstanceModel() {
        industryDemandDictService.parseRZIDictInstanceModel();
        return "over";
    }

    @Synchronized(waitLockMillisecond = 1000)
    @GetMapping("/genPurchaseData")
    @TaskLog(taskName = "genPurchaseData")
    public String genPurchaseData(String startDate, String endDate) {
        log.info("url begin genPurchaseData");

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            start = DateUtils.parse(DateUtils.formatDate(DateUtils.addTime(new Date(), Calendar.DATE, -1)));
            end = DateUtils.parse(DateUtils.formatDate(DateUtils.addTime(new Date(), Calendar.DATE, -1)));
        }

        while (!start.after(end)) {

            String statTime = DateUtils.formatDate(start);

            purchaseOrderDetailService.genPurchaseData(statTime);
            purchaseOrderDetailService.genPurchaseROrderData(statTime);
            purchaseOrderDetailService.writeDataToClickhouse(statTime); // 计算的是昨天的数据

            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }

        log.info("url end genPurchaseData");
        return "done";
    }

    @GetMapping("/genReuseData")
    @Synchronized(waitLockMillisecond = 1000)
    @TaskLog(taskName = "genReuseData")
    public String genReuseData(String startDate, String endDate) {
        log.info("url begin genReuseData");

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            start = DateUtils.parse(DateUtils.formatDate(DateUtils.addTime(new Date(), Calendar.DATE, -1)));
            end = DateUtils.parse(DateUtils.formatDate(DateUtils.addTime(new Date(), Calendar.DATE, -1)));
        }

        while (!start.after(end)) {
            String statTime = DateUtils.formatDate(start);
            purchaseOrderDetailService.genPurchaseROrderData(statTime);
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }

        log.info("url end genReuseData");
        return "done";
    }

    @Synchronized(waitLockMillisecond = 1000)
    @GetMapping("/genTransferDetail")
    public String genTransferDetail(String statDateStr, String selectStartDate, String selectEndDate) {
        log.info("url begin genTransferDetail");
        erpDetailService.genTransferDetail(statDateStr, selectStartDate, selectEndDate);
        log.info("url end genTransferDetail");
        return "done";
    }

    @Synchronized(waitLockMillisecond = 1000)
    @GetMapping("/genReturnDetail")
    public String genReturnDetail() {
        log.info("url begin genReturnDetail");
        erpDetailService.genReturnDetail();
        log.info("url end genReturnDetail");
        return "done";
    }

    @Synchronized(waitLockMillisecond = 1000)
    @GetMapping("/runPlanDetail")
    public String runPlanDetail(String startDate, String endDate) {

        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            planDetailService.genAllPlanDetailData(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }
        return "done";
    }

    @Synchronized(waitLockMillisecond = 1000)
    @GetMapping("/runPlanDetail2")
    public String runPlanDetail2(String startDate, String endDate) {

        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            planDetailService.genAllPlanDetailData(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }
        return "done";
    }

    @Synchronized(waitLockMillisecond = 1000)
    @GetMapping("/runPlanDetail3")
    public String runPlanDetail3(String startDate, String endDate) {

        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            planDetailService.genAllPlanDetailData(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }
        return "done";
    }

    @Synchronized(waitLockMillisecond = 1000)
    @GetMapping("/runPlanDetail4")
    public String runPlanDetail4(String startDate, String endDate) {

        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            planDetailService.genAllPlanDetailData(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }
        return "done";
    }

    @Synchronized(waitLockMillisecond = 1000)
    @GetMapping("/runPlanDetail5")
    public String runPlanDetail5(String startDate, String endDate) {

        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            planDetailService.genAllPlanDetailData(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }
        return "done";
    }

    @Synchronized(waitLockMillisecond = 1000)
    @GetMapping("/runAssetDiff")
    public String runAssetDiff(String startDate, String endDate) {

        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            //  比较固资切片
            calAssetDiffService.calAndSaveAssetDiffData(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }
        return "done";
    }

    @Synchronized(waitLockMillisecond = 1000)
    @GetMapping("/runAssetDiff2")
    public String runAssetDiff2(String startDate, String endDate) {

        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            //  有单据，库存无变化补充
            hasOrderNoDiffService.manualGenDiff(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }
        return "done";
    }

    // cvm退回明细
    @Synchronized(waitLockMillisecond = 100)
    @GetMapping("/genCvmReturnDetail")
    public String genCvmReturnDetail() {
        cvmApplyReturnDetailService.genCvmReturnDetail();
        return "done";
    }

    // cvm申领明细
    @Synchronized(waitLockMillisecond = 100)
    @GetMapping("/genCvmApplyDetail")
    public String genCvmApplyDetail() {
        cvmApplyReturnDetailService.genCvmApplyDetail();
        return "done";
    }

    // 退回计划（物理机和cvm） nickxie
    @Synchronized(waitLockMillisecond = 1000)
    @GetMapping("/returnPlanDetail")
    public String returnPlanDetail(String startDate, String endDate) {
        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            returnPlanService.genReturnPlanDetail(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }
        return "done";
    }

    @Synchronized(waitLockMillisecond = 1000)
    @GetMapping("/mergeReturnDataToClickhouse")
    public String mergeReturnDataToClickhouse(String startDate, String endDate) {
        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            returnReportService.mergeDataToClickhouse(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }
        return "done";
    }

    @Synchronized(waitLockMillisecond = 1000)
    @GetMapping("/copyPlanDetailToClickhouse")
    public String copyPlanDetailToClickhouse(String startDate, String endDate) {

        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            planDetailService.copyPlanDetailToClickhouse(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }
        return "done";
    }

    @GetMapping("/diff")
    public String getLogicCoreDiff() {
        planDetailCommonService.diffLogicCore();
        return "done";
    }

//    @Synchronized(waitLockMillisecond = 1000)
//    @GetMapping("/runBigReport")
//    public String runBigReport(String version) {
//        genBigResourceReportTask.createAndSaveBigResourceReport(version);
//        return "done";
//    }

    @GetMapping
    public List<ReportErpServerDiffDetailDO> getAllDiffDetails(String statTime, String productType) {
        if (DateUtils.parse(statTime) == null) {
            return null;
        }
        return calAssetDiffService.getAllDiffData(statTime, productType);
    }

    @Synchronized(waitLockMillisecond = 1000)
    @GetMapping("/cmongoSnap")
    public String cmongoSnap() {
        cMongoIndicatorService.genCmongoBaseSnapshotData();
        return "done";
    }

    @GetMapping("/getAndSaveNetworkInvData")
    public String getAndSaveNetworkInvData(String startDate, String endDate) {

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            thirdPartService.getAndSaveNetworkInvData(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }
        return "done";

    }

    @GetMapping("/genInvCostData")
    public String genInvCostData(String startDate, String endDate) {

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            queryRepoShowService.genNetworkInvCostInfo(DateUtils.formatDate(start));
            queryRepoShowService.genCompanyPriceInfo(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }
        return "done";
    }

    @GetMapping("/syncYunxiaoTask")
    public String syncYunxiaoTask() {
        pplApplyService.reverseSyncYunxiaoOrder(false);
        return "done";
    }

    @GetMapping("syncForceRefreshYunxiaoTask")
    public String syncForceRefreshYunxiaoTask() {
        pplApplyService.reverseSyncYunxiaoOrder(true);
        return "done";
    }

    @GetMapping("/sendPplMeddleMail")
    public String sendPplMeddleMail() {
        sendMailTask.sendPplMeddleMail();
        return "done";
    }

    @GetMapping("genCompanyInventory")
    public String genCompanyInventory() {
        decisionViewService.genCompanyInventory();
        return "done";
    }

    @GetMapping("/reinitInputGroup")
    public String reinitInputGroup(String demandVersion) {
        if (StringTools.isBlank(demandVersion)) {
            return "missing demandVersion";
        }
        industryDemandInputGroupService.initInputGroupForVersion(demandVersion);
        return "done";
    }

//    @Synchronized(waitLockMillisecond = 1000)
//    @GetMapping("/runOperationViewDetail")
//    public String runOperationViewDetail(String startDate, String endDate) {
//        Date start = DateUtils.parse(startDate);
//        Date end = DateUtils.parse(endDate);
//        if (start == null || end == null) {
//            return "startDate或endDate日期格式错误";
//        }
//
//        while (!start.after(end)) {
//            operationViewGenDataService.genAllDetailData(DateUtils.formatDate(start));
//            start = DateUtils.addTime(start, Calendar.DATE, 1);
//        }
//        return "done";
//    }

    @GetMapping("/getAppId2Uin")
    public String getAppId2Uin(String appId) {
        if (StringTools.isBlank(appId)) {
            return "missing appId";
        }
        Map<String, String> appId2UinMap =
                AccountUtil.getAppId2UinMap(ListUtils.newList(
                        appId));
        return JSON.toJson(appId2UinMap);
    }

    @Synchronized(waitLockMillisecond = 1000)
    @GetMapping("syncData2ClickHouse2NewCk")
    public String syncData2ClickHouse2NewCk(String yearMonth) {
        industryReportGenDataService.syncData2ClickHouse2NewCk(yearMonth + "-01");
        return "done";
    }

    @Synchronized(waitLockMillisecond = 100)
    @GetMapping("/genEnd2EndData")
    public String genEnd2EndData(Integer year) {
        endToEndReportGenDataService.genDeviceIncreaseData(year);
        endToEndReportGenDataService.genStockReturnDeviceDetail(year);
        return "done";
    }

    @Synchronized(waitLockMillisecond = 100)
    @GetMapping("/genForecastHolidayWeekData")
    public String genForecastHolidayWeekData() {
        forecastViewService.genForecastHolidayWeekData();
        return "done";
    }

    @Synchronized(waitLockMillisecond = 100)
    @GetMapping("/genPurchaseFutureData")
    public String genPurchaseFutureData() {
        forecastViewService.genPurchaseFutureData();
        return "done";
    }

    @Synchronized(waitLockMillisecond = 100)
    @GetMapping("/genFutureForecastData")
    public String genFutureForecastData() {
        forecastViewService.genFutureForecastData();
        return "done";
    }

    @PostMapping("/queryDeviceTypeInfoList")
    public Object queryDeviceTypeInfoList(@RequestBody QueryDeviceTypeInfoListReq req) {
        return dictService.queryDeviceTypeInfoList(req.getPageNum(), req.getPageSize(), req.getDefaultFlag());
    }

    @GetMapping("/syncPplItemLatestToCkStdTable")
    public String syncPplItemLatestToCkStdTable() {
        pplStdTableService.syncPplItemLatestToCkStdTable();
        return "ok";
    }

    @GetMapping("/syncPplItemVersionToCkStdTable")
    public String syncPplItemVersionToCkStdTable() {
        pplStdTableService.syncPplItemVersionToCkStdTable();
        return "ok";
    }

    @GetMapping("/syncOrderItemAndInfoToCkStdTable")
    public String syncOrderItemAndInfoToCkStdTable() {
        pplStdTableService.syncOrderItemAndInfoToCkStdTable();
        return "ok";
    }

    @GetMapping("/syncPplYunxiaoApplyToCkStdTable")
    public String syncPplYunxiaoApplyToCkStdTable() {
        pplStdTableService.syncPplYunxiaoApplyToCkStdTable();
        return "ok";
    }

    @GetMapping("/syncLatestPplItemVersionBaseToCkStdTable")
    public String syncLatestPplItemVersionBaseToCkStdTable() {
        pplStdTableService.syncLatestPplItemVersionBaseToCkStdTable();
        return "ok";
    }

    @GetMapping("/syncLatestPplItemVersion532ToCkStdTable")
    public String syncLatestPplItemVersion532ToCkStdTable() {
        pplStdTableService.syncLatestPplItemVersion532ToCkStdTable();
        return "ok";
    }

    @GetMapping("/syncYunxiaoApplyOrderDetailToToCkStdTable")
    public String syncYunxiaoApplyOrderDetailToToCkStdTable(String endYearMonth, Integer months, Boolean forceRefresh) {
        YearMonth end = null;
        if (endYearMonth != null) {
            end = YearMonth.parse(endYearMonth, DatePattern.SIMPLE_MONTH_FORMATTER);
        }
        pplStdTableService.syncYunxiaoApplyOrderDetailToToCkStdTable(end, months, forceRefresh != null && forceRefresh);
        return "ok";
    }

    @GetMapping("/syncPplItemVersionNewestFromStdSwapToCkStdTable")
    public String syncPplItemVersionNewestFromStdSwapToCkStdTable() {
        pplStdTableService.syncPplItemVersionNewestFromStdSwapToCkStdTable();
        return "ok";
    }

    @GetMapping("/syncPplItemVersionJoinOrderLatestToCkStdTable")
    public String syncPplItemVersionJoinOrderLatestToCkStdTable() {
        pplStdTableService.syncPplItemVersionJoinOrderLatestToCkStdTable();
        return "ok";
    }

    @GetMapping("/syncConsensusOrderToCkStdTable")
    public String syncConsensusOrderToCkStdTable() {
        pplStdTableService.syncConsensusOrderToCkStdTable();
        return "ok";
    }

    @GetMapping("/refreshHistoryJoinTable")
    public String refreshHistoryJoinTable() {
        pplStdTableService.refreshHistoryJoinTable();
        return "ok";
    }

    @GetMapping("genSafetyInventoryData")
    @Synchronized(waitLockMillisecond = 1000)
    public String genSafetyInventoryData(String startDate, String endDate) {
        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            inventoryHealthGenService.genSafetyInventoryDetail(DateUtils.formatDate(start), Lang.list(), Lang.list(),
                    null);
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }
        return "done";
    }

    /**
     * 生成历史周峰需求数据，以及过去 12/13周的平均值。周维度，每天刷新昨天所在周的数据
     *
     * @param startDate
     * @param endDate
     * @return
     */
    @GetMapping("genSafetyInventoryDataHistoryPeak")
    @Synchronized(waitLockMillisecond = 1000)
    public String genSafetyInventoryDataHistoryPeak(String startDate, String endDate) {
        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            inventoryHealthGenService.genSafetyInventoryDataHistoryPeak(DateUtils.formatDate(start), Lang.list(),
                    Lang.list(),
                    null);
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }
        return "done";
    }

    /**
     * 生成安全库存供应汇总表
     *
     * @param startDate
     * @param endDate
     * @return
     */
    @GetMapping("genSupplySummaryData")
    @Synchronized(waitLockMillisecond = 1000)
    public String genSupplySummaryData(String startDate, String endDate) {
        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            inventoryHealthGenService.genSupplySummaryData(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }

        return "done";
    }

    @GetMapping("genDeliveryDaysData")
    @Synchronized(waitLockMillisecond = 1000)
    public String genDeliveryDaysData(String startDate, String endDate) {
        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            inventoryHealthGenService.genDeliveryDaysData(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }
        return "done";
    }


    @GetMapping("genWeekNForecastData")
    @Synchronized(waitLockMillisecond = 1000)
    public String genWeekNForecastData(String startDate, String endDate) {
        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            inventoryHealthGenService.genWeekNForecastData(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }
        return "done";
    }

    @GetMapping("genMckTurnoverInventoryData")
    @Synchronized(waitLockMillisecond = 1000)
    public String genMckTurnoverInventoryData(String startDate, String endDate) {
        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            inventoryHealthGenService.genMckTurnoverInventoryData(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 7);
        }
        return "done";
    }

    @GetMapping("genMckForecastTurnoverInventoryData")
    @Synchronized(waitLockMillisecond = 1000)
    public String genMckForecastTurnoverInventoryData(String startDate, String endDate) {
        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            inventoryHealthGenService.genMckForecastTurnoverInventoryData(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }
        return "done";
    }

    @GetMapping("genMckForecastSafeInventoryData")
    @Synchronized(waitLockMillisecond = 1000)
    public String genMckForecastSafeInventoryData(String startDate, String endDate) {
        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            inventoryHealthGenService.genMckForecastSafeInventoryData(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }
        return "done";
    }

    @GetMapping("snapshotHeadZlkhbData")
    @Synchronized(waitLockMillisecond = 1000)
    public String snapshotHeadZlkhbData(String startDate, String endDate) {
        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            inventoryHealthGenService.snapshotHeadZlkhbData(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }
        return "done";
    }

    @GetMapping("genMonthlySafeInventoryData")
    @Synchronized(waitLockMillisecond = 1000)
    public Object genMonthlySafeInventoryData(String startDate, String endDate) {
        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            inventoryHealthGenService.genMonthlySafeInventoryData(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }
        return "done";
    }

    @GetMapping("genMckMonthlySafeInventoryData")
    @Synchronized(waitLockMillisecond = 1000)
    public Object genMckMonthlySafeInventoryData(String startDate, String endDate) {
        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            inventoryHealthGenService.genMckMonthlySafeInventoryData(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }
        return "done";
    }

    @GetMapping("genBufferSafeInventoryData")
    @Synchronized(waitLockMillisecond = 1000)
    public Object genBufferSafeInventoryData(String startDate, String endDate) {
        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            inventoryHealthGenService.genBufferSafeInventoryData(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }
        return "done";
    }

    @GetMapping("genActualInventoryData")
    @Synchronized(waitLockMillisecond = 1000)
    public Object genActualInventoryData(String startDate, String endDate) {
        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }

        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }

        while (!start.after(end)) {
            inventoryHealthGenService.genActualInventoryData(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }
        return "done";
    }

    @GetMapping("syncYunxiaoMainData")
    @Synchronized(waitLockMillisecond = 100)
    public String syncYunxiaoMainData() {
        pplDictService.syncYunxiaoMainData();
        return "done";
    }

    @GetMapping("snapshotThresholdConfig")
    @Synchronized(waitLockMillisecond = 100)
    public void snapshotThresholdConfig() {
        thresholdTransferService.snapshotThresholdConfig();
    }

    @GetMapping("/getCampus2ZoneInfoMap")
    public String getCampus2ZoneInfoMap() {
        return JSON.toJson(dictService.getCampus2ZoneInfoMap());
    }

    @GetMapping("/getZoneNameByCampusAndBizType")
    public String getZoneNameByCampusAndBizType(String campusName) {
        return dictService.getZoneNameByCampusAndBizType(campusName);
    }

    @GetMapping("getCampus2ZoneNameMap")
    public String getCampus2ZoneNameMap() {
        Map<String, String> transMap =
                MapUtils.transform(dictService.getCampus2ZoneInfoMap(), o -> o.getZoneName());
        return JSON.toJson(transMap);
    }

    @GetMapping("/campusCheck")
    public String campusCheck() {
        try {
            pplStockSupplyService.campusCheck();
        } catch (Exception e) {
            return e.getClass() + ":" + e.getMessage();
        }
        return "ok";
    }

    @PostMapping("/createSupplyPlan")
    public String createSupplyPlan(@RequestBody PplSupplyConsensusReq req) {
        try {
            pplConsensusService.createSupplyPlan(req);
        } catch (Exception e) {
            return e.getClass() + ":" + e.getMessage();
        }
        return "ok";
    }

    @GetMapping("/autoStartConsensus")
    public String autoStartConsensus(String versionCode, String industryDept, String product) {
        pplConsensusService.autoStartConsensus(versionCode, industryDept, product);
        return "ok";
    }

    @GetMapping("/syncYunxiaoRubikGridData")
    public String syncYunxiaoRubikGridData(String date) {
        if (date == null || date.isEmpty()) {
            throw new WrongWebParameterException("参数date不能为空");
        }

        syncYunxiaoRubikGridService.syncYunxiaoRubikGridData(date);

        return "ok";
    }

    @GetMapping("/snapshotInventoryHealthManualConfig")
    public String snapshotInventoryHealthManualConfig(String date) {
        if (date == null || date.isEmpty()) {
            throw new WrongWebParameterException("参数date不能为空");
        }

        manualConfigService.snapshotManualConfig(date);

        return "ok";
    }

    @GetMapping("/snapshotInventoryHealthInstanceModelManualConfig")
    public String snapshotInventoryHealthInstanceModelManualConfig(String date) {
        if (date == null || date.isEmpty()) {
            throw new WrongWebParameterException("参数date不能为空");
        }

        instanceModelManualConfigService.snapshotManualConfig(date);

        return "ok";
    }

    @PostMapping("/initDraftData")
    public String initDraftData(@RequestBody InitDataVo req) {
        pplInnerProcessService.initData(req);
        return "ok";
    }

    @PostMapping("/queryIndustryDemandDetail")
    Object queryIndustryDemandDetail(@RequestBody QueryPplDraftReq req) {
        PageData pageData = new PageData();
        req.setUsername("oliverychen");
        if (PplOrderDraftQueryTypeEnum.DRAFT.getCode().equals(req.getStatus())) {
            pageData.setData(pplDraftService.queryDraftData(req));
        } else if (PplOrderDraftQueryTypeEnum.PRE_SUBMIT.getCode().equals(req.getStatus())) {
            pageData.setData(pplDraftService.queryDraftData(req));
        } else if (PplOrderDraftQueryTypeEnum.PRE_SUBMIT_BY_LD.getCode().equals(req.getStatus())) {
            pageData.setData(pplDraftService.queryPreSubmitDraftData(req));
        } else if (PplOrderDraftQueryTypeEnum.IN_PROGRESS.getCode().equals(req.getStatus())) {
            pageData.setData(pplDraftService.queryPendingData(req));
        } else if (PplOrderDraftQueryTypeEnum.VALID.getCode().equals(req.getStatus())) {
            pageData.setData(pplDraftService.queryValidData(req));
        }

        return pageData;
    }

    @GetMapping("/refreshYunxiaoOrderCreateTime")
    public String refreshYunxiaoOrderCreateTime() {
        pplApplyService.refreshYunxiaoOrderCreateTime();
        return "ok";
    }

    @PostMapping("/refreshGpuGap")
    public String refreshGpuGap(@RequestBody RefreshGpuGapReq req) {
        return pplVersionGroupService.refreshGpuGap(req.getVersionCode(), req.getIndustryDept(), req.getProduct());
    }

    @PostMapping("/initProcessItem")
    public void initProcessItem(@RequestBody InitProcessItemReq req) {
        industryProcessService.initProcessItem(req);
    }

    @GetMapping("/correctWarZone")
    public String correctWarZone(String pplOrder, String warZone, Long versionId) {
        pplInnerProcessService.correctWarZone(pplOrder, warZone, versionId);
        return "OK";
    }

    @GetMapping("/pushToOrderFollowing")
    public String pushToOrderFollowing() {
        orderCommonTask.pushToOrderFollowing();
        return "OK";
    }

    @GetMapping("/pushToOrderClose")
    public String pushToOrderClose() {
        orderCommonTask.pushToOrderClose();
        return "OK";
    }

    @GetMapping("/syncOrderItemsLateConsensusBeginBuyDate")
    public String syncOrderItemsLateConsensusBeginBuyDate(String orderNumber) {
        if (Strings.isBlank(orderNumber)) {
            orderCommonOperateService.syncOrderItemsLateConsensusBeginBuyDate();
        } else {
            orderOperateService.syncOrderItemsLateConsensusBeginBuyDate(orderNumber);
        }
        return "ok";
    }

    @GetMapping("/autoPreDeduct")
    public String autoPreDeduct(String orderNumber) {
        preDeductOrderService.autoPreDeduct(orderNumber);
        return "ok";
    }

    @GetMapping("/preDeductDelayForOrderDelay")
    public String preDeductDelayForOrderDelay(String orderNumber, Long delayFlowId) {
        preDeductOrderService.preDeductDelayForOrderDelay(orderNumber, delayFlowId);
        return "ok";
    }

    @GetMapping("/preDeductDeadlineNotice")
    public String preDeductDeadlineNotice(Integer days) {
        preDeductOrderService.preDeductDeadlineNotice(days == null ? 3 : days);
        return "ok";
    }

    /**
     * 补偿订单满足度计算
     */
    @GetMapping("/compensateOrderSatisfactionCalc")
    public String compensateOrderSatisfactionCalc() {
        orderSatisfyRateService.compensateOrderSatisfactionCalc();
        return "ok";
    }

    @GetMapping("/orderSatisfactionCalc")
    public String orderSatisfactionCalc(String statDate, String endDate) {
        LocalDate calcDate;
        if (StringTools.isBlank(statDate)) {
            calcDate = LocalDate.now();
        } else {
            try {
                calcDate = LocalDate.parse(statDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } catch (Exception e) {
                calcDate = LocalDate.parse(statDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
            }
        }
        LocalDate end = calcDate;
        if (StringTools.isNotBlank(endDate)) {
            try {
                end = LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } catch (Exception e) {
                end = LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
            }
        }
        if (end.isBefore(calcDate)) {
            end = calcDate;
        }
        while (!calcDate.isAfter(end)) {
            orderSatisfyRateService.orderSatisfactionCalc(calcDate, 14);
            calcDate = calcDate.plusDays(1);
        }
        return "ok";
    }

    /**
     * 累计满足量的计算更新
     */
    @GetMapping("/orderCumulativeDeductCal")
    public String orderCumulativeDeductCal(String startDate, String endDate) {
        LocalDate calcDate;
        if (StringTools.isBlank(startDate)) {
            calcDate = LocalDate.now();
        } else {
            try {
                calcDate = LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } catch (Exception e) {
                calcDate = LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
            }
        }
        LocalDate end = calcDate;
        if (StringTools.isNotBlank(endDate)) {
            try {
                end = LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } catch (Exception e) {
                end = LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
            }
        }
        if (end.isBefore(calcDate)) {
            end = calcDate;
        }
        while (!calcDate.isAfter(end)) {
            orderSatisfyRateService.orderCumulativeDeductCal(calcDate);
            calcDate = calcDate.plusDays(1);
        }
        return "ok";
    }

    @GetMapping("/calcGpuMatchRate")
    public String calcGpuMatchRate(String minBeginBuyDate) {
        orderSatisfyRateService.calcGpuMatchRate(LocalDate.parse(minBeginBuyDate));
        return "ok";
    }

    @GetMapping("/calcGpuSatisfyOneOrder")
    public String calcGpuSatisfyOneOrder(String orderNumber) {
        orderSatisfyRateService.calcGpuSatisfyOneOrder(orderNumber);
        return "ok";
    }

    @GetMapping("/refreshSatisfyForNotSatisfyMatchOneOrder")
    public String refreshSatisfyForNotSatisfyMatchOneOrder(String orderNumber) {
        orderSatisfyRateService.refreshSatisfyForNotSatisfyMatchOneOrder(orderNumber);
        return "ok";
    }

    /**
     *  运维计算应当有满足度数据却没有非大盘满足满足度信息的订单，生成非大盘满足的供应方案明细的满足度信息。<br/>
     *  当 orderNumber 非空时，只计算指定的 orderNumber
     */
    @GetMapping("/calcOrderForNotSatisfyMatch")
    public String calcOrderForNotSatisfyMatch(String orderNumber) {
        orderSatisfyRateService.opsCalcForNotSatisfyMatch(orderNumber);
        return "ok";
    }

    @GetMapping("/completePreDeduct")
    public String completePreDeduct() {
        preDeductOrderService.completePreDeduct();
        return "ok";
    }

    /**
     *  计算出订单的方案明细满足度信息
     */
    @GetMapping("/calcSupplyPlanDetailMatchCore")
    public String calcSupplyPlanDetailMatchCore(String orderNumber) {
        orderSatisfyRateService.calcSupplyPlanDetailMatchCore(orderNumber);
        return "ok";
    }

    /**
     * 对漏掉计算供应方案明细满足度的订单，计算出方案明细满足度信息
     */
    @GetMapping("/calcSupplyPlanDetailMatchCoreForNoData")
    public String calcSupplyPlanDetailMatchCoreForNoData() {
        orderCommonTask.calcSupplyPlanDetailMatchCoreForNoData();
        return "ok";
    }

    @GetMapping("/preSubmitDraftForStartNewVersion")
    public String preSubmitDraftForStartNewVersion(Long versionId) {
        pplDraftService.preSubmitDraftForStartNewVersion(versionId);
        return "ok";
    }

    @GetMapping("/syncSimpleOrderServiceLevelSummary")
    public String syncSimpleOrderServiceLevelSummary() {
        orderSatisfyRateService.syncSimpleOrderServiceLevelSummary();
        return "ok";
    }

    @GetMapping("/industrySatisfyGapAlert")
    public String industrySatisfyGapAlert() {
        orderSatisfyRateService.industrySatisfyGapAlert();
        return "ok";
    }

    @GetMapping("/globalSatisfyGapAlert")
    public String globalSatisfyGapAlert() {
        orderSatisfyRateService.globalSatisfyGapAlert();
        return "ok";
    }

    /**
     * 对漏掉计算供应方案明细满足度的订单，计算出方案明细满足度信息
     */
    @GetMapping("/supplyInfoSync")
    public String supplyInfoSync(String orderStatus, String orderNodeCode, String orderNumber) {
        if (Strings.isBlank(orderStatus)) {
            orderStatus = OrderStatusEnum.PROCESS.getCode();
        }
        WhereContent where = new WhereContent()
                .andEqual(OrderInfoDO::getOrderStatus, orderStatus)
                .andEqual(OrderInfoDO::getAvailableStatus, OrderAvailableStatusEnum.AVAILABLE.getCode())
                .andEqualIfValueNotEmpty(OrderInfoDO::getOrderNodeCode, orderNodeCode)
                .andEqualIfValueNotEmpty(OrderInfoDO::getOrderNumber, orderNumber);
        List<OrderInfoDO> list = demandDBHelper.getAll(OrderInfoDO.class, where.getSql(), where.getParams());
        if (ListUtils.isEmpty(list)) {
            return "ok";
        }
        for (OrderInfoDO order : list) {
            try {
                supplyPlanOperateService.supplyProgressAndHandler(order.getOrderNumber(), null);
            } catch (Exception e) {
                // 告警通知
                String msg = "订单【{}】同步供应相关信息异常【{}】";
                AlarmRobotUtil.doAlarm("ops-supplyInfoSync",
                        StrUtil.format(msg, order.getOrderNumber(), ExceptionUtil.getMessage(e)),
                        null, false);
            }
        }
        return "ok";
    }

    @GetMapping("/orderAuditOverTime")
    public String orderAuditOverTime() {
        orderCommonTask.orderAuditOverTime();
        return "ok";
    }

    @GetMapping("/opsNextVersionValidPplForApply")
    public String opsNextVersionValidPplForApply(String pplOrder) {
        industryProcessService.opsNextVersionValidPplForApply(pplOrder);
        return "ok";
    }


    @PostMapping("/addProductVersion")
    public String addProductVersion(@RequestBody AddProductVersionReq req) {
        pplInnerVersionService.addProductVersion(req.getProcessId(), req.getProduct());
        return "ok";
    }

    @GetMapping("/genConsensusDemandBySupplyPlan")
     public String genConsensusDemandBySupplyPlan(String orderNumber) {
        supplyPlanOperateService.genOrderConsensusBySupplyDetail(orderNumber);
        return "ok";
     }

     @GetMapping("/genFullConsensusDemand")
     @Synchronized(waitLockMillisecond = 10)
     public String genFullConsensusDemand() {
        //获取所有生效的供应明细的订单
         List<String> orderNumbers = demandDBHelper.getRaw(String.class,
                 "select distinct order_number from order_supply_plan_version where status = 'available' and deleted = 0");
         for (String orderNumber : orderNumbers) {
             try{
                 supplyPlanOperateService.genOrderConsensusBySupplyDetail(orderNumber);
             }catch (Exception e) {
                 //告警处理
                 String msg = "订单【{}】生成共识需求明细异常【{}】";
                 AlarmRobotUtil.doAlarm("ops-genFullConsensusDemand",
                         StrUtil.format(msg, orderNumber, ExceptionUtil.getMessage(e)),
                         null, false);
             }
         }
         return "ok";

     }

    @GetMapping("/addAllOrderSatisfyInfo")
    public String addAllOrderSatisfyInfo() {
        List<String> orderNumbers = demandDBHelper.getRaw(String.class,
                "select distinct order_number from order_supply_plan_version where status = 'available' and deleted = 0");
        for (String orderNumber : orderNumbers) {
            try{
                orderSatisfyRateService.updateOrderConsensusByNumChange(orderNumber);
            }catch (Exception e) {
                //告警处理
                String msg = "订单【{}】更新满足量异常【{}】";
                AlarmRobotUtil.doAlarm("ops-addAllOrderSatisfyInfo",
                        StrUtil.format(msg, orderNumber, ExceptionUtil.getMessage(e)),
                        null, false);
            }
        }
        return "ok";
    }
    @GetMapping("/genFullConsensusDemandByOrder")
    @Synchronized(waitLockMillisecond = 10)
     public String genFullConsensusDemandByOrder() {
        //获取所有生效的供应明细的订单
        List<String> supplyOrder = demandDBHelper.getRaw(String.class,
                "select distinct order_number from order_supply_plan_version where status = 'available' and deleted = 0");
        HashSet<String> orderSet = new HashSet<>(supplyOrder);

        //获取所有有效的订单号
        List<String> fullOrder = demandDBHelper.getRaw(String.class,
                "select distinct order_number from order_info where deleted = 0 and available_status = 'available' and order_status = 'PROCESS'");

        List<StaticZoneDO> zoneAll = cdCommonDbHelper.getAll(StaticZoneDO.class);
        Map<String, StaticZoneDO> zoneMap = ListUtils.toMap(zoneAll, StaticZoneDO::getZoneName, o -> o);
        SupplyPlanOperateServiceImpl impl = SpringUtil.getBean(SupplyPlanOperateServiceImpl.class);

        List<String> noSupplyOrder = new ArrayList<>();
        for (String order : fullOrder) {
            if (!orderSet.contains(order)) {
                noSupplyOrder.add(order);
            }
        }
        for (String order : noSupplyOrder) {
            try{
                impl.genFullConsensusDemandByOrder(order, zoneMap);
            }catch (Exception e) {
                //告警处理
                String msg = "订单【{}】生成共识需求明细异常【{}】";
                AlarmRobotUtil.doAlarm("ops-genFullConsensusDemandByOrder",
                        StrUtil.format(msg, order, ExceptionUtil.getMessage(e)),
                        null, false);
            }
        }

        return "ok";
    }

     @GetMapping("/addOrderSatisfyInfo")
     public String addOrderSatisfyInfo(String orderNumber) {
        orderSatisfyRateService.updateOrderConsensusByNumChange(orderNumber);
        return "ok";
     }

    @Data
    @ToString
    public static class InitDataVo {

        private String beginYearMonth;

        //endYearMonth 是需要选到后一个月的 如想要 8月份数据  beginYearMonth = 2023-08 endYearMonth = 2023-09
        private String endYearMonth;
        private String industryDept;
        private String product;

    }

    @PostMapping("/testActualSafeInv")
    public Object testActualSafeInv(@RequestBody OperationViewReq req, String algorithm) {
        HiSpeedCacheContext.disableOnce();
        return soeCommonService.getActualAndSafeInv(req, algorithm);
    }

    @GetMapping("/genServiceLevelData")
    public String genServiceLevelData(String startDate, String endDate) {
        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }
        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }
        while (!start.after(end)) {
            String statTime = DateUtils.formatDate(start);
            long unix = start.getTime() / 1000;
            inventoryHealthGenService.genServiceLevelSoldData(String.valueOf(unix), statTime);
            inventoryHealthGenService.genServiceLevelApiSuccessData(String.valueOf(unix), statTime);
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }
        return "ok";
    }

    @GetMapping("/genEsServiceLevelData")
    public String genEsServiceLevelData(String startDate, String endDate) {
        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }
        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }
        InventoryHealthGenServiceImpl impl = SpringUtil.getBean(InventoryHealthGenServiceImpl.class);
        while(!start.after(end)) {
            List<DwsApiSuccessDataDfLocalDO> esApiSuccessData = impl.getESApiSuccessData(DateUtils.formatDate(start));
            List<DwsSoldOutDataDfDO> esSoldOutData = impl.getESSoldOutData(DateUtils.formatDate(start));
            List<DwsApiSuccessDataDfLocalDO> tcApiSuccessData = impl.getTCApiSuccessData(DateUtils.formatDate(start));
            ckcldDBHelper.insert(esApiSuccessData);
            ckcldDBHelper.insert(esSoldOutData);
            ckcldDBHelper.insert(tcApiSuccessData);
            start = DateUtils.addTime(start,Calendar.DATE, 1);
        }
        return "ok";
    }

    @GetMapping("/genNewTurnoverInventoryLog")
    public String genNewTurnoverInventoryLog(String startDate, String endDate) {
        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }
        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }
        while(!start.after(end)) {
            inventoryHealthGenService.getNewTurnoverInventoryLog(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }
        return "ok";
    }


    @GetMapping("/genNewTurnoverInventoryData")
    public String genNewTurnoverInventoryData(String startDate, String endDate) {
        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }
        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }
        while(!start.after(end)) {
            inventoryHealthGenService.getNewTurnoverInventoryData(DateUtils.formatDate(start));
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }
        return "ok";
    }

    @GetMapping("/addPreDeductInventoryInfo")
    public String addPreDeductInventoryInfo(String startDate, String endDate) {
        if (StringTools.isBlank(startDate) || StringTools.isBlank(endDate)) {
            return "缺少参数:startDate和endDate";
        }
        Date start = DateUtils.parse(startDate);
        Date end = DateUtils.parse(endDate);
        if (start == null || end == null) {
            return "startDate或endDate日期格式错误";
        }
        Map<String, StaticZoneDO> zoneMap = dictService.getAllPlanZoneInfosGroupByName();
        while(!start.after(end)) {
            String statTime = DateUtils.formatDate(start);
            List<DwsNewTurnoverInventoryDataDfDO> all = ckcldDBHelper.getAll(
                    DwsNewTurnoverInventoryDataDfDO.class, "where stat_time = ?", statTime);
            ckcldDBHelper.executeRaw("ALTER TABLE dws_new_turnover_inventory_data_df_local ON CLUSTER default_cluster DROP PARTITION ?",
                    statTime);
            for (DwsNewTurnoverInventoryDataDfDO item : all) {
                StaticZoneDO staticZoneDO = zoneMap.get(item.getZoneName());
                if (staticZoneDO != null) {
                    item.setCustomhouseTitle(staticZoneDO.getCustomhouseTitle());
                    item.setAreaName(staticZoneDO.getAreaName());
                    item.setRegionName(staticZoneDO.getRegionName());
                }
            }
            ckcldDBHelper.insertBatchWithoutReturnId(all);
            start = DateUtils.addTime(start, Calendar.DATE, 1);
        }
        return "ok";
    }

    @GetMapping("/genTCHouseCustomerData")
    public String genTCHouseCustomerData() {
        LocalDate yesterday = DateUtils.yesterday();
        inventoryHealthGenService.getTCHouseCustomerData(DateUtils.formatDate(yesterday));
        return "ok";
    }

    @GetMapping("/genEKSApiSuccessData")
    @Synchronized(waitLockMillisecond = 10)
    public String genEKSApiSuccessData(String start, String end) {
        LocalDate startDate = DateUtils.parseLocalDate(start);
        LocalDate endDate = DateUtils.parseLocalDate(end);
        if (startDate == null || endDate == null) {
            return "startDate或endDate日期格式错误";
        }
        InventoryHealthGenServiceImpl bean = SpringUtil.getBean(InventoryHealthGenServiceImpl.class);
        while(!startDate.isAfter(endDate)) {
            List<DwsApiSuccessDataDfLocalDO> eksApiSuccessData = bean.getEKSApiSuccessData(
                    DateUtils.formatDate(startDate));
            ckcldDBHelper.insert(eksApiSuccessData);
            startDate = startDate.plusDays(1);
        }
        return "ok";
    }


    @GetMapping("/changeInsForESData")
    public String changeInsForESData(String start, String end) {
        LocalDate startDate = DateUtils.parseLocalDate(start);
        LocalDate endDate = DateUtils.parseLocalDate(end);
        if (startDate == null || endDate == null) {
            return "startDate或endDate日期格式错误";
        }
        InventoryHealthGenServiceImpl bean = SpringUtil.getBean(InventoryHealthGenServiceImpl.class);
        LocalDate cur = startDate;
        while(!cur.isAfter(endDate)) {
            String statTime = DateUtils.formatDate(cur);
            List<DwsApiSuccessDataDfLocalDO> ESData = bean.getESApiSuccessData(DateUtils.formatDate(cur));
            List<DwsApiSuccessDataDfLocalDO> TCData = bean.getTCApiSuccessData(DateUtils.formatDate(cur));
            Map<String, String> ESMap = ListUtils.toMap(ESData, DwsApiSuccessDataDfLocalDO::getInstanceType,
                    DwsApiSuccessDataDfLocalDO::getInstanceFamily);
            Map<String, String> TCMap = ListUtils.toMap(TCData, DwsApiSuccessDataDfLocalDO::getInstanceType,
                    DwsApiSuccessDataDfLocalDO::getInstanceFamily);
            List<DwsApiSuccessDataDfLocalDO> all = ckcldDBHelper.getAll(DwsApiSuccessDataDfLocalDO.class,
                    "where stat_time = ?", statTime);
            ckcldDBHelper.executeRaw("ALTER TABLE dwd_api_success_data_df_local ON CLUSTER default_cluster DROP PARTITION ?",
                    statTime);
            for (DwsApiSuccessDataDfLocalDO item : all) {
                if (item.getProductType().equals("ES")) {
                    item.setInstanceFamily(ESMap.get(item.getInstanceType()));
                } else if (item.getProductType().equals("TC")) {
                    item.setInstanceFamily(TCMap.get(item.getInstanceType()));
                }
            }
            if (ListUtils.isNotEmpty(all)) {
                ckcldDBHelper.insertBatchWithoutReturnId(all);
            }
            cur = cur.plusDays(1);
        }
        return "ok";
    }

    @GetMapping("/syncIndustryWarZoneTask")
    public String syncIndustryWarZoneTask(){
        syncIndustryWarZoneTask.sync();
        return "ok";
    }

    @GetMapping("/refreshConsensusDemandFollow")
    public String refreshConsensusDemandFollow(String date) {
        LocalDate minEndBuyDate = null;
        if (Strings.isNotBlank(date)) {
            minEndBuyDate = DateUtils.parseLocalDate(date);
        }
        performanceTrackService.refreshConsensusDemandFollow(minEndBuyDate);
        return "ok";
    }

    @GetMapping("/refreshCycleElasticConsensusDemandFollow")
    public String refreshCycleElasticConsensusDemandFollow(String date) {
        LocalDate minEndBuyDate = null;
        if (Strings.isNotBlank(date)) {
            minEndBuyDate = DateUtils.parseLocalDate(date);
        }
        performanceTrackService.refreshCycleElasticConsensusDemandFollow(minEndBuyDate);
        return "ok";
    }

    @PostMapping("/refreshCycleElasticConsensusDemandFollowByOrders")
    public String refreshCycleElasticConsensusDemandFollowByOrders(@RequestBody List<String> orderNumberList) {
        performanceTrackService.refreshCycleElasticConsensusDemandFollowByOrders(orderNumberList);
        return "ok";
    }

    @PostMapping("/refreshCycleElasticConsensusDemandFollowBySql")
    public String refreshCycleElasticConsensusDemandFollowBySql(@RequestBody String orderNumberListSql)  {
        List<String> orderNumberList = demandDBHelper.getRaw(String.class, orderNumberListSql);
        performanceTrackService.refreshCycleElasticConsensusDemandFollowByOrders(orderNumberList);
        return "ok";
    }

    @GetMapping("/calcElasticOrderSatisfyOneOrder")
    public String calcElasticOrderSatisfyOneOrder(String orderNumber) {
        orderSatisfyRateService.calcElasticOrderSatisfyOneOrder(orderNumber);
        return "ok";
    }

    @GetMapping("/calcElasticOrderSatisfy")
    public String calcElasticOrderSatisfy(String minDate, String maxDate)  {
        LocalDate begin = null;
        if (Strings.isNotBlank(minDate)) {
            begin = DateUtils.parseLocalDate(minDate);
        } else {
            begin = LocalDate.now().minusDays(7);
        }
        LocalDate end = null;
        if (Strings.isNotBlank(maxDate)) {
            end = DateUtils.parseLocalDate(maxDate);
        } else {
            end = LocalDate.now().plusDays(7);
        }
        orderSatisfyRateService.calcElasticOrderSatisfy(begin, end);
        return "ok";
    }

    @PostMapping("/calcElasticOrderSatisfyBySql")
    public String calcElasticOrderSatisfyBySql(@RequestBody String orderNumberListSql)  {
        List<String> orderNumberList = demandDBHelper.getRaw(String.class, orderNumberListSql);
        orderSatisfyRateService.calcElasticOrderSatisfyForOrderNumbers(orderNumberList);
        return "ok";
    }

    @GetMapping("/refreshSatisfyForNotSatisfyMatch")
    public String refreshSatisfyForNotSatisfyMatch(Boolean includeCloseOrder) {
        orderSatisfyRateService.refreshSatisfyForNotSatisfyMatch(includeCloseOrder != null && includeCloseOrder);
        return "ok";
    }

    @GetMapping("/opsOrderForError2_21")
    public String opsOrderForError2_21(String orderNumber) {
        if (Strings.isNotBlank(orderNumber)) {
            orderSatisfyRateService.opsOrderForError2_21(orderNumber);
            return "ok";
        }
        String sql = "select DISTINCT order_number  from order_item_satisfy_rate "
                + " where deleted  = 0 and main_result  = 1 and total_system_match_core  != 0 and total_match_core = 0 "
                + " and match_type = 'SATISFY'";
        List<String> orderNumberList = demandDBHelper.getRaw(String.class, sql);
        if (ListUtils.isNotEmpty(orderNumberList)) {
            for (String item : orderNumberList) {
                orderSatisfyRateService.opsOrderForError2_21(item);
            }
        }
        return "ok";
    }

    @GetMapping("/correctRegionByZoneNameForOrderItemSatisfyRate")
    public String correctRegionByZoneNameForOrderItemSatisfyRate() {
        orderSatisfyRateService.correctRegionByZoneNameForOrderItemSatisfyRate();
        return "ok";
    }

    @GetMapping("/autoPreDeductDelay")
    public void autoPreDeductDelay(String date) {
        LocalDate minDate = null;
        if (Strings.isNotBlank(date)) {
            minDate = DateUtils.parseLocalDate(date);
        }
        if (minDate == null) {
            minDate = LocalDate.now().minusDays(30);
        }
        preDeductOrderService.autoPreDeductDelay(minDate);
    }

    @GetMapping("/inheritBaseData")
    public String inheritBaseData(Long newVersionId, Long oldVersionId)  {
        pplIndustryPackageBaseDataService.inheritBaseData(newVersionId, oldVersionId);
        return "ok";
    }

    @GetMapping("/syncIdcRiskZone")
    public String syncIdcRiskZone() {
        syncOuterService.syncIdcRiskZone();
        return "ok";
    }

    @GetMapping("/syncCloudZone")
    public String syncCloudZone() {
        syncOuterService.syncCloudZone();
        return "ok";
    }

    @PostMapping("/opsConsensusBeginEndBuyDate")
    public String opsConsensusBeginEndBuyDate(@RequestBody String sql)  {
        Object res = orderCommonOperateService.opsConsensusBeginEndBuyDate(sql);
        return JSON.toJson(res);
    }

    @GetMapping("/opsConsensusBeginEndBuyDateOnOrder")
    public String opsConsensusBeginEndBuyDateOnOrder(String orderNumber) {
        orderCommonOperateService.opsConsensusBeginEndBuyDateOnOrder(orderNumber);
        return "ok";
    }

    @GetMapping("/synConsensusOrderDate")
    public String synConsensusOrderDate() {
        orderCommonOperateService.synConsensusOrderDate();
        return "ok";
    }

    @GetMapping("/refreshOrderMatchWayProcessor")
    public String refreshOrderMatchWayProcessor() {
        orderCommonOperateService.refreshOrderMatchWayProcessor();
        return "ok";
    }

    @GetMapping("/genConsensusDemandForOrderCancel")
    public String genConsensusDemandForOrderCancel() {
        orderCommonOperateService.genConsensusDemandForOrderCancel();
        return "ok";
    }

    @GetMapping("/syncSurplusInventory")
    public String syncSurplusInventory() {
        atpIndustryDataService.syncSurplusInventory();
        return "ok";
    }


    @GetMapping("/refreshCbsSpike")
    @Synchronized(waitLockMillisecond = 10)
    public String refreshCbsSpike(String versionCode) {
        versionGroupService.refreshCbsSpike(versionCode);
        return "ok";
    }
}
