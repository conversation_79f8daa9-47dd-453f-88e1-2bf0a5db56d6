SELECT
    version_code,
    version_begin_year,
    version_begin_month,
    version_end_year,
    version_end_month,
    demand_type,
    product,
    source,
    year,
    month,
    industry_dept,
    is_comd,
    cbs_is_spike,
    data_disk_type,
    system_disk_type,
    sum(total_core) as total_core,
    sum(total_gpu_num) as total_gpu_num,
    sum(system_disk_storage * instance_num) as system_capacity,
    sum(data_disk_storage * data_disk_num * instance_num) as data_capacity
FROM dwd_crp_ppl_item_version_cf
    ${FILTER} -- 额外的条件
group by
    version_code,
    version_begin_year,
    version_begin_month,
    version_end_year,
    version_end_month,
    demand_type,
    product,
    source,
    year,
    month,
    industry_dept,
    is_comd,
    cbs_is_spike,
    data_disk_type,
    system_disk_type