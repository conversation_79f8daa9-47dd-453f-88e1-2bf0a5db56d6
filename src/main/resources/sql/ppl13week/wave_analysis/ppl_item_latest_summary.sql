SELECT
    version_code,
    demand_type,
    product,
    source,
    year,
    month,
    industry_dept,
    status,
    is_comd,
    system_disk_type,
    data_disk_type,
    cbs_is_spike,
    sum(total_core) as total_core,
    sum(total_gpu_num) as total_gpu_num,
    sum(system_disk_storage * instance_num) as system_capacity,
    sum(data_disk_storage * data_disk_num * instance_num) as data_capacity
FROM dws_crp_ppl_item_version_newest_cf
    ${FILTER} -- 额外的条件
group by
    version_code,
    demand_type,
    product,
    source,
    year,
    month,
    industry_dept,
    status,
    is_comd,
    cbs_is_spike,
    system_disk_type,
    data_disk_type