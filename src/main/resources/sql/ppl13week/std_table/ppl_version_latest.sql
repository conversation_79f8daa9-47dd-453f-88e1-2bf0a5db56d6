SELECT
    c.`version_code`,c.`version_name`,c.`demand_begin_year`,c.`demand_begin_month`,
    c.`demand_end_year`,c.`demand_end_month`,c.`version_type`,c.`status` AS `version_status`,
    c.`note` AS version_note, c.`creator` AS version_creator, c.`start_audit_time`, c.`end_audit_time`,
    c.create_time as version_create_time,
    b.`industry_dept` AS version_group_industry_dept,
    b.`product` AS version_group_product, b.`status` AS version_group_status,

    a.ppl_id,a.ppl_order,a.update_time,a.status,a.instance_num,a.total_core,
    d.source,YEAR(a.begin_buy_date) AS `year`,MONTH(a.begin_buy_date) AS `month`,
    d.industry, d.industry_dept,d.create_time as submit_time,
    d.customer_uin,d.customer_type,d.customer_name,d.customer_short_name,d.war_zone,d.customer_source,
    a.creator,
    a.product,a.demand_type,a.demand_scene,a.project_name,a.bill_type,a.win_rate,
    a.begin_buy_date,a.end_buy_date,a.begin_elastic_date,a.end_elastic_date,
    a.note,a.region_name,a.zone_name,a.instance_type,a.instance_model,
    a.total_disk,a.alternative_instance_type,a.affinity_type,a.affinity_value,
    a.system_disk_type,a.system_disk_storage,a.system_disk_num,
    a.data_disk_type,a.data_disk_storage,a.data_disk_num,
    a.gpu_type,a.gpu_num,a.total_gpu_num,a.is_accept_adjust,a.accept_gpu,a.biz_scene,a.biz_detail,a.service_time,
    a.gpu_product_type,a.is_comd,a.source_ppl_id,a.is_lock,a.is_from_forecast,a.is_forecast_inner,
    a.is_spike,a.cbs_is_spike, a.database_name, a.more_than_one_az, a.database_storage_type, a.framework_type,
    a.slice_num, a.replica_num, a.read_only_num, a.database_specs, a.database_storage,
    a.total_database_storage, a.cos_storage_type, a.cos_az, a.cos_storage, a.bandwidth, a.qps,
    a.total_cos_storage, a.instance_model_core_num, a.instance_model_ram_num, a.total_memory
FROM `ppl_version_group_record_item` a
    JOIN `ppl_version_group` b ON a.version_group_id=b.`id` AND b.`deleted`=0
    JOIN `ppl_version` c ON b.`version_code`=c.`version_code` AND c.`deleted`=0
    JOIN ppl_order d ON a.`ppl_order`=d.`ppl_order` AND d.`deleted`=0
WHERE a.deleted=0 AND version_group_record_id IN (
    SELECT MAX(id) FROM `ppl_version_group_record`
    WHERE deleted=0
  AND version_group_id IN (SELECT id FROM `ppl_version_group` WHERE deleted=0)
    GROUP BY version_group_id
    )
