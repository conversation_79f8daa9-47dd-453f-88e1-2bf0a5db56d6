select
    stat_time,
    if(${has_industry_dept}, industry_dept, null) as any_industry_dept,
    if(${has_war_zone}, crp_war_zone, null) as any_war_zone,
    if(${has_customer_short_name}, un_customer_short_name, null) as any_customer_short_name,
    if(${has_instance_type}, instance_type, NULL) as any_instance_type,
    if(${has_instance_family}, ${instance_family_map}, NULL)  as any_instance_family,
    if(${has_gpu_card_type}, gpu_card_type, null) as any_gpu_card_type,
    if(${has_region_name}, region_name, null) as any_region_name,
    if(${has_zone_name}, zone_name, null) as any_zone_name,
    if(${has_app_id}, app_id, null) as any_app_id,
    if(${has_uin}, uin, null) as any_uin,
    if(${has_instance_model}, instance_model, null) as any_instance_model,
    if(${has_reserve_mode}, reserve_mode, null) as any_reserve_mode,
    if(${has_withhold_duration}, withhold_duration, null) as any_withhold_duration,
    if(${has_withhold_days}, withhold_days, null) as any_withhold_days,
    sum(case when '${unit}' = '卡数' then if(reserve_mode = '普通预扣',gpu_count,0)
         else if(reserve_mode = '普通预扣',cpu_count,0)
        end) as normal_withhold_num,
    sum(case when '${unit}' = '卡数' then if(reserve_mode = '普通预扣' and withhold_duration = 0,gpu_count,0)
             else if(reserve_mode = '普通预扣' and withhold_duration = 0,cpu_count,0)
        end) as normal_withhold_num_le_14,
    sum(case when '${unit}' = '卡数' then if(reserve_mode = '普通预扣' and withhold_duration > 0,gpu_count,0)
             else if(reserve_mode = '普通预扣' and withhold_duration > 0,cpu_count,0)
        end) as normal_withhold_num_gt_14,
    sum(case when '${unit}' = '卡数' then if(reserve_mode = '弹性预扣',gpu_count,0)
             else if(reserve_mode = '弹性预扣',cpu_count,0)
        end) as elasticity_withhold_num,
    sum(case when '${unit}' = '卡数' then gpu_count
             else cpu_count
        end) as total_withhold_num,
    sum(stat_time - create_time) as total_withhold_days,
    avg(stat_time - create_time) as avg_withhold_days
from std_crp.dws_industry_cockpit_v3_withhold_df
${where}
group by stat_time,any_industry_dept, any_war_zone, any_customer_short_name, any_instance_type, any_instance_family, any_gpu_card_type, any_region_name, any_zone_name, any_app_id, any_uin, any_instance_model, any_reserve_mode, any_withhold_duration,any_withhold_days
order by total_withhold_num desc
