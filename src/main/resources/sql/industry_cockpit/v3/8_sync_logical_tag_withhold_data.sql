-- 每日预扣
select
    host.stattime as stat_time ,
    host.biztype as biz_type ,
    zone_info.zone_name as zone_name,
    zone_info.region_name as region_name,
    zone_info.area_name as area_name,
    zone_info.customhouse_title as customhouse_title,
    host.zoneid as zone_id,
    host.host_ip as host_ip,
    cvm.ginsfamily as instance_type,
    host.cvmtype as cvm_type,
    host.stdtype as cvm_standard_type,
    host.app_mask as app_mask ,
    host.bsi_id as bsi_id ,
    host.stocks_label as stocks_label,
    host.cpucore_good as cpu_core_good,
    host.cpucore_bad as cpu_core_bad,
    host.cpucore_ugly as cpu_core_idle,
    host.cpucore_good + host.cpucore_bad +  host.cpucore_ugly as cpu_core_total,
    host.logical_pool as logical_info,
    host.tags as tag_info
from
    metric.daily_host_logicalpoold_tags host
        left join metric.static_cvmtype cvm on
            host.cvmtype = cvm.cvmtype
        left join metric.static_zone zone_info on
            host.zoneid = zone_info.zoneid
where
        host.stattime = ?
  and host.biztype in ('CVM','GPU')
  and ( host.logical_pool like '%【周转】%' or host.tags like '%HOST_RESERVED%')
  and host.cpucore_good + host.cpucore_bad +  host.cpucore_ugly > 0
